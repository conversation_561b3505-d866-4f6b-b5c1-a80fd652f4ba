<?php
/**
 * اقتراح لجعل قائمة الأعمدة ديناميكية بالكامل
 * Suggestion for making column list completely dynamic
 */

// بدلاً من القائمة الثابتة، استخدم هذا:
function getAllColumnsFromDatabase($conn) {
    $columns = [];
    $result = $conn->query("DESCRIBE databasehc");
    
    while ($row = $result->fetch_assoc()) {
        $columns[] = $row['Field'];
    }
    
    return $columns;
}

// أو مع معلومات إضافية:
function getAllColumnsWithDetails($conn) {
    $columns = [];
    $result = $conn->query("DESCRIBE databasehc");
    
    while ($row = $result->fetch_assoc()) {
        $columns[$row['Field']] = [
            'name' => $row['Field'],
            'type' => $row['Type'],
            'null' => $row['Null'],
            'key' => $row['Key'],
            'default' => $row['Default'],
            'extra' => $row['Extra']
        ];
    }
    
    return $columns;
}

// تطبيق في الكود:
// بدلاً من:
// $allColumns = ['ID', 'InterpreterName', 'NameOnCommunity', ...];

// استخدم:
$allColumns = getAllColumnsFromDatabase($conn);

// أو للحصول على تفاصيل أكثر:
$columnsDetails = getAllColumnsWithDetails($conn);
$allColumns = array_keys($columnsDetails);

// مثال على الاستخدام المتقدم:
function getColumnDisplayName($columnName, $columnsDetails) {
    // تحويل أسماء الأعمدة لأسماء أكثر وضوحاً
    $displayNames = [
        'FGEmails' => 'Email Address',
        'InterpreterName' => 'Interpreter Name',
        'SeatLang' => 'Seat Language',
        'Startdate' => 'Start Date'
    ];
    
    return $displayNames[$columnName] ?? ucwords(str_replace('_', ' ', $columnName));
}

// استخدام في HTML:
foreach ($allColumns as $column) {
    $displayName = getColumnDisplayName($column, $columnsDetails);
    echo "<th>$displayName</th>";
}

// فلترة الأعمدة حسب النوع:
function getColumnsByType($columnsDetails, $type) {
    $filtered = [];
    foreach ($columnsDetails as $name => $details) {
        if (strpos(strtolower($details['type']), $type) !== false) {
            $filtered[] = $name;
        }
    }
    return $filtered;
}

// مثال:
$dateColumns = getColumnsByType($columnsDetails, 'date');
$enumColumns = getColumnsByType($columnsDetails, 'enum');
$textColumns = getColumnsByType($columnsDetails, 'text');

// تطبيق معالجة خاصة حسب نوع العمود:
foreach ($allColumns as $column) {
    $columnType = $columnsDetails[$column]['type'];
    
    if (strpos($columnType, 'date') !== false) {
        // معالجة خاصة للتواريخ
        echo "<input type='date' name='$column'>";
    } elseif (strpos($columnType, 'enum') !== false) {
        // معالجة خاصة للـ ENUM
        preg_match_all("/'([^']+)'/", $columnType, $matches);
        echo "<select name='$column'>";
        foreach ($matches[1] as $option) {
            echo "<option value='$option'>$option</option>";
        }
        echo "</select>";
    } else {
        // معالجة عادية
        echo "<input type='text' name='$column'>";
    }
}
?>
