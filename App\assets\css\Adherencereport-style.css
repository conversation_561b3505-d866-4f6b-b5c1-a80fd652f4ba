@import url('root-variables.css');

/* Base Styles */
body {
    background-color: var(--light-color);
    font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
    color: var(--text-dark);
    line-height: 1.6;
}

.container-fluid {
    padding: 2rem;
    perspective: 1000px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    color: var(--accent-color);
    margin-bottom: 1rem;
    font-weight: 700;
}

h1 {
    font-size: 1.8rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.text-primary { color: var(--primary-color) !important; }
.text-secondary { color: var(--secondary-color) !important; }
.text-accent { color: var(--accent-color) !important; }
.text-success { color: var(--success-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-danger { color: var(--danger-color) !important; }

/* Form Container */
.form-container {
    background: var(--white);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 2rem;
    transform-style: preserve-3d;
    transition: var(--transition);
}

.form-container:hover {
    transform: translateY(-5px) rotateX(2deg);
    box-shadow: var(--shadow-lg);
}

/* Table Styles */
.table-container {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 1rem;
    transform-style: preserve-3d;
    transition: var(--transition);
    overflow-x: auto;
}

.table-container:hover {
    transform: translateY(-5px) rotateX(2deg);
    box-shadow: var(--shadow-lg);
}

.table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-bottom: 0;
}

.table th, .table td {
    padding: 1rem;
    border: 1px solid var(--border-color);
    vertical-align: middle;
}

.table thead th {
    background: var(--primary-light);
    color: var(--text-dark);
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: 10;
    border-bottom: 2px solid var(--primary-dark);
}

/* Sticky Email Column */
.table th:first-child,
.table td:first-child {
    position: sticky;
    left: 0;
    background: var(--white);
    z-index: 20;
    box-shadow: 2px 0 5px rgba(0,0,0,0.1);
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: var(--primary-light);
    transform: scale(1.01);
}

/* Form Elements */
.form-control, .form-select {
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    padding: 0.5rem 1rem;
    transition: var(--transition);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(199, 154, 199, 0.25);
}

/* Select2 Customization */
.select2-container--default .select2-selection--single {
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    height: 38px;
    padding: 5px;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 28px;
    color: var(--text-dark);
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: var(--primary-color);
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    padding: 0.5rem 1.5rem;
    transition: var(--transition);
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.btn-secondary {
    background-color: var(--primary-color);
    border-color: var(--primary-dark);
    color: var(--white);
}

.btn-secondary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

/* Hours Bar */
.hours-bar {
    position: relative;
    height: 40px;
    width: 100%;
    display: flex;
    font-size: 12px;
    color: var(--text-medium);
    background: var(--white);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
}

.hour-block {
    flex: 1;
    text-align: center;
    border-left: 1px solid var(--border-color);
    padding: 5px 0;
    background: var(--white);
    transition: var(--transition);
}

.hour-block:hover {
    background: var(--primary-light);
    color: var(--accent-color);
}

/* Bars */
.shift-bar {
    position: absolute;
    height: 20px;
    background-color: var(--primary-color);
    opacity: 0.8;
    border-radius: 4px;
    transition: var(--transition);
}

.shift-bar:hover {
    opacity: 1;
    transform: scale(1.02);
    box-shadow: var(--shadow);
}

.break-bar {
    position: absolute;
    height: 15px;
    background-color: var(--warning-color);
    border-radius: 4px;
    transition: var(--transition);
}

.break-bar:hover {
    transform: scale(1.02);
    box-shadow: var(--shadow);
}

/* Alerts */
.alert {
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    border: none;
    padding: 1.5rem;
    margin: 2rem 0;
    background: var(--white);
    color: var(--text-dark);
}

.alert-light {
    background-color: var(--light-color);
    border-left: 4px solid var(--primary-color);
}

/* Icons */
.bi {
    margin-right: 5px;
    color: var(--accent-color);
}

/* Loading Spinner */
.loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid var(--primary-light);
    border-top-color: var(--accent-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 3D Effects */
.card-effect {
    transform-style: preserve-3d;
    transition: var(--transition);
}

.card-effect:hover {
    transform: translateY(-5px) rotateX(5deg);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container-fluid {
        padding: 1rem;
    }

    .form-container {
        padding: 1rem;
    }

    h1 {
        font-size: 1.5rem;
    }

    .table th, .table td {
        padding: 0.75rem;
    }

    .btn {
        padding: 0.4rem 1rem;
    }
}

/* Print Styles */
@media print {
    .form-container,
    .table-container {
        box-shadow: none;
        transform: none;
    }

    .table th:first-child,
    .table td:first-child {
        position: static;
        box-shadow: none;
    }

    .btn,
    .form-control,
    .form-select {
        border: 1px solid #000;
    }
} 