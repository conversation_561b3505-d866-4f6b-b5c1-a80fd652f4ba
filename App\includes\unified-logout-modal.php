<!-- Unified Logout Modal CSS -->
<link rel="stylesheet" href="/App/assets/css/unified-logout-modal.css">

<!-- Unified Logout Modal -->
<div class="modal unified-logout-modal" id="unifiedLogoutModal" tabindex="-1" aria-labelledby="unifiedLogoutModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="unifiedLogoutModalLabel">
                    <i class="fas fa-sign-out-alt"></i>
                    Confirm Logout
                </h5>
                <button type="button" class="btn-close" onclick="closeUnifiedModal()" aria-label="Close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="logout-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h6>Are you sure you want to log out?</h6>
                <p>You'll need to log in again to access the system. Any unsaved changes will be lost.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn cancel-btn" onclick="closeUnifiedModal()">
                    <i class="fas fa-times"></i>
                    Cancel
                </button>
                <button type="button" class="btn logout-btn" onclick="unifiedLogout()">
                    <i class="fas fa-sign-out-alt"></i>
                    Logout
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Unified Logout JavaScript -->
<script>
// Unified logout functionality
function showUnifiedLogoutModal() {
    console.log('Unified logout modal initiated...');

    const modalElement = document.getElementById('unifiedLogoutModal');

    // Show modal instantly without animation
    modalElement.style.display = 'block';
    modalElement.classList.add('show');
    modalElement.setAttribute('aria-hidden', 'false');

    // Add backdrop
    document.body.style.overflow = 'hidden';
    document.body.classList.add('modal-open');

    // Focus on modal for accessibility
    modalElement.focus();
}

function unifiedLogout() {
    console.log('Unified logout confirmed...');

    // Get modal element
    const modalElement = document.getElementById('unifiedLogoutModal');

    // Close the modal instantly
    closeUnifiedModal();

    // Show loading state
    const loadingModal = document.createElement('div');
    loadingModal.className = 'modal fade unified-logout-modal show';
    loadingModal.style.display = 'block';
    loadingModal.style.background = 'rgba(0,0,0,0.5)';
    loadingModal.style.backdropFilter = 'blur(8px)';
    loadingModal.style.zIndex = '9999';
    loadingModal.innerHTML = `
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="loading-state">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Logging out, please wait...</p>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(loadingModal);

    // Perform logout
    const xhr = new XMLHttpRequest();
    xhr.open("POST", "/App/Logpage/logout.php", true);
    xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
    xhr.setRequestHeader("X-Requested-With", "XMLHttpRequest");
    xhr.timeout = 5000;
    xhr.onreadystatechange = function () {
        if (xhr.readyState === XMLHttpRequest.DONE) {
            console.log('Unified logout request completed. Status:', xhr.status);
            console.log('Response text:', xhr.responseText);

            // Remove loading modal
            if (loadingModal && loadingModal.parentNode) {
                loadingModal.parentNode.removeChild(loadingModal);
            }

            // Always redirect to login page regardless of response
            if (xhr.status === 200) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    console.log('Unified logout response:', response);

                    if (response.status === 'success') {
                        console.log('Unified logout successful, redirecting...');
                    } else {
                        console.warn('Unified logout warning:', response.message);
                    }
                } catch (e) {
                    console.error('Unified logout JSON parse error:', e);
                }
            } else {
                console.error('Unified logout HTTP Error:', xhr.status);
            }

            // Always redirect to login page
            window.location.href = '/Loginpage';
        }
    };

    // Add timeout handler
    xhr.ontimeout = function() {
        console.error('⏰ Unified logout request timed out');
        // Remove loading modal
        if (loadingModal && loadingModal.parentNode) {
            loadingModal.parentNode.removeChild(loadingModal);
        }
        // Still redirect even if timeout
        window.location.href = '/Loginpage';
    };

    // Add error handler
    xhr.onerror = function() {
        console.error('🌐 Network error during unified logout');
        // Remove loading modal
        if (loadingModal && loadingModal.parentNode) {
            loadingModal.parentNode.removeChild(loadingModal);
        }
        // Still redirect even if network error
        window.location.href = '/Loginpage';
    };

    xhr.send("update_logout_time=1");
}

// Alternative function names for compatibility
function showModal() {
    showUnifiedLogoutModal();
}

function logout() {
    unifiedLogout();
}

function closeUnifiedModal() {
    const modalElement = document.getElementById('unifiedLogoutModal');

    // Hide modal instantly
    modalElement.style.display = 'none';
    modalElement.classList.remove('show');
    modalElement.setAttribute('aria-hidden', 'true');

    // Remove backdrop effects
    document.body.style.overflow = '';
    document.body.classList.remove('modal-open');
}

function closeModal() {
    closeUnifiedModal();
}

function hideModal() {
    closeModal();
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    console.log('Unified logout modal system loaded successfully!');
    console.log('Functions available:', {
        showUnifiedLogoutModal: typeof showUnifiedLogoutModal === 'function',
        unifiedLogout: typeof unifiedLogout === 'function',
        showModal: typeof showModal === 'function',
        logout: typeof logout === 'function'
    });
});
</script>
