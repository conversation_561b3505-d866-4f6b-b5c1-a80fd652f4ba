<?php
/**
 * Test Enhanced Error System
 * اختبار نظام معالجة الأخطاء المحسن
 * 
 * @version 1.0
 * <AUTHOR> CX Security Team
 */

// منع الوصول المباشر إلا للاختبار
if (!isset($_GET['test']) || $_GET['test'] !== 'error_system') {
    die('Access denied. Use ?test=error_system to run tests.');
}

// تهيئة نظام معالجة الأخطاء
require_once __DIR__ . '/init_error_system.php';
initializeErrorSystemForFile('test');

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام معالجة الأخطاء - Kalam CX</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .test-card { margin: 20px 0; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .test-success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .test-error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .test-warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .test-info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-bug me-2"></i>
            اختبار نظام معالجة الأخطاء المحسن
        </h1>
        
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            <strong>ملاحظة:</strong> هذه الصفحة لاختبار نظام معالجة الأخطاء الجديد. 
            جميع الأخطاء المُولدة هنا لأغراض الاختبار فقط.
        </div>

        <?php
        $test_results = [];
        
        /**
         * اختبار 1: تسجيل الأخطاء الآمن
         */
        function test_secure_logging() {
            global $test_results;
            
            try {
                secureErrorLog("Test error message with sensitive data: password=secret123", 'error', [
                    'test_type' => 'secure_logging',
                    'sensitive_data' => 'This should be hidden'
                ]);
                
                $test_results[] = [
                    'name' => 'تسجيل الأخطاء الآمن',
                    'status' => 'success',
                    'message' => 'تم تسجيل الخطأ بنجاح مع إخفاء البيانات الحساسة'
                ];
                
            } catch (Exception $e) {
                $test_results[] = [
                    'name' => 'تسجيل الأخطاء الآمن',
                    'status' => 'error',
                    'message' => 'فشل في تسجيل الخطأ: ' . $e->getMessage()
                ];
            }
        }
        
        /**
         * اختبار 2: معالجة أخطاء قاعدة البيانات
         */
        function test_database_error_handling() {
            global $test_results;
            
            try {
                // محاولة اتصال خاطئ لاختبار معالجة الأخطاء
                $test_conn = new mysqli('invalid_host', 'invalid_user', 'invalid_pass', 'invalid_db');
                
                if ($test_conn->connect_error) {
                    handleDatabaseError($test_conn, 'test_connection');
                }
                
            } catch (Exception $e) {
                $test_results[] = [
                    'name' => 'معالجة أخطاء قاعدة البيانات',
                    'status' => 'success',
                    'message' => 'تم اكتشاف ومعالجة خطأ قاعدة البيانات بنجاح'
                ];
            }
        }
        
        /**
         * اختبار 3: معالجة أخطاء التحقق
         */
        function test_validation_error_handling() {
            global $test_results;
            
            try {
                handleValidationError('email', 'invalid-email', 'valid_email');
                
            } catch (InvalidArgumentException $e) {
                $test_results[] = [
                    'name' => 'معالجة أخطاء التحقق',
                    'status' => 'success',
                    'message' => 'تم اكتشاف ومعالجة خطأ التحقق بنجاح'
                ];
            } catch (Exception $e) {
                $test_results[] = [
                    'name' => 'معالجة أخطاء التحقق',
                    'status' => 'error',
                    'message' => 'خطأ غير متوقع: ' . $e->getMessage()
                ];
            }
        }
        
        /**
         * اختبار 4: فحص إعدادات البيئة
         */
        function test_environment_settings() {
            global $test_results;
            
            $environment = ErrorConfig::getCurrentEnvironment();
            $is_production = ErrorConfig::isProduction();
            $settings = ErrorConfig::getErrorSettings();
            
            $test_results[] = [
                'name' => 'إعدادات البيئة',
                'status' => 'info',
                'message' => "البيئة الحالية: $environment | الإنتاج: " . ($is_production ? 'نعم' : 'لا')
            ];
        }
        
        /**
         * اختبار 5: فحص مجلدات اللوجات
         */
        function test_log_directories() {
            global $test_results;
            
            $log_paths = ErrorConfig::getLogPaths();
            $all_accessible = true;
            $missing_dirs = [];
            
            foreach ($log_paths as $type => $path) {
                $dir = dirname($path);
                if (!is_dir($dir)) {
                    $all_accessible = false;
                    $missing_dirs[] = $type;
                }
            }
            
            if ($all_accessible) {
                $test_results[] = [
                    'name' => 'مجلدات اللوجات',
                    'status' => 'success',
                    'message' => 'جميع مجلدات اللوجات متاحة وقابلة للكتابة'
                ];
            } else {
                $test_results[] = [
                    'name' => 'مجلدات اللوجات',
                    'status' => 'warning',
                    'message' => 'بعض مجلدات اللوجات غير متاحة: ' . implode(', ', $missing_dirs)
                ];
            }
        }
        
        /**
         * اختبار 6: فحص الأنماط الحساسة
         */
        function test_sensitive_patterns() {
            global $test_results;
            
            $test_message = "User login: email=<EMAIL> password=secret123 api_key=abc123xyz";
            $cleaned_message = sanitizeErrorMessage($test_message);
            
            if (strpos($cleaned_message, 'secret123') === false && strpos($cleaned_message, 'abc123xyz') === false) {
                $test_results[] = [
                    'name' => 'إخفاء البيانات الحساسة',
                    'status' => 'success',
                    'message' => 'تم إخفاء البيانات الحساسة بنجاح من رسائل الأخطاء'
                ];
            } else {
                $test_results[] = [
                    'name' => 'إخفاء البيانات الحساسة',
                    'status' => 'error',
                    'message' => 'فشل في إخفاء البيانات الحساسة'
                ];
            }
        }
        
        /**
         * اختبار 7: فحص صحة النظام
         */
        function test_system_health() {
            global $test_results;
            
            $health_status = performSystemHealthCheck();
            
            if ($health_status) {
                $test_results[] = [
                    'name' => 'فحص صحة النظام',
                    'status' => 'success',
                    'message' => 'النظام يعمل بصحة جيدة'
                ];
            } else {
                $test_results[] = [
                    'name' => 'فحص صحة النظام',
                    'status' => 'warning',
                    'message' => 'تم اكتشاف بعض المشاكل في النظام (راجع اللوجات)'
                ];
            }
        }
        
        // تشغيل جميع الاختبارات
        if (isset($_GET['run_tests'])) {
            test_secure_logging();
            test_database_error_handling();
            test_validation_error_handling();
            test_environment_settings();
            test_log_directories();
            test_sensitive_patterns();
            test_system_health();
        }
        ?>
        
        <!-- أزرار التحكم -->
        <div class="row mb-4">
            <div class="col-md-6">
                <a href="?test=error_system&run_tests=1" class="btn btn-primary btn-lg w-100">
                    <i class="fas fa-play me-2"></i>
                    تشغيل جميع الاختبارات
                </a>
            </div>
            <div class="col-md-6">
                <a href="enhanced_error_handler.php?error=500" class="btn btn-warning btn-lg w-100" target="_blank">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    اختبار صفحة الأخطاء
                </a>
            </div>
        </div>
        
        <!-- نتائج الاختبارات -->
        <?php if (!empty($test_results)): ?>
        <div class="card">
            <div class="card-header">
                <h3><i class="fas fa-clipboard-check me-2"></i>نتائج الاختبارات</h3>
            </div>
            <div class="card-body">
                <?php foreach ($test_results as $result): ?>
                <div class="test-result test-<?php echo $result['status']; ?>">
                    <strong><?php echo htmlspecialchars($result['name']); ?>:</strong>
                    <?php echo htmlspecialchars($result['message']); ?>
                </div>
                <?php endforeach; ?>
                
                <?php
                $success_count = count(array_filter($test_results, function($r) { return $r['status'] === 'success'; }));
                $total_count = count($test_results);
                $success_rate = $total_count > 0 ? round($success_count / $total_count * 100, 2) : 0;
                ?>
                
                <div class="mt-3 p-3 bg-light rounded">
                    <h5>ملخص النتائج:</h5>
                    <p><strong>معدل النجاح:</strong> <?php echo $success_rate; ?>%</p>
                    <p><strong>الاختبارات الناجحة:</strong> <?php echo $success_count; ?> من <?php echo $total_count; ?></p>
                    
                    <?php if ($success_rate >= 90): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        ممتاز! نظام معالجة الأخطاء يعمل بشكل مثالي.
                    </div>
                    <?php elseif ($success_rate >= 70): ?>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        جيد! معظم الاختبارات نجحت، لكن هناك بعض المشاكل البسيطة.
                    </div>
                    <?php else: ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-times-circle me-2"></i>
                        تحذير! هناك مشاكل في نظام معالجة الأخطاء تحتاج إصلاح.
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>
        
        <!-- معلومات النظام -->
        <div class="card mt-4">
            <div class="card-header">
                <h3><i class="fas fa-info-circle me-2"></i>معلومات النظام</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>البيئة:</strong> <?php echo ErrorConfig::getCurrentEnvironment(); ?></p>
                        <p><strong>إصدار PHP:</strong> <?php echo PHP_VERSION; ?></p>
                        <p><strong>استهلاك الذاكرة:</strong> <?php echo round(memory_get_usage(true) / 1024 / 1024, 2); ?> MB</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>وقت التشغيل:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
                        <p><strong>المستخدم:</strong> <?php echo $_SESSION['username'] ?? 'غير مسجل'; ?></p>
                        <p><strong>عنوان IP:</strong> <?php echo $_SERVER['REMOTE_ADDR'] ?? 'غير معروف'; ?></p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- روابط مفيدة -->
        <div class="card mt-4">
            <div class="card-header">
                <h3><i class="fas fa-link me-2"></i>روابط مفيدة</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <a href="ERROR_HANDLING_IMPROVEMENTS_REPORT.md" class="btn btn-outline-primary w-100 mb-2" target="_blank">
                            <i class="fas fa-file-alt me-2"></i>تقرير التحسينات
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="logs/" class="btn btn-outline-secondary w-100 mb-2" target="_blank">
                            <i class="fas fa-folder me-2"></i>مجلد اللوجات
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="apply_error_system.php?apply_system=1" class="btn btn-outline-success w-100 mb-2" target="_blank">
                            <i class="fas fa-cogs me-2"></i>تطبيق النظام
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
