<?php
/**
 * ملف مساعد لتطبيق نظام الصلاحيات على الصفحات الموجودة
 * يحتوي على دوال مساعدة لتسهيل تطبيق النظام
 */

// تضمين اتصال قاعدة البيانات
include_once $_SERVER['DOCUMENT_ROOT'].'/config/Databasehost.php';
// تضمين نظام الصلاحيات
include_once $_SERVER['DOCUMENT_ROOT'].'/App/includes/permissions.php';

/**
 * دالة سريعة لبدء نظام الصلاحيات في أي صفحة
 * @param string $page_name اسم الصفحة للتحقق من الصلاحية
 * @return array معلومات المستخدم والصلاحيات
 */
function initializePermissions($page_name = '') {
    // التأكد من بدء الجلسة
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    // الحصول على معلومات المستخدم
    $user_email = $_SESSION['username'] ?? '';
    $user_permission = $_SESSION['access_level'] ?? 'Viewer';

    // تضمين ملف leader_filter للحصول على اسم المستخدم
    include_once $_SERVER['DOCUMENT_ROOT'].'/App/includes/leader_filter.php';

    $user_info = [
        'email' => $user_email,
        'permission' => $user_permission,
        'name' => extractLeaderNameFromEmail($user_email),
        'can_edit' => canEdit($user_permission),
        'leader_name_in_db' => findLeaderNameInDatabase($user_email),
        'is_leader_filtered' => getDataFilterType($user_permission) === 'leader'
    ];
    
    // التحقق من صلاحية الوصول للصفحة إذا تم تحديد اسم الصفحة
    if (!empty($page_name)) {
        checkPagePermission($page_name, $user_info['permission']);
    }
    
    return $user_info;
}

/**
 * دالة لإضافة فلترة الليدر إلى استعلام SQL موجود
 * @param string $sql الاستعلام الأصلي
 * @param string $user_permission صلاحية المستخدم
 * @param string $user_email إيميل المستخدم
 * @param string $leader_column_name اسم عمود الليدر (افتراضي: Leader)
 * @return string الاستعلام مع الفلترة
 */
function addLeaderFilterToSQL($sql, $user_permission, $user_email, $leader_column_name = 'Leader') {
    $leader_filter = getLeaderFilterCondition($user_permission, $user_email, $leader_column_name);

    if ($leader_filter) {
        // البحث عن آخر WHERE في الاستعلام
        $last_where_pos = strripos($sql, 'WHERE');

        if ($last_where_pos !== false) {
            // البحث عن ORDER BY بعد آخر WHERE
            $order_by_pos = stripos($sql, 'ORDER BY', $last_where_pos);

            if ($order_by_pos !== false) {
                // إدراج الشرط قبل ORDER BY
                $before_order = substr($sql, 0, $order_by_pos);
                $order_part = substr($sql, $order_by_pos);
                $sql = $before_order . $leader_filter . ' ' . $order_part;
            } else {
                // إضافة الشرط في نهاية الاستعلام
                $sql .= $leader_filter;
            }
        } else {
            // إذا لم يكن يحتوي على WHERE، أضف WHERE
            $sql .= ' WHERE 1=1' . $leader_filter;
        }
    }

    return $sql;
}

/**
 * دالة لإنشاء شريط معلومات المستخدم
 * @param array $user_info معلومات المستخدم
 * @return string HTML للشريط
 */
function generateUserInfoBar($user_info) {
    $permission_colors = [
        'Super Admin' => 'danger',
        'Admin' => 'primary',
        'Editor' => 'info',
        'Manager' => 'warning',
        'Leader' => 'success',
        'Viewer' => 'secondary'
    ];
    
    $color = $permission_colors[$user_info['permission']] ?? 'secondary';
    $edit_status = $user_info['can_edit'] ? 'يمكن التعديل' : 'قراءة فقط';
    $edit_color = $user_info['can_edit'] ? 'success' : 'warning';
    
    return "
    <div class='alert alert-light border d-flex justify-content-between align-items-center mb-3'>
        <div>
            <i class='fas fa-user'></i>
            <strong>{$user_info['name']['full']}</strong>
            <span class='badge bg-{$color} ms-2'>{$user_info['permission']}</span>
        </div>
        <div>
            <span class='badge bg-{$edit_color}'>{$edit_status}</span>
        </div>
    </div>";
}

/**
 * دالة لإنشاء أزرار الإجراءات بناءً على الصلاحيات
 * @param array $user_info معلومات المستخدم
 * @param array $actions الإجراءات المطلوبة
 * @return string HTML للأزرار
 */
function generateActionButtons($user_info, $actions = []) {
    if (!$user_info['can_edit']) {
        return '<p class="text-muted"><i class="fas fa-lock"></i> وضع القراءة فقط</p>';
    }
    
    $buttons = '';
    $default_actions = [
        'add' => ['label' => 'إضافة جديد', 'icon' => 'fa-plus', 'class' => 'btn-primary'],
        'edit' => ['label' => 'تعديل', 'icon' => 'fa-edit', 'class' => 'btn-warning'],
        'delete' => ['label' => 'حذف', 'icon' => 'fa-trash', 'class' => 'btn-danger'],
        'export' => ['label' => 'تصدير', 'icon' => 'fa-download', 'class' => 'btn-success']
    ];
    
    foreach ($actions as $action => $config) {
        $action_config = array_merge($default_actions[$action] ?? [], $config);
        $onclick_value = $action_config['onclick'] ?? '';
        $buttons .= "
        <button type='button' class='btn {$action_config['class']} me-2' onclick='{$onclick_value}'>
            <i class='fas {$action_config['icon']}'></i>
            {$action_config['label']}
        </button>";
    }
    
    return $buttons;
}

/**
 * دالة لإنشاء تنبيه فلترة البيانات للـ Leader
 * @param string $user_permission صلاحية المستخدم
 * @param string $user_email إيميل المستخدم
 * @return string HTML للتنبيه
 */
function generateLeaderFilterAlert($user_permission, $user_email) {
    if (getDataFilterType($user_permission) === 'leader') {
        // تضمين ملف leader_filter للحصول على اسم المستخدم
        include_once $_SERVER['DOCUMENT_ROOT'].'/App/includes/leader_filter.php';
        $name = extractLeaderNameFromEmail($user_email);
        return "
        <div class='alert alert-info'>
            <i class='fas fa-filter'></i>
            <strong>تنبيه:</strong> يتم عرض البيانات المتعلقة بفريق <strong>{$name['full']}</strong> فقط.
        </div>";
    }

    return '';
}

/**
 * دالة لتطبيق نظام الصلاحيات على جدول HTML
 * @param string $table_html HTML الجدول
 * @param array $user_info معلومات المستخدم
 * @param array $restricted_columns الأعمدة المقيدة حسب الصلاحية
 * @return string HTML الجدول المعدل
 */
function applyPermissionsToTable($table_html, $user_info, $restricted_columns = []) {
    // إذا كان المستخدم Viewer، إزالة أعمدة الإجراءات
    if (!$user_info['can_edit']) {
        // إزالة أعمدة الإجراءات (Edit, Delete, etc.)
        $table_html = preg_replace('/<th[^>]*>.*?(تعديل|حذف|إجراءات|Actions|Edit|Delete).*?<\/th>/i', '', $table_html);
        $table_html = preg_replace('/<td[^>]*>.*?<button.*?<\/button>.*?<\/td>/i', '', $table_html);
    }
    
    // تطبيق قيود الأعمدة حسب الصلاحية
    if (isset($restricted_columns[$user_info['permission']])) {
        foreach ($restricted_columns[$user_info['permission']] as $column) {
            $table_html = preg_replace("/<th[^>]*>.*?{$column}.*?<\/th>/i", '', $table_html);
            // يمكن إضافة منطق أكثر تعقيداً لإزالة خلايا البيانات المقابلة
        }
    }
    
    return $table_html;
}

/**
 * دالة لإنشاء قائمة منسدلة للصلاحيات (للـ Admin فقط)
 * @param string $current_permission الصلاحية الحالية
 * @param string $user_permission صلاحية المستخدم الذي يعدل
 * @return string HTML للقائمة المنسدلة
 */
function generatePermissionDropdown($current_permission = 'Viewer', $user_permission = 'Viewer') {
    // فقط Admin و Super Admin يمكنهم تعديل الصلاحيات
    if (!in_array($user_permission, ['Admin', 'Super Admin'])) {
        return "<input type='hidden' name='permission' value='{$current_permission}'>
                <span class='badge bg-secondary'>{$current_permission}</span>";
    }
    
    $permissions = getAvailablePermissions();
    $options = '';
    
    foreach ($permissions as $permission) {
        $selected = ($permission === $current_permission) ? 'selected' : '';
        $options .= "<option value='{$permission}' {$selected}>{$permission}</option>";
    }
    
    return "<select name='permission' class='form-select' required>{$options}</select>";
}

/**
 * دالة لتسجيل عمليات الوصول (للمراجعة الأمنية)
 * @param string $action الإجراء المنفذ
 * @param array $details تفاصيل إضافية
 */
function logPermissionAccess($action, $details = []) {
    $log_entry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'user' => $_SESSION['username'] ?? 'unknown',
        'permission' => $_SESSION['access_level'] ?? 'unknown',
        'action' => $action,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        'details' => $details
    ];
    
    // يمكن حفظ السجل في قاعدة البيانات أو ملف
    error_log("PERMISSION_ACCESS: " . json_encode($log_entry));
}

/**
 * دالة للتحقق من صلاحية تعديل مستخدم آخر
 * @param string $target_user_permission صلاحية المستخدم المراد تعديله
 * @param string $current_user_permission صلاحية المستخدم الحالي
 * @return bool
 */
function canEditUser($target_user_permission, $current_user_permission) {
    $hierarchy = [
        'Super Admin' => 6,
        'Admin' => 5,
        'Editor' => 4,
        'Manager' => 3,
        'Leader' => 2,
        'Viewer' => 1
    ];
    
    $current_level = $hierarchy[$current_user_permission] ?? 0;
    $target_level = $hierarchy[$target_user_permission] ?? 0;
    
    // يمكن تعديل المستخدمين ذوي المستوى الأقل أو المساوي
    return $current_level >= $target_level;
}
?>
