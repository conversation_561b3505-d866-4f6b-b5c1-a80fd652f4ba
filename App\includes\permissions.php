<?php
/**
 * نظام إدارة الصلاحيات
 * يحتوي على تعريف جميع الصلاحيات والدوال المساعدة
 */

// تعريف مستويات الصلاحيات
define('PERMISSION_SUPER_ADMIN', 'Super Admin');
define('PERMISSION_ADMIN', 'Admin');
define('PERMISSION_EDITOR', 'Editor');
define('PERMISSION_MANAGER', 'Manager');
define('PERMISSION_LEADER', 'Leader');
define('PERMISSION_VIEWER', 'Viewer');
define('PERMISSION_MEMBER', 'Member');

/**
 * مصفوفة الصلاحيات وما يمكن لكل صلاحية الوصول إليه
 */
$permissions_config = [
    PERMISSION_SUPER_ADMIN => [
        'can_access_all' => true,
        'sidebar_items' => ['all'],
        'data_filter' => 'none' // لا توجد فلترة
    ],
    PERMISSION_ADMIN => [
        'can_access_all' => true,
        'sidebar_items' => ['all'],
        'data_filter' => 'none'
    ],
    PERMISSION_EDITOR => [
        'can_access_all' => true,
        'sidebar_items' => ['all'],
        'data_filter' => 'none'
    ],
    PERMISSION_MANAGER => [
        'can_access_all' => false,
        'sidebar_items' => [
            'profile', 'dashboard', 'reports', 'leaves', 'realtime', 
            'transactions', 'settings'
        ],
        'hidden_items' => ['users', 'users_archive'],
        'data_filter' => 'none'
    ],
    PERMISSION_LEADER => [
        'can_access_all' => false,
        'sidebar_items' => [
            'profile', 'dashboard', 'reports', 'leaves', 'realtime', 
            'transactions', 'settings'
        ],
        'hidden_items' => ['users', 'users_archive'],
        'data_filter' => 'leader' // فلترة البيانات بناءً على اسم الليدر
    ],
    PERMISSION_VIEWER => [
        'can_access_all' => false,
        'sidebar_items' => ['all'],
        'data_filter' => 'none',
        'read_only' => true
    ],
    PERMISSION_MEMBER => [
        'can_access_all' => false,
        'sidebar_items' => ['profile', 'dashboard'],
        'data_filter' => 'member', // فلترة البيانات الخاصة بالعضو
        'read_only' => false
    ]
];



/**
 * التحقق من صلاحية الوصول لعنصر في القائمة الجانبية
 * @param string $item_name
 * @param string $user_permission
 * @return bool
 */
function canAccessSidebarItem($item_name, $user_permission) {
    global $permissions_config;
    
    if (!isset($permissions_config[$user_permission])) {
        return false;
    }
    
    $config = $permissions_config[$user_permission];
    
    // إذا كان يمكن الوصول لكل شيء
    if (isset($config['can_access_all']) && $config['can_access_all']) {
        return true;
    }
    
    // إذا كان العنصر في القائمة المخفية
    if (isset($config['hidden_items']) && in_array($item_name, $config['hidden_items'])) {
        return false;
    }
    
    // إذا كان العنصر في القائمة المسموحة أو 'all'
    if (isset($config['sidebar_items'])) {
        return in_array('all', $config['sidebar_items']) || in_array($item_name, $config['sidebar_items']);
    }
    
    return false;
}

/**
 * التحقق من نوع فلترة البيانات المطلوبة للمستخدم
 * @param string $user_permission
 * @return string
 */
function getDataFilterType($user_permission) {
    global $permissions_config;
    
    if (!isset($permissions_config[$user_permission])) {
        return 'none';
    }
    
    return $permissions_config[$user_permission]['data_filter'] ?? 'none';
}

/**
 * البحث عن اسم الليدر الصحيح في قاعدة البيانات
 * @param string $user_email
 * @return string
 */
function findLeaderNameInDatabase($user_email) {
    // تضمين ملف leader_filter للحصول على اسم الليدر من الإيميل
    include_once $_SERVER['DOCUMENT_ROOT'].'/App/includes/leader_filter.php';

    // استخراج اسم الليدر من الإيميل مباشرة
    $name_info = extractLeaderNameFromEmail($user_email);

    // إرجاع الاسم الكامل المستخرج من الإيميل
    return !empty($name_info['full']) ? $name_info['full'] : '';
}

/**
 * الحصول على شرط WHERE للفلترة بناءً على صلاحية المستخدم
 * @param string $user_permission
 * @param string $user_email
 * @param string $leader_column_name
 * @return string
 */
function getLeaderFilterCondition($user_permission, $user_email, $leader_column_name = 'Leader') {
    $filter_type = getDataFilterType($user_permission);

    if ($filter_type === 'leader') {
        $leader_name = findLeaderNameInDatabase($user_email);
        return " AND {$leader_column_name} = '" . addslashes($leader_name) . "'";
    }

    return '';
}

/**
 * التحقق من صلاحية الوصول للصفحة
 * @param string $page_name
 * @param string $user_permission
 * @return bool
 */
function canAccessPage($page_name, $user_permission) {
    global $permissions_config;
    
    if (!isset($permissions_config[$user_permission])) {
        return false;
    }
    
    $config = $permissions_config[$user_permission];
    
    // Super Admin و Admin يمكنهم الوصول لكل شيء
    if (in_array($user_permission, [PERMISSION_SUPER_ADMIN, PERMISSION_ADMIN, PERMISSION_EDITOR])) {
        return true;
    }
    
    // صفحات محظورة على Manager و Leader
    $restricted_pages = ['users', 'users_archive'];
    if (in_array($user_permission, [PERMISSION_MANAGER, PERMISSION_LEADER]) && in_array($page_name, $restricted_pages)) {
        return false;
    }
    
    return true;
}

/**
 * التحقق من صلاحية التعديل (للـ Viewer)
 * @param string $user_permission
 * @return bool
 */
function canEdit($user_permission) {
    global $permissions_config;
    
    if (!isset($permissions_config[$user_permission])) {
        return false;
    }
    
    return !isset($permissions_config[$user_permission]['read_only']) || 
           !$permissions_config[$user_permission]['read_only'];
}

/**
 * إعادة توجيه المستخدم إذا لم يكن لديه صلاحية
 * @param string $page_name
 * @param string $user_permission
 */
function checkPagePermission($page_name, $user_permission) {
    if (!canAccessPage($page_name, $user_permission)) {
        // Log the permission denial
        logPermissionDenial($page_name, $user_permission);

        // Redirect immediately to appropriate page
        if (isset($_SESSION['username']) && isset($_SESSION['access_level'])) {
            // User is logged in, redirect to dashboard
            $access_level = $_SESSION['access_level'];
            switch ($access_level) {
                case 'Super Admin':
                case 'Admin':
                case 'Editor':
                case 'Manager':
                case 'Leader':
                case 'Viewer':
                    header("Location: /App/Home");
                    break;
                case 'Member':
                    header("Location: /MemberHome.php");
                    break;
                default:
                    header("Location: /App/Home");
                    break;
            }
        } else {
            // User is not logged in, redirect to login page
            header("Location: /Loginpage.php?error=access_denied");
        }
        exit();
    }
}

/**
 * تسجيل محاولات الوصول المرفوضة
 * @param string $page_name
 * @param string $user_permission
 */
function logPermissionDenial($page_name, $user_permission) {
    global $conn;
    try {
        // Include database connection if not available
        if (!isset($conn) || $conn === null) {
            include_once $_SERVER['DOCUMENT_ROOT'].'/config/Databasehost.php';
        }

        // Check if security_logs table exists
        $check_table = $conn->query("SHOW TABLES LIKE 'security_logs'");
        if ($check_table->num_rows == 0) {
            $create_table = "CREATE TABLE IF NOT EXISTS security_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                event_type VARCHAR(100) NOT NULL,
                user_email VARCHAR(255),
                ip_address VARCHAR(45),
                event_data TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )";
            $conn->query($create_table);
        }

        $sql = "INSERT INTO security_logs (event_type, user_email, ip_address, event_data, created_at)
                VALUES (?, ?, ?, ?, NOW())";
        $stmt = $conn->prepare($sql);
        if ($stmt) {
            $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
            $user = $_SESSION['username'] ?? 'anonymous';
            $details = [
                'page_name' => $page_name,
                'user_permission' => $user_permission,
                'requested_url' => $_SERVER['REQUEST_URI'] ?? 'unknown',
                'referer' => $_SERVER['HTTP_REFERER'] ?? 'direct',
                'timestamp' => date('Y-m-d H:i:s')
            ];
            $details_json = json_encode($details);
            $event_type = 'permission_denied';
            $stmt->bind_param("ssss", $event_type, $user, $ip, $details_json);
            $stmt->execute();
            $stmt->close();
        }
    } catch (Exception $e) {
        error_log("Permission denial logging failed: " . $e->getMessage());
    }
}

/**
 * الحصول على قائمة الصلاحيات المتاحة
 * @return array
 */
function getAvailablePermissions() {
    return [
        PERMISSION_SUPER_ADMIN,
        PERMISSION_ADMIN,
        PERMISSION_EDITOR,
        PERMISSION_MANAGER,
        PERMISSION_LEADER,
        PERMISSION_VIEWER,
        PERMISSION_MEMBER
    ];
}
?>
