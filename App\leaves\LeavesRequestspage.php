
<?php
include $_SERVER['DOCUMENT_ROOT'].'/config/Databasehost.php';
session_start(); // Start session

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    header("Location: ../../Loginpage.php");
    exit();
}
// تضمين نظام الصلاحيات
include_once $_SERVER['DOCUMENT_ROOT'].'/App/includes/permissions.php';

// التحقق من صلاحية الوصول للصفحة
checkPagePermission('leaves', $_SESSION['access_level'] ?? 'Viewer');

// Check access level
$access = $_SESSION['access_level'];
$email = $_SESSION['username'];

// Define permission levels for different functionalities
$canEditComments = in_array($access, ['Admin', 'Super Admin', 'Editor']);
$canUseActions = in_array($access, ['Admin', 'Super Admin', 'Editor', 'Manager', 'Leader']);
$isViewer = ($access === 'Viewer');

// تضمين ملف leader_filter للحصول على اسم المستخدم
include_once $_SERVER['DOCUMENT_ROOT'].'/App/includes/leader_filter.php';

// Extract user information for header display
$userInfo = extractLeaderNameFromEmail($email);
$displayName = !empty($userInfo['full']) ? $userInfo['full'] : 'User';
$displayRole = !empty($access) ? $access : 'Viewer';

// Create the header display text with fallback handling
if (!empty($displayName) && $displayName !== 'User') {
    $headerDisplayText = $displayName . ' - ' . $displayRole;
} else {
    // Fallback: extract name manually if function fails
    $emailParts = explode('@', $email);
    if (!empty($emailParts[0])) {
        $namePart = $emailParts[0];
        if (strpos($namePart, '.') !== false) {
            $nameComponents = explode('.', $namePart);
            $fallbackName = ucfirst($nameComponents[0]) . ' ' . ucfirst($nameComponents[1]);
        } else {
            $fallbackName = ucfirst($namePart);
        }
        $headerDisplayText = $fallbackName . ' - ' . $displayRole;
    } else {
        $headerDisplayText = 'User - ' . $displayRole;
    }
}

set_time_limit(300);
include $_SERVER['DOCUMENT_ROOT']. '/PHPMailer/vendor/phpmailer/phpmailer/src/PHPMailer.php';
include $_SERVER['DOCUMENT_ROOT']. '/PHPMailer/vendor/phpmailer/phpmailer/src/SMTP.php';
include $_SERVER['DOCUMENT_ROOT']. '/PHPMailer/vendor/phpmailer/phpmailer/src/Exception.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

// Handle comment-only save
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['SaveCommentOnly'], $_POST['LeavesCode'], $_POST['WFMComment'])) {
    $LeavesCode = $_POST['LeavesCode'];
    $WFMComment = $_POST['WFMComment'];
    $CurrentUserRole = $_SESSION['access_level'];

    // Check if user has permission to edit WFM Comments
    $canEditComments = in_array($CurrentUserRole, ['Admin', 'Super Admin', 'Editor']);

    if (!$canEditComments) {
        echo json_encode([
            'status' => 'error',
            'message' => 'You do not have permission to edit WFM Comments'
        ]);
        exit();
    }

    $stmt = $conn->prepare("UPDATE leaves SET `WFMComment` = ? WHERE LeavesCode = ?");
    $stmt->bind_param("ss", $WFMComment, $LeavesCode);

    if ($stmt->execute()) {
        echo "success";
    } else {
        echo "error";
    }
    exit();
}

// Handle full status update
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['LeavesCode'], $_POST['Status'], $_POST['Email'], $_POST['From'], $_POST['WFMComment'])) {
    $LeavesCode = $_POST['LeavesCode'];
    $Status = $_POST['Status'];
    $Email = $_POST['Email'];
    $From = $_POST['From'];
    $WFMComment = $_POST['WFMComment'];
    $Approver = $_SESSION['username'];
    $CurrentUserRole = $_SESSION['access_level'];

    // Get Leader name from database
    $leader_sql = "SELECT Leader FROM databasehc WHERE FGEmails = ? LIMIT 1";
    $leader_stmt = $conn->prepare($leader_sql);
    $leader_stmt->bind_param("s", $Email);
    $leader_stmt->execute();
    $leader_result = $leader_stmt->get_result();
    $leader_row = $leader_result->fetch_assoc();
    $Leader = $leader_row['Leader'] ?? "Not Assigned";
    $leader_stmt->close();

    // Check if current user is Manager or Leader
    $isManagerOrLeader = in_array($CurrentUserRole, ['Manager', 'Leader']);

    if ($isManagerOrLeader) {
        // For Manager/Leader: Update LeaderFeedback fields instead of Status
        $stmt = $conn->prepare("UPDATE leaves SET `LeaderFeedback` = ?, `LeaderFeedbackemail` = ? WHERE LeavesCode = ?");
        $stmt->bind_param("sss", $Status, $Approver, $LeavesCode);

        if ($stmt->execute()) {
            echo json_encode([
                'status' => 'success',
                'message' => 'Leader feedback recorded successfully',
                'action' => 'leader_feedback'
            ]);
        } else {
            echo json_encode([
                'status' => 'error',
                'message' => 'Failed to record leader feedback: ' . $stmt->error,
                'action' => 'leader_feedback'
            ]);
        }
        $stmt->close();
        exit();
    }

    // For admin/super admin/editor: Update status normally (existing functionality)
    $stmt = $conn->prepare("UPDATE leaves SET `Status` = ?, `Approver` = ?, `WFMComment` = ? WHERE LeavesCode = ?");
    $stmt->bind_param("ssss", $Status, $Approver, $WFMComment, $LeavesCode);

    if ($stmt->execute()) {
        // Update schedule table if approved
        if ($Status === "Approved") {
            $stmt2 = $conn->prepare("UPDATE schedule SET Shift = 'Vacation' WHERE EmailSch = ? AND Date = ?");
            $stmt2->bind_param("ss", $Email, $From);
            $stmt2->execute();
        }

        $mailConfig = include $_SERVER['DOCUMENT_ROOT'].'/config/Emailinformation.php';
        $mail = new PHPMailer(true);
        try {
            // SMTP settings
            $mail->isSMTP();
            $mail->Host = $mailConfig['host'];
            $mail->SMTPAuth = true;
            $mail->Username = $mailConfig['username'];
            $mail->Password = $mailConfig['password'];
            $mail->SMTPSecure = $mailConfig['encryption'];
            $mail->Port = $mailConfig['port'];
            $mail->CharSet = $mailConfig['charset'];

            // Email setup
            $mail->setFrom($mailConfig['username'], 'Kalam App IMS');

            // Add recipients
            if (isset($mailConfig['recipients']) && is_array($mailConfig['recipients'])) {
                foreach ($mailConfig['recipients'] as $recipient) {
                    $mail->addAddress($recipient);
                }
            }

            // Add the employee as recipient
            $mail->addAddress($Email);

            // Email content
            $mail->isHTML(true);
            $mail->Subject = "Leave Request {$Status} - Kalam App";

            // Email body with status-specific styling
            $statusColor = ($Status === "Approved") ? "#4CAF50" : "#F44336";
            $statusIcon = ($Status === "Approved") ? "✅" : "❌";

            $mail->Body = "
            <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px;'>
                <div style='text-align: center; margin-bottom: 20px;'>
                    <img src='https://kalamcxapp.site/' alt='Kalam Logo' style='max-width: 150px;'>
                    <h2 style='color: #333;'>Leave Request Update</h2>
                </div>

                <div style='background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin-bottom: 20px;'>
                    <p style='font-size: 18px; color: #333;'>
                        Your leave request has been <strong style='color: {$statusColor};'>{$Status} {$statusIcon}</strong>
                    </p>
                </div>

                <table style='width: 100%; border-collapse: collapse; margin-bottom: 20px;'>
                    <tr style='background-color: #f2f2f2;'>
                        <th style='padding: 10px; border-bottom: 1px solid #ddd; text-align: left;'>Leave Details</th>
                        <th style='padding: 10px; border-bottom: 1px solid #ddd; text-align: left;'>Information</th>
                    </tr>
                    <tr>
                        <td style='padding: 10px; border-bottom: 1px solid #ddd;'><strong>Request ID</strong></td>
                        <td style='padding: 10px; border-bottom: 1px solid #ddd;'>{$LeavesCode}</td>
                    </tr>
                    <tr>
                        <td style='padding: 10px; border-bottom: 1px solid #ddd;'><strong>Status</strong></td>
                        <td style='padding: 10px; border-bottom: 1px solid #ddd; color: {$statusColor};'><strong>{$Status}</strong></td>
                    </tr>
                    <tr>
                        <td style='padding: 10px; border-bottom: 1px solid #ddd;'><strong>Approver</strong></td>
                        <td style='padding: 10px; border-bottom: 1px solid #ddd;'>{$Approver}</td>
                    </tr>
                    <tr>
                        <td style='padding: 10px; border-bottom: 1px solid #ddd;'><strong>Comments</strong></td>
                        <td style='padding: 10px; border-bottom: 1px solid #ddd;'>{$WFMComment}</td>
                    </tr>
                    <tr>
                        <td style='padding: 10px; border-bottom: 1px solid #ddd;'><strong>Date</strong></td>
                        <td style='padding: 10px; border-bottom: 1px solid #ddd;'>" . date('d/m/Y', strtotime($From)) . "</td>
                    </tr>
                </table>

                <p style='font-size: 16px; color: #555; margin-top: 15px;'>For further details, please check your leave records.</p>

                <p style='text-align: center; margin-top: 20px;'>
                    <a href='https://kalamcxapp.site/App/leaves/LeavesRequestspage.php?highlight={$LeavesCode}' style='display: inline-block; padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px; font-weight: bold;'>📂 View This Leave Request</a>
                </p>

                <p style='font-size: 14px; color: #666; text-align: center; margin-top: 20px;'>
                    Best regards, <br>
                    <strong style='color: #007bff;'>💼 Kalam App Team</strong>
                </p>
            </div>
            ";

            $mail->send();
            echo "success";

        } catch (Exception $e) {
            echo "Email error: " . $mail->ErrorInfo;
        }

    } else {
        echo "error";
    }
    exit();
}

// Function to get distinct values for filters
function getDistinctValues($conn, $column) {
    $values = [];
    $sql = "SELECT DISTINCT `$column` FROM leaves WHERE `$column` IS NOT NULL AND `$column` <> '' ORDER BY `$column` ASC";
    $result = $conn->query($sql);
    while ($row = $result->fetch_assoc()) {
        $values[] = $row[$column];
    }
    return $values;
}

// Get distinct values for each filter
$statuses = getDistinctValues($conn, "Status");
$languages = getDistinctValues($conn, "Language");
$leaders = getDistinctValues($conn, "LeaderFeedback");
$approvers = getDistinctValues($conn, "Approver");

// Calculate first and last day of current month
$first_day_of_month = date('Y-m-01');
$last_day_of_month = date('Y-m-t');

// Get filter values (with defaults)
$from_filter = isset($_GET['from_filter']) ? $_GET['from_filter'] : $first_day_of_month;
$to_filter = isset($_GET['to_filter']) ? $_GET['to_filter'] : $last_day_of_month;

$status_filter = isset($_GET['status']) ? $_GET['status'] : '';
$language_filter = isset($_GET['language']) ? $_GET['language'] : '';
$leader_filter = isset($_GET['leader_feedback']) ? $_GET['leader_feedback'] : '';
$approver_filter = isset($_GET['approver']) ? $_GET['approver'] : '';
$search_query = isset($_GET['search']) ? $_GET['search'] : '';

// Handle highlight parameter for email links
$highlight_id = isset($_GET['highlight']) ? $_GET['highlight'] : '';
$highlight_email = isset($_GET['email']) ? urldecode($_GET['email']) : '';

// If highlight parameters are present, override other filters to show specific leave request
if (!empty($highlight_id)) {
    // Remove date filters when coming from email link to show all records
    $from_filter = '';
    $to_filter = '';

    // Clear other filters to ensure we see the specific request
    $status_filter = '';
    $language_filter = '';
    $leader_filter = '';
    $approver_filter = '';

    // If email is also provided, search by email, otherwise search by LeavesCode
    if (!empty($highlight_email)) {
        // Clean and decode the email
        $highlight_email = trim(urldecode($highlight_email));
        $search_query = $highlight_email; // Set search to the email
    } else {
        // Search by LeavesCode
        $search_query = $highlight_id;
    }
}

// Build SQL query with filters
$sql = "SELECT LeavesCode, Email, `From`, `To`, TotalDays, `Status`, Approver, WFMComment, requestdate, LeavesName, Attached, Description, Seat, Language, Module, LeaderFeedback, LeaderFeedbackemail, originalshift
        FROM leaves WHERE 1=1";

if (!empty($status_filter)) {
    $sql .= " AND `Status` = '" . $conn->real_escape_string($status_filter) . "'";
}

if (!empty($language_filter)) {
    $sql .= " AND `Language` = '" . $conn->real_escape_string($language_filter) . "'";
}

if (!empty($leader_filter)) {
    $sql .= " AND `LeaderFeedback` = '" . $conn->real_escape_string($leader_filter) . "'";
}

if (!empty($approver_filter)) {
    $sql .= " AND `Approver` = '" . $conn->real_escape_string($approver_filter) . "'";
}

if (!empty($from_filter) && !empty($to_filter)) {
    $sql .= " AND (`From` BETWEEN '" . $conn->real_escape_string($from_filter) . "' AND '" . $conn->real_escape_string($to_filter) . "')";
}

// Add search functionality
if (!empty($search_query)) {
    $search = $conn->real_escape_string($search_query);

    // If this is an email highlight search, prioritize exact email match
    if (!empty($highlight_email) && $search_query === $highlight_email) {
        $sql .= " AND Email = '$search'";
    }
    // If this is a LeavesCode highlight search, prioritize exact LeavesCode match
    elseif (!empty($highlight_id) && empty($highlight_email) && $search_query === $highlight_id) {
        $sql .= " AND LeavesCode = '$search'";
    }
    else {
        $sql .= " AND (
            LeavesCode LIKE '%$search%' OR
            Email LIKE '%$search%' OR
            `From` LIKE '%$search%' OR
            `To` LIKE '%$search%' OR
            TotalDays LIKE '%$search%' OR
            `Status` LIKE '%$search%' OR
            Approver LIKE '%$search%' OR
            WFMComment LIKE '%$search%' OR
            requestdate LIKE '%$search%' OR
            LeavesName LIKE '%$search%' OR
            Attached LIKE '%$search%' OR
            Description LIKE '%$search%' OR
            Seat LIKE '%$search%' OR
            Language LIKE '%$search%' OR
            Module LIKE '%$search%' OR
            LeaderFeedback LIKE '%$search%' OR
            LeaderFeedbackemail LIKE '%$search%'
        )";
    }
}

$result = $conn->query($sql);

// Debug: Log the query when coming from highlight link
if (!empty($highlight_id)) {
    error_log("=== HIGHLIGHT DEBUG ===");
    error_log("Highlight ID: " . $highlight_id);
    error_log("Highlight Email: " . ($highlight_email ?? 'Not provided'));
    error_log("Search Query: " . $search_query);
    error_log("Search Type: " . (!empty($highlight_email) ? 'Email Search' : 'LeavesCode Search'));
    error_log("From Filter: " . $from_filter);
    error_log("To Filter: " . $to_filter);
    error_log("Final SQL: " . $sql);
    error_log("Result count: " . ($result ? $result->num_rows : 'Query failed'));
    error_log("=== END DEBUG ===");
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Leaves Management - Kalam App</title>

    <!-- Essential Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/datatables.net-bs5@1.13.4/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/datatables.net-buttons-bs5@2.3.6/css/buttons.bootstrap5.min.css">
    <!-- Google Fonts replaced with system fonts for CSP compliance -->
    <link rel="stylesheet" href="../assets/css/root-variables.css">
    <link rel="stylesheet" href="../assets/css/simple-professional-leaves.css">

    <!-- Custom styles for permission-based restrictions -->
    <style>
        .readonly-comment {
            background-color: #f8f9fa !important;
            border-color: #dee2e6 !important;
            color: #6c757d !important;
            cursor: not-allowed !important;
        }

        .readonly-indicator {
            color: #6c757d;
            font-size: 14px;
            padding: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .readonly-indicator i {
            margin-right: 4px;
        }

        /* Ensure read-only styling is applied */
        .professional-comment-input.readonly-comment:disabled {
            opacity: 0.7;
        }

        /* Hide action buttons for viewers */
        .viewer-hidden {
            display: none !important;
        }

        /* Dynamic user badge styling */
        .user-badge .badge-text {
            font-weight: 500;
            font-size: 0.85em; /* تصغير الخط */
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 200px;
            display: inline-block;
        }

        /* Responsive adjustments for smaller screens */
        @media (max-width: 768px) {
            .user-badge .badge-text {
                max-width: 150px;
                font-size: 0.8em; /* خط أصغر للتابلت */
            }
        }

        @media (max-width: 480px) {
            .user-badge .badge-text {
                max-width: 120px;
                font-size: 0.75em; /* خط أصغر للموبايل */
            }
        }

        /* Back Button Styling */
        .back-button-container {
            margin-right: 15px;
        }

        .back-button {
            display: inline-flex;
            align-items: center;
            padding: 8px 16px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-size: 0.9em;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .back-button:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            color: white;
            text-decoration: none;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .back-button i {
            margin-right: 6px;
            font-size: 0.9em;
        }

        /* Responsive adjustments for back button */
        @media (max-width: 768px) {
            .back-button {
                padding: 6px 12px;
                font-size: 0.85em;
            }

            .back-button-container {
                margin-right: 10px;
            }
        }

        @media (max-width: 480px) {
            .back-button span {
                display: none; /* إخفاء النص وإظهار الأيقونة فقط */
            }

            .back-button {
                padding: 8px;
                border-radius: 50%;
                width: 36px;
                height: 36px;
                justify-content: center;
            }

            .back-button i {
                margin-right: 0;
            }
        }

        /* Ensure user info section is properly aligned */
        .user-info-section {
            display: flex;
            align-items: center;
        }

        /* Highlighted row styling for email links */
        .highlighted-row {
            background: linear-gradient(135deg, rgba(200, 156, 196, 0.1) 0%, rgba(200, 156, 196, 0.05) 100%) !important;
            border-left: 4px solid #c89cc4 !important;
            box-shadow: 0 2px 8px rgba(200, 156, 196, 0.2) !important;
            animation: highlightPulse 2s ease-in-out !important;
        }

        .highlighted-row:hover {
            background: linear-gradient(135deg, rgba(200, 156, 196, 0.15) 0%, rgba(200, 156, 196, 0.08) 100%) !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(200, 156, 196, 0.25) !important;
        }

        @keyframes highlightPulse {
            0% {
                background: rgba(200, 156, 196, 0.3);
                transform: scale(1.01);
            }
            50% {
                background: rgba(200, 156, 196, 0.15);
                transform: scale(1.005);
            }
            100% {
                background: linear-gradient(135deg, rgba(200, 156, 196, 0.1) 0%, rgba(200, 156, 196, 0.05) 100%);
                transform: scale(1);
            }
        }

        /* Scroll to highlighted row smoothly */
        .highlighted-row {
            scroll-margin-top: 100px;
        }
    </style>

</head>

<body class="professional-body">
    <!-- Notification Container -->
    <div id="notificationContainer" class="notification-container"></div>

    <!-- Professional Navigation Header -->
    <nav class="professional-navbar animate__animated animate__fadeInDown">
        <div class="container-fluid">
            <div class="navbar-content">
                <!-- Logo Section -->
                <div class="navbar-brand-section">
                    <div class="logo-container">
                        <img src="/Logos/kalam.png" alt="Logo" class="professional-logo">
                    </div>
                </div>

                <!-- Page Title Section -->
                <div class="page-title-section">
                    <div class="page-icon">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <div class="page-info">
                        <h2 class="page-title">Leaves Management</h2>
                        <span class="page-description">Leave Request Management System</span>
                    </div>
                </div>

                <!-- User Info Section -->
                <div class="user-info-section">
                    <!-- Back Button -->
                    <div class="back-button-container">
                        <a href="/App/Reports" class="back-button" title="Back to Reports">
                            <i class="fas fa-arrow-left"></i>
                            <span>Back</span>
                        </a>
                    </div>

                    <div class="user-badge">
                        <i class="fas fa-shield-alt me-2"></i>
                        <span class="badge-text" title="Current User: <?= htmlspecialchars($headerDisplayText) ?>"><?= htmlspecialchars($headerDisplayText) ?></span>
                        <div class="badge-indicator"></div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content Container -->
    <div class="professional-container">
        <div class="container-fluid">

            <!-- Professional Filters Panel -->
            <div class="professional-filters-panel animate__animated animate__fadeInUp">
                <div class="filters-header">
                    <div class="filters-title">
                        <i class="fas fa-filter"></i>
                        <h3>Filters</h3>
                    </div>
                    <div class="filters-actions">
                        <button type="button" class="btn-toggle-filters" onclick="toggleFiltersPanel()">
                            <i class="fas fa-chevron-up"></i>
                        </button>
                    </div>
                </div>

                <form method="GET" class="filters-form" id="filtersForm">
                    <div class="filters-grid-compact">
                        <!-- Row 1: Search and Status -->
                        <div class="filter-group">
                            <label class="filter-label">
                                <i class="fas fa-search"></i>
                                Search
                            </label>
                            <input type="text" name="search" class="professional-search-input"
                                   placeholder="Search..."
                                   value="<?= htmlspecialchars($search_query) ?>">
                        </div>

                        <div class="filter-group">
                            <label class="filter-label">
                                <i class="fas fa-check-circle"></i>
                                Status
                            </label>
                            <select name="status" class="professional-select">
                                <option value="">All Statuses</option>
                                <?php foreach ($statuses as $status): ?>
                                    <option value="<?= htmlspecialchars($status) ?>" <?= $status_filter === $status ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($status) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <!-- Row 2: Language and Leader -->
                        <div class="filter-group">
                            <label class="filter-label">
                                <i class="fas fa-language"></i>
                                Language
                            </label>
                            <select name="language" class="professional-select">
                                <option value="">All Languages</option>
                                <?php foreach ($languages as $language): ?>
                                    <option value="<?= htmlspecialchars($language) ?>" <?= $language_filter === $language ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($language) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label class="filter-label">
                                <i class="fas fa-user-tie"></i>
                                Leader
                            </label>
                            <select name="leader_feedback" class="professional-select">
                                <option value="">All Leaders</option>
                                <?php foreach ($leaders as $leader): ?>
                                    <option value="<?= htmlspecialchars($leader) ?>" <?= $leader_filter === $leader ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($leader) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <!-- Row 3: Approver and Date Range -->
                        <div class="filter-group">
                            <label class="filter-label">
                                <i class="fas fa-user-check"></i>
                                Approver
                            </label>
                            <select name="approver" class="professional-select">
                                <option value="">All Approvers</option>
                                <?php foreach ($approvers as $approver): ?>
                                    <option value="<?= htmlspecialchars($approver) ?>" <?= $approver_filter === $approver ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($approver) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="filter-group date-range-group">
                            <label class="filter-label">
                                <i class="fas fa-calendar"></i>
                                Date Range
                            </label>
                            <div class="date-range-inputs">
                                <input type="date" name="from_filter" class="professional-date-input"
                                       value="<?= htmlspecialchars($from_filter) ?>" placeholder="From">
                                <span class="date-separator">to</span>
                                <input type="date" name="to_filter" class="professional-date-input"
                                       value="<?= htmlspecialchars($to_filter) ?>" placeholder="To">
                            </div>
                        </div>
                    </div>

                    <!-- Filter Actions -->
                    <div class="filters-actions-bar">
                        <div class="filters-buttons">
                            <button class="btn-excel-export" onclick="exportToExcel()" title="Export to Excel">
                                <i class="fas fa-file-excel"></i>
                                Export
                            </button>
                            <button type="submit" class="btn-apply-filters">
                                <i class="fas fa-filter"></i>
                                Apply Filters
                            </button>
                            <a href="LeavesRequestspage.php" class="btn-reset-filters">
                                <i class="fas fa-rotate-left"></i>
                                Reset All
                            </a>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Professional Data Table -->
            <div class="professional-table-container animate__animated animate__fadeInUp animate__delay-1s">

                <div class="table-wrapper">
                    <table id="professionalLeavesTable" class="table table-hover professional-table">
                        <thead class="professional-thead">
                            <tr>
                                <th class="th-code">
                                    <div class="th-content">
                                        <i class="fas fa-hashtag"></i>
                                        <span>Request Code</span>
                                    </div>
                                </th>
                                <th class="th-email">
                                    <div class="th-content">
                                        <i class="fas fa-envelope"></i>
                                        <span>Employee Email</span>
                                    </div>
                                </th>
                                <th class="th-date">
                                    <div class="th-content">
                                        <i class="fas fa-calendar-day"></i>
                                        <span>From Date</span>
                                    </div>
                                </th>
                                <th class="th-date">
                                    <div class="th-content">
                                        <i class="fas fa-calendar-day"></i>
                                        <span>To Date</span>
                                    </div>
                                </th>
                                <th class="th-days">
                                    <div class="th-content">
                                        <i class="fas fa-calculator"></i>
                                        <span>Total Days</span>
                                    </div>
                                </th>
                                <th class="th-status">
                                    <div class="th-content">
                                        <i class="fas fa-check-circle"></i>
                                        <span>Status</span>
                                    </div>
                                </th>
                                <th class="th-approver">
                                    <div class="th-content">
                                        <i class="fas fa-user-check"></i>
                                        <span>Approver</span>
                                    </div>
                                </th>
                                <th class="th-comment">
                                    <div class="th-content">
                                        <i class="fas fa-comment"></i>
                                        <span>WFM Comment</span>
                                    </div>
                                </th>
                                <th class="th-request-date">
                                    <div class="th-content">
                                        <i class="fas fa-calendar-plus"></i>
                                        <span>Request Date</span>
                                    </div>
                                </th>
                                <th class="th-leave-type">
                                    <div class="th-content">
                                        <i class="fas fa-tag"></i>
                                        <span>Leave Type</span>
                                    </div>
                                </th>
                                <th class="th-attachment">
                                    <div class="th-content">
                                        <i class="fas fa-paperclip"></i>
                                        <span>Attachment</span>
                                    </div>
                                </th>
                                <th class="th-description">
                                    <div class="th-content">
                                        <i class="fas fa-align-left"></i>
                                        <span>Description</span>
                                    </div>
                                </th>
                                <th class="th-seat">
                                    <div class="th-content">
                                        <i class="fas fa-chair"></i>
                                        <span>Seat</span>
                                    </div>
                                </th>
                                <th class="th-language">
                                    <div class="th-content">
                                        <i class="fas fa-language"></i>
                                        <span>Language</span>
                                    </div>
                                </th>
                                <th class="th-module">
                                    <div class="th-content">
                                        <i class="fas fa-puzzle-piece"></i>
                                        <span>Module</span>
                                    </div>
                                </th>
                                <th class="th-shift">
                                    <div class="th-content">
                                        <i class="fas fa-clock"></i>
                                        <span>Original Shift</span>
                                    </div>
                                </th>
                                <th class="th-leader">
                                    <div class="th-content">
                                        <i class="fas fa-user-tie"></i>
                                        <span>Team Leader</span>
                                    </div>
                                </th>
                                <th class="th-leader-email">
                                    <div class="th-content">
                                        <i class="fas fa-envelope-open-text"></i>
                                        <span>Leader Email</span>
                                    </div>
                                </th>
                                <?php if ($canUseActions): ?>
                                <th class="th-actions">
                                    <div class="th-content">
                                        <i class="fas fa-cogs"></i>
                                        <span>Actions</span>
                                    </div>
                                </th>
                                <?php endif; ?>
                            </tr>
                        </thead>
                        <tbody class="professional-tbody">
                            <?php
                            if ($result->num_rows > 0) {
                                $rowIndex = 0;
                                while($row = $result->fetch_assoc()) {
                                    $rowIndex++;

                                    // Determine status styling
                                    $statusClass = '';
                                    $statusIcon = '';
                                    $statusBadge = '';

                                    if ($row['Status'] == 'Approved') {
                                        $statusClass = 'status-approved';
                                        $statusIcon = 'fas fa-check-circle';
                                        $statusBadge = 'badge-success';
                                    } elseif ($row['Status'] == 'Rejected') {
                                        $statusClass = 'status-rejected';
                                        $statusIcon = 'fas fa-times-circle';
                                        $statusBadge = 'badge-danger';
                                    } else {
                                        $statusClass = 'status-pending';
                                        $statusIcon = 'fas fa-clock';
                                        $statusBadge = 'badge-warning';
                                    }

                                    // Check if this row should be highlighted
                                    $highlightClass = '';
                                    if (!empty($highlight_email) && $row['Email'] === $highlight_email) {
                                        $highlightClass = ' highlighted-row';
                                    }

                                    // Add data attributes for easier JavaScript access
                                    echo "<tr class='professional-row animate__animated animate__fadeInUp$highlightClass' data-email='" . htmlspecialchars($row['Email'] ?? '') . "' data-from='" . htmlspecialchars($row['From'] ?? '') . "' data-to='" . htmlspecialchars($row['To'] ?? '') . "' style='animation-delay: " . ($rowIndex * 0.05) . "s;'>";

                                    // Request Code
                                    echo "<td class='td-code'>
                                            <div class='cell-content'>
                                                <span class='code-badge'>" . htmlspecialchars($row['LeavesCode'] ?? '') . "</span>
                                            </div>
                                          </td>";

                                    // Employee Email
                                    echo "<td class='td-email'>
                                            <div class='cell-content'>
                                                <div class='email-container'>
                                                    <i class='fas fa-user-circle email-icon'></i>
                                                    <span class='email-text'>" . htmlspecialchars($row['Email'] ?? '') . "</span>
                                                </div>
                                            </div>
                                          </td>";

                                    // Format dates better
                                    $fromDate = !empty($row['From']) ? date('d M Y', strtotime($row['From'])) : '';
                                    $toDate = !empty($row['To']) ? date('d M Y', strtotime($row['To'])) : '';

                                    // From Date
                                    echo "<td class='td-date'>
                                            <div class='cell-content'>
                                                <div class='date-container'>
                                                    <i class='fas fa-calendar-day date-icon'></i>
                                                    <span class='date-text'>" . htmlspecialchars($fromDate) . "</span>
                                                </div>
                                            </div>
                                          </td>";

                                    // To Date
                                    echo "<td class='td-date'>
                                            <div class='cell-content'>
                                                <div class='date-container'>
                                                    <i class='fas fa-calendar-day date-icon'></i>
                                                    <span class='date-text'>" . htmlspecialchars($toDate) . "</span>
                                                </div>
                                            </div>
                                          </td>";

                                    // Total Days
                                    echo "<td class='td-days'>
                                            <div class='cell-content'>
                                                <div class='days-badge'>
                                                    <i class='fas fa-calculator'></i>
                                                    <span>" . htmlspecialchars($row['TotalDays'] ?? '') . "</span>
                                                </div>
                                            </div>
                                          </td>";

                                    // Status
                                    echo "<td class='td-status'>
                                            <div class='cell-content'>
                                                <div class='status-badge " . $statusBadge . "'>
                                                    <i class='" . $statusIcon . "'></i>
                                                    <span>" . htmlspecialchars($row['Status'] ?? '') . "</span>
                                                </div>
                                            </div>
                                          </td>";

                                    // Approver
                                    echo "<td class='td-approver'>
                                            <div class='cell-content'>
                                                <div class='approver-info'>";
                                    if (!empty($row['Approver'])) {
                                        echo "<i class='fas fa-user-check approver-icon'></i>
                                              <span class='approver-name'>" . htmlspecialchars($row['Approver']) . "</span>";
                                    } else {
                                        echo "<i class='fas fa-user-clock pending-icon'></i>
                                              <span class='pending-text'>Pending</span>";
                                    }
                                    echo "      </div>
                                            </div>
                                          </td>";

                                    // WFM Comment - Different rendering based on permissions
                                    if ($canEditComments) {
                                        // Editable for Admin, Super Admin, Editor
                                        echo "<td class='td-comment'>
                                                <div class='cell-content'>
                                                    <div class='comment-container'>
                                                        <input type='text' id='wfmComment_" . $row['LeavesCode'] . "'
                                                               class='professional-comment-input'
                                                               value='" . htmlspecialchars($row['WFMComment'] ?? '') . "'
                                                               placeholder='Add your comment...'
                                                               title='WFM Comment' />
                                                        <div class='comment-actions'>
                                                            <button class='btn-save-comment' onclick='saveComment(\"" . $row['LeavesCode'] . "\")' title='Save Comment'>
                                                                <i class='fas fa-save'></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                              </td>";
                                    } else {
                                        // Read-only for Manager, Leader, Viewer
                                        echo "<td class='td-comment'>
                                                <div class='cell-content'>
                                                    <div class='comment-container'>
                                                        <input type='text' id='wfmComment_" . $row['LeavesCode'] . "'
                                                               class='professional-comment-input readonly-comment'
                                                               value='" . htmlspecialchars($row['WFMComment'] ?? '') . "'
                                                               placeholder='No comment'
                                                               title='WFM Comment (Read Only)'
                                                               readonly disabled />
                                                        <div class='comment-actions'>
                                                            <span class='readonly-indicator' title='Read Only Access'>
                                                                <i class='fas fa-lock'></i>
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                              </td>";
                                    }

                                    // Format request date better
                                    $requestDate = !empty($row['requestdate']) ? date('d M Y', strtotime($row['requestdate'])) : '';
                                    echo "<td class='td-request-date'>
                                            <div class='cell-content'>
                                                <div class='date-container'>
                                                    <i class='fas fa-calendar-plus date-icon'></i>
                                                    <span class='date-text'>" . htmlspecialchars($requestDate) . "</span>
                                                </div>
                                            </div>
                                          </td>";

                                    // Leave Type
                                    echo "<td class='td-leave-type'>
                                            <div class='cell-content'>
                                                <div class='leave-type-badge'>" . htmlspecialchars($row['LeavesName'] ?? '') . "</div>
                                            </div>
                                          </td>";

                                    // Attachment
                                    echo "<td class='td-attachment'>
                                            <div class='cell-content'>";
                                    if (!empty($row['Attached'])) {
                                        // إصلاح مسار المرفق - استخدام المسار المطلق من الجذر
                                        $attachmentPath = $row['Attached'];

                                        // إذا كان المسار يحتوي على uploads/ فهو مسار كامل قديم
                                        if (strpos($attachmentPath, 'uploads/') !== false) {
                                            // استخدام المسار من الجذر مباشرة
                                            $finalPath = '/' . ltrim($attachmentPath, '/');
                                        } else {
                                            // إذا كان اسم ملف فقط، إنشاء المسار الكامل من الجذر
                                            $finalPath = '/uploads/' . basename($attachmentPath);
                                        }

                                        echo "<a href='" . htmlspecialchars($finalPath) . "' target='_blank' class='attachment-link'>
                                                <i class='fas fa-file-download'></i>
                                                <span>View File</span>
                                              </a>";
                                    } else {
                                        echo "<div class='no-attachment'>
                                                <i class='fas fa-file-slash'></i>
                                                <span>No File</span>
                                              </div>";
                                    }
                                    echo "    </div>
                                          </td>";

                                    // Description
                                    echo "<td class='td-description'>
                                            <div class='cell-content'>";
                                    if (!empty($row['Description'])) {
                                        $desc = htmlspecialchars($row['Description'] ?? '');
                                        if (strlen($desc) > 40) {
                                            echo "<div class='description-text' title='" . $desc . "'>
                                                    <i class='fas fa-align-left'></i>
                                                    <span>" . substr($desc, 0, 40) . "...</span>
                                                  </div>";
                                        } else {
                                            echo "<div class='description-text'>
                                                    <i class='fas fa-align-left'></i>
                                                    <span>" . $desc . "</span>
                                                  </div>";
                                        }
                                    } else {
                                        echo "<div class='no-description'>
                                                <i class='fas fa-minus'></i>
                                                <span>No Description</span>
                                              </div>";
                                    }
                                    echo "    </div>
                                          </td>";

                                    // Seat, Language, Module, Shift, Leader Info
                                    echo "<td class='td-seat'><div class='cell-content'><span class='info-text'>" . htmlspecialchars($row['Seat'] ?? '-') . "</span></div></td>";
                                    echo "<td class='td-language'><div class='cell-content'><span class='info-text'>" . htmlspecialchars($row['Language'] ?? '-') . "</span></div></td>";
                                    echo "<td class='td-module'><div class='cell-content'><span class='info-text'>" . htmlspecialchars($row['Module'] ?? '-') . "</span></div></td>";
                                    echo "<td class='td-shift'><div class='cell-content'><span class='info-text'>" . htmlspecialchars($row['originalshift'] ?? '-') . "</span></div></td>";
                                    echo "<td class='td-leader'><div class='cell-content'><span class='info-text'>" . htmlspecialchars($row['LeaderFeedback'] ?? '-') . "</span></div></td>";
                                    echo "<td class='td-leader-email'><div class='cell-content'><span class='info-text'>" . htmlspecialchars($row['LeaderFeedbackemail'] ?? '-') . "</span></div></td>";

                                    // Professional Action buttons - Only for users with action permissions
                                    if ($canUseActions) {
                                        $isManagerOrLeader = in_array($access, ['Manager', 'Leader']);
                                        $approveTitle = $isManagerOrLeader ? 'Record Approval Recommendation (Manager/Leader)' : 'Approve Request';
                                        $rejectTitle = $isManagerOrLeader ? 'Record Rejection Recommendation (Manager/Leader)' : 'Reject Request';
                                        $approveText = $isManagerOrLeader ? 'Recommend' : 'Approve';
                                        $rejectText = $isManagerOrLeader ? 'Not Recommend' : 'Reject';

                                        echo "<td class='td-actions'>
                                                <div class='cell-content'>
                                                    <div class='actions-container'>
                                                        <button class='btn-action btn-approve'
                                                                data-leavescode='" . $row['LeavesCode'] . "'
                                                                onclick='updateStatus(this, \"Approved\")'
                                                                title='" . $approveTitle . "'>
                                                            <i class='fas fa-check'></i>
                                                            <span>" . $approveText . "</span>
                                                        </button>
                                                        <button class='btn-action btn-reject'
                                                                data-leavescode='" . $row['LeavesCode'] . "'
                                                                onclick='updateStatus(this, \"Rejected\")'
                                                                title='" . $rejectTitle . "'>
                                                            <i class='fas fa-times'></i>
                                                            <span>" . $rejectText . "</span>
                                                        </button>
                                                        <div class='action-loading d-none'>
                                                            <i class='fas fa-spinner fa-spin'></i>
                                                        </div>
                                                    </div>
                                                </div>
                                              </td>";
                                    }
                                    echo "</tr>";
                                }
                            } else {
                                // Special message when coming from highlight link
                                if (!empty($highlight_id)) {
                                    if (!empty($highlight_email)) {
                                        // Email search
                                        echo "<tr class='no-data-row'>
                                                <td colspan='19' class='no-data-cell'>
                                                    <div class='no-data-content'>
                                                        <i class='fas fa-search'></i>
                                                        <h4>No Leave Requests Found for " . htmlspecialchars($highlight_email) . "</h4>
                                                        <p>The email link you followed is looking for leave requests from this email address, but none were found in the database.</p>
                                                        <p><strong>Possible reasons:</strong></p>
                                                        <ul style='text-align: left; display: inline-block;'>
                                                            <li>The leave request may have been deleted</li>
                                                            <li>The email address might be different</li>
                                                            <li>The request might be in a different system</li>
                                                        </ul>
                                                        <button onclick='clearEmailHighlight()' class='btn btn-primary mt-3'>
                                                            <i class='fas fa-list'></i> Show All Leave Requests
                                                        </button>
                                                    </div>
                                                </td>
                                              </tr>";
                                    } else {
                                        // LeavesCode search
                                        echo "<tr class='no-data-row'>
                                                <td colspan='19' class='no-data-cell'>
                                                    <div class='no-data-content'>
                                                        <i class='fas fa-search'></i>
                                                        <h4>No Leave Request Found with Code: " . htmlspecialchars($highlight_id) . "</h4>
                                                        <p>The link you followed is looking for a specific leave request, but it was not found in the database.</p>
                                                        <p><strong>Possible reasons:</strong></p>
                                                        <ul style='text-align: left; display: inline-block;'>
                                                            <li>The leave request may have been deleted</li>
                                                            <li>The request code might be incorrect</li>
                                                            <li>The request might be in a different system</li>
                                                        </ul>
                                                        <button onclick='clearEmailHighlight()' class='btn btn-primary mt-3'>
                                                            <i class='fas fa-list'></i> Show All Leave Requests
                                                        </button>
                                                    </div>
                                                </td>
                                              </tr>";
                                    }
                                } else {
                                    echo "<tr class='no-data-row'>
                                            <td colspan='19' class='no-data-cell'>
                                                <div class='no-data-content'>
                                                    <i class='fas fa-inbox'></i>
                                                    <h4>No Leave Requests Found</h4>
                                                    <p>There are currently no leave requests matching your criteria.</p>
                                                </div>
                                            </td>
                                          </tr>";
                                }
                            }
                            ?>
                        </tbody>
                    </table>
                </div>
            </div>



        </div>
    </div>

    <!-- Professional Floating Actions -->
    <div class="professional-floating-actions">
        <div class="floating-action-group">
            <button class="floating-btn floating-btn-primary" onclick="scrollToTop()" title="Scroll to Top">
                <i class="fas fa-arrow-up"></i>
            </button>
        </div>
    </div>

    <!-- Essential JavaScript Libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/datatables.net@1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/datatables.net-bs5@1.13.4/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/datatables.net-buttons@2.3.6/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/datatables.net-buttons-bs5@2.3.6/js/buttons.bootstrap5.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/datatables.net-buttons@2.3.6/js/buttons.html5.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>

    <script>
    // User role information for frontend
    const currentUserRole = '<?= $access ?>';
    const isManagerOrLeader = ['Manager', 'Leader'].includes(currentUserRole);
    const canEditComments = <?= $canEditComments ? 'true' : 'false' ?>;
    const canUseActions = <?= $canUseActions ? 'true' : 'false' ?>;
    const isViewer = <?= $isViewer ? 'true' : 'false' ?>;

    // Simple Configuration
    $(document).ready(function() {
        initializeDataTable();
        enhanceTableScrolling();
        initializePermissionBasedUI();
    });

    // Initialize UI based on user permissions
    function initializePermissionBasedUI() {
        // Disable comment editing for restricted users
        if (!canEditComments) {
            $('.professional-comment-input').not('.readonly-comment').each(function() {
                $(this).prop('readonly', true).prop('disabled', true);
                $(this).addClass('readonly-comment');
                $(this).attr('title', 'WFM Comment (Read Only)');
            });

            // Hide save buttons for restricted users
            $('.btn-save-comment').hide();
        }

        // Add visual indicators for role-based restrictions
        if (isViewer) {
            console.log('Viewer mode: Actions column hidden, all editing disabled');
        } else if (!canEditComments) {
            console.log('Manager/Leader mode: Comment editing disabled, actions available');
        }
    }

    // Initialize DataTable
    function initializeDataTable() {
        if ($.fn.DataTable.isDataTable('#professionalLeavesTable')) {
            $('#professionalLeavesTable').DataTable().destroy();
        }

        const table = $('#professionalLeavesTable').DataTable({
            responsive: false,
            pageLength: 400,
            lengthMenu: [[400, 800, 1200, -1], [400, 800, 1200, "All"]],
            dom: 'Brtip',
            scrollX: true,
            scrollY: false,
            paging: true,
            searching: true, // Enable for programmatic filtering
            lengthChange: false,
            fixedHeader: false,
            autoWidth: false,
            columnDefs: (function() {
                let defs = [
                    { targets: 0, width: '120px' },   // Code
                    { targets: 1, width: '180px' },   // Email
                    { targets: 2, width: '110px' },   // From Date
                    { targets: 3, width: '110px' },   // To Date
                    { targets: 4, width: '70px' },    // Days
                    { targets: 5, width: '110px' },   // Status
                    { targets: 6, width: '130px' },   // Approver
                    { targets: 7, width: '180px' },   // Comment
                    { targets: 8, width: '110px' },   // Request Date
                    { targets: 9, width: '120px' },   // Leave Type
                    { targets: 10, width: '90px' },   // Attachment
                    { targets: 11, width: '140px' },  // Description
                    { targets: 12, width: '70px' },   // Seat
                    { targets: 13, width: '90px' },   // Language
                    { targets: 14, width: '90px' },   // Module
                    { targets: 15, width: '110px' },  // Shift
                    { targets: 16, width: '120px' },  // Leader
                    { targets: 17, width: '160px' }   // Leader Email
                ];

                // Add Actions column definition only if user can use actions
                if (canUseActions) {
                    defs.push({ targets: 18, width: '180px', orderable: false });  // Actions
                }

                return defs;
            })(),
            buttons: [
                {
                    extend: 'excel',
                    text: '<i class="fas fa-file-excel"></i> Excel',
                    className: 'btn btn-success btn-sm d-none',
                    exportOptions: {
                        columns: ':visible:not(.no-export)'
                    }
                }
            ],
            language: {
                search: "Search:",
                lengthMenu: "Show _MENU_ entries",
                info: "Showing _START_ to _END_ of _TOTAL_ entries",
                paginate: {
                    first: "First",
                    last: "Last",
                    next: "Next",
                    previous: "Previous"
                },
                emptyTable: "No leave requests found",
                zeroRecords: "No matching records found"
            },

            order: [[8, 'desc']],
            initComplete: function() {
                console.log('Table loaded successfully');
                // Force header visibility
                $('.professional-thead').show();
                $('.th-content').show();
            },
            drawCallback: function() {
                // Ensure header remains visible after each draw
                $('.professional-thead').show();
                $('.th-content').show();
            }
        });

        // Move DataTable buttons to our custom location
        table.buttons().container().appendTo('.table-actions');

        return table;
    }



    // Professional Status Update Function
    function updateStatus(button, status) {
        // Check if user has permission to use actions
        if (!canUseActions) {
            showNotification('You do not have permission to perform this action', 'error', 'Access Denied');
            return;
        }

        // Show different confirmation messages based on user role
        let confirmMessage = '';
        if (isManagerOrLeader) {
            const actionType = status === 'Approved' ? 'recommendation' : 'non-recommendation';
            confirmMessage = `Record ${actionType} feedback for this leave request?`;
        } else {
            confirmMessage = `${status === 'Approved' ? 'Approve' : 'Reject'} this leave request?`;
        }

        // Optional: Add confirmation for important actions
        // if (!confirm(confirmMessage)) return;

        // Get request data
        const leavesCode = button.getAttribute('data-leavescode');
        const commentInput = document.getElementById('wfmComment_' + leavesCode);
        const comment = commentInput ? commentInput.value : '';
        const row = button.closest('tr');
        const email = row.getAttribute('data-email');
        const fromDate = row.getAttribute('data-from');

        // Show professional loading state
        const actionsContainer = button.closest('.actions-container');
        const loadingElement = actionsContainer.querySelector('.action-loading');
        const actionButtons = actionsContainer.querySelectorAll('.btn-action');

        // Disable buttons and show loading
        actionButtons.forEach(btn => {
            btn.disabled = true;
            btn.style.opacity = '0.6';
        });
        loadingElement.classList.remove('d-none');

        // Send AJAX request
        $.ajax({
            url: window.location.href,
            type: 'POST',
            data: {
                LeavesCode: leavesCode,
                Status: status,
                Email: email,
                From: fromDate,
                WFMComment: comment
            },
            success: function(response) {
                // Hide loading and enable buttons
                loadingElement.classList.add('d-none');
                actionButtons.forEach(btn => {
                    btn.disabled = false;
                    btn.style.opacity = '1';
                });

                // Try to parse JSON response first (for Manager/Leader actions)
                let parsedResponse;
                try {
                    parsedResponse = JSON.parse(response);
                } catch (e) {
                    // If not JSON, treat as legacy string response
                    parsedResponse = null;
                }

                if (parsedResponse && parsedResponse.action === 'leader_feedback') {
                    // Handle Manager/Leader feedback response
                    if (parsedResponse.status === 'success') {
                        // Update LeaderFeedback columns (Team Leader column is 17th, Leader Email is 18th)
                        const leaderFeedbackCell = row.cells[16]; // Team Leader column (LeaderFeedback)
                        const leaderEmailCell = row.cells[17]; // Leader Email column (LeaderFeedbackemail)

                        if (leaderFeedbackCell) {
                            const feedbackSpan = leaderFeedbackCell.querySelector('.info-text');
                            if (feedbackSpan) {
                                feedbackSpan.textContent = status;
                            }
                        }
                        if (leaderEmailCell) {
                            const emailSpan = leaderEmailCell.querySelector('.info-text');
                            if (emailSpan) {
                                emailSpan.textContent = '<?= $_SESSION['username'] ?>';
                            }
                        }

                        // Show success notification for leader feedback
                        const feedbackType = status === 'Approved' ? 'Recommendation' : 'Non-Recommendation';
                        const actionMessage = `${feedbackType} feedback recorded successfully`;
                        const actionTitle = 'Leader Feedback Recorded';
                        showNotification(actionMessage, 'info', actionTitle);
                    } else {
                        showNotification('Error recording leader feedback: ' + parsedResponse.message, 'error');
                    }
                } else if (response.trim() === 'success') {
                    // Handle traditional admin/super admin/editor response
                    // Update status cell
                    const statusCell = row.cells[5];
                    statusCell.innerHTML = '<span class="' + (status === 'Approved' ? 'leaves-status-approved' : 'leaves-status-rejected') + '">' + status + '</span>';

                    // Update approver cell
                    const approverCell = row.cells[6];
                    approverCell.textContent = '<?= $_SESSION['username'] ?>';

                    // Show success notification
                    const actionMessage = status === 'Approved' ? 'Leave request approved successfully' : 'Leave request rejected successfully';
                    const actionTitle = status === 'Approved' ? 'Request Approved' : 'Request Rejected';
                    const actionType = status === 'Approved' ? 'success' : 'warning';

                    showNotification(actionMessage, actionType, actionTitle);
                } else {
                    showNotification('Error updating leave request: ' + response, 'error');
                }
            },
            error: function(xhr, status, error) {
                // Hide loading and enable buttons
                loadingElement.classList.add('d-none');
                actionButtons.forEach(btn => {
                    btn.disabled = false;
                    btn.style.opacity = '1';
                });

                showNotification('Network error: ' + error, 'error');
            }
        });
    }

    // Simple comment save with permission check
    function saveComment(leavesCode) {
        // Check if user has permission to edit comments
        if (!canEditComments) {
            showNotification('You do not have permission to edit WFM Comments', 'error', 'Access Denied');
            return;
        }

        const commentInput = document.getElementById('wfmComment_' + leavesCode);
        const comment = commentInput.value.trim();

        $.ajax({
            url: window.location.href,
            type: 'POST',
            data: {
                LeavesCode: leavesCode,
                WFMComment: comment,
                SaveCommentOnly: true
            },
            success: function(response) {
                // Try to parse JSON response for error handling
                try {
                    const parsedResponse = JSON.parse(response);
                    if (parsedResponse.status === 'error') {
                        showNotification(parsedResponse.message, 'error', 'Permission Error');
                        return;
                    }
                } catch (e) {
                    // If not JSON, treat as legacy string response
                }

                // Comment save notifications are handled by the event listener
                // in addNotificationToButtons function
            },
            error: function(xhr, status, error) {
                showNotification('Failed to save comment: ' + error, 'error', 'Save Error');
            }
        });
    }

    // Simple export functions
    function exportToExcel() {
        const table = $('#professionalLeavesTable').DataTable();
        table.button('.buttons-excel').trigger();
    }

    function scrollToTop() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    }

    // Simple functions
    function toggleFiltersPanel() {
        const filtersForm = $('.filters-form');
        filtersForm.toggle();
    }









    // Simple scroll enhancement
    function enhanceTableScrolling() {
        const tableWrapper = document.querySelector('.table-wrapper');
        if (tableWrapper) {
            tableWrapper.addEventListener('wheel', function(e) {
                if (e.shiftKey) {
                    e.preventDefault();
                    this.scrollLeft += e.deltaY;
                }
            });
        }
    }

    // ==================== NOTIFICATION SYSTEM ====================
    function showNotification(message, type = 'info', title = null, duration = 4000) {
        const container = document.getElementById('notificationContainer');
        if (!container) return;

        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;

        // Set notification ID for tracking
        const notificationId = 'notification_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        notification.id = notificationId;

        // Get icon based on type
        let icon = '';
        let defaultTitle = '';
        switch(type) {
            case 'success':
                icon = 'fas fa-check';
                defaultTitle = 'Success';
                break;
            case 'error':
                icon = 'fas fa-times';
                defaultTitle = 'Error';
                break;
            case 'warning':
                icon = 'fas fa-exclamation-triangle';
                defaultTitle = 'Warning';
                break;
            case 'info':
            default:
                icon = 'fas fa-info';
                defaultTitle = 'Information';
                break;
        }

        // Use provided title or default
        const notificationTitle = title || defaultTitle;

        // Create notification HTML
        notification.innerHTML = `
            <div class="notification-icon">
                <i class="${icon}"></i>
            </div>
            <div class="notification-content">
                <div class="notification-title">${notificationTitle}</div>
                <div class="notification-message">${message}</div>
            </div>
            <button class="notification-close" onclick="closeNotification('${notificationId}')">
                <i class="fas fa-times"></i>
            </button>
            <div class="notification-progress"></div>
        `;

        // Add to container
        container.appendChild(notification);

        // Show notification with animation
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);

        // Auto remove after duration
        setTimeout(() => {
            closeNotification(notificationId);
        }, duration);

        return notificationId;
    }

    function closeNotification(notificationId) {
        const notification = document.getElementById(notificationId);
        if (notification) {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 400);
        }
    }

    function clearAllNotifications() {
        const container = document.getElementById('notificationContainer');
        if (container) {
            const notifications = container.querySelectorAll('.notification');
            notifications.forEach(notification => {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 400);
            });
        }
    }

    // Add notifications to common actions
    function addNotificationToButtons() {
        // Excel Export Button - notification handled separately
        const excelBtn = document.querySelector('.btn-excel-export');
        if (excelBtn) {
            excelBtn.addEventListener('click', function() {
                setTimeout(() => {
                    showNotification('Excel file exported successfully', 'success', 'Export Complete');
                }, 500); // Small delay to ensure export completes
            });
        }

        // Filter Apply Button
        const applyBtn = document.querySelector('.btn-apply-filters');
        if (applyBtn) {
            applyBtn.addEventListener('click', function() {
                showNotification('Filters applied successfully', 'success', 'Filters Updated');
            });
        }

        // Filter Reset Button
        const resetBtn = document.querySelector('.btn-reset-filters');
        if (resetBtn) {
            resetBtn.addEventListener('click', function() {
                showNotification('All filters have been reset', 'info', 'Filters Reset');
            });
        }

        // Save Comment Buttons
        document.addEventListener('click', function(e) {
            if (e.target.closest('.btn-save-comment')) {
                showNotification('Comment saved successfully', 'success', 'Comment Updated');
            }
        });
    }

    // Initialize notifications when page loads
    $(document).ready(function() {
        addNotificationToButtons();
        highlightRowFromURL();
    });

    // Filter and highlight specific row if highlight parameter exists
    function highlightRowFromURL() {
        const urlParams = new URLSearchParams(window.location.search);
        const highlightCode = urlParams.get('highlight');

        if (highlightCode) {
            // Wait for table to load
            setTimeout(() => {
                // Use DataTable search to filter for the specific code
                const table = $('#professionalLeavesTable').DataTable();

                // Apply search filter to show only the highlighted row
                table.search(highlightCode).draw();

                // Wait for filter to apply, then highlight the row
                setTimeout(() => {
                    const visibleRows = document.querySelectorAll('#professionalLeavesTable tbody tr:not(.odd):not(.even)');
                    const allRows = document.querySelectorAll('#professionalLeavesTable tbody tr');

                    allRows.forEach(row => {
                        const codeCell = row.querySelector('td:first-child .code-badge');
                        if (codeCell && codeCell.textContent.trim() === highlightCode) {
                            // Highlight the filtered row
                            row.style.backgroundColor = 'var(--success-100)';
                            row.style.border = '2px solid var(--success-500)';
                            row.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';

                            // Scroll to the row
                            row.scrollIntoView({
                                behavior: 'smooth',
                                block: 'center'
                            });
                        }
                    });

                    // Show notification with option to clear filter
                    const notificationId = showNotification(
                        `Showing leave request: ${highlightCode}. <br><button onclick="clearHighlightFilter()" style="background: var(--primary-500); color: white; border: none; padding: 4px 8px; border-radius: 4px; margin-top: 5px; cursor: pointer;">Show All Records</button>`,
                        'info',
                        'Filtered View',
                        0 // Don't auto-hide
                    );

                    // Store notification ID for later use
                    window.currentHighlightNotification = notificationId;

                }, 500);
            }, 1000);
        }
    }

    // Function to clear highlight filter
    function clearHighlightFilter() {
        const table = $('#professionalLeavesTable').DataTable();

        // Clear search filter
        table.search('').draw();

        // Remove highlight styling from all rows
        const allRows = document.querySelectorAll('#professionalLeavesTable tbody tr');
        allRows.forEach(row => {
            row.style.backgroundColor = '';
            row.style.border = '';
            row.style.boxShadow = '';
        });

        // Close the current notification
        if (window.currentHighlightNotification) {
            closeNotification(window.currentHighlightNotification);
        }

        // Show success notification
        showNotification('All records are now visible', 'success', 'Filter Cleared');

        // Remove highlight parameter from URL
        const url = new URL(window.location);
        url.searchParams.delete('highlight');
        url.searchParams.delete('email');
        window.history.replaceState({}, document.title, url);
    }

    // Handle email highlight from email links
    document.addEventListener('DOMContentLoaded', function() {
        const urlParams = new URLSearchParams(window.location.search);
        const highlightId = urlParams.get('highlight');
        const highlightEmail = urlParams.get('email');

        if (highlightId && highlightEmail) {
            // Wait for the page to fully load
            setTimeout(function() {
                const highlightedRow = document.querySelector('.highlighted-row');
                if (highlightedRow) {
                    // Scroll to the highlighted row
                    highlightedRow.scrollIntoView({
                        behavior: 'smooth',
                        block: 'center'
                    });

                    // Show notification
                    showNotification(
                        `Showing leave request for: ${highlightEmail}<br><button onclick="clearEmailHighlight()" style="background: #c89cc4; color: white; border: none; padding: 4px 8px; border-radius: 4px; margin-top: 5px; cursor: pointer;">Show All Records</button>`,
                        'info',
                        'Email Filter Active',
                        0 // Don't auto-hide
                    );
                }
            }, 1000);
        }
    });

    // Function to clear email highlight
    function clearEmailHighlight() {
        // Remove highlight and email parameters from URL
        const url = new URL(window.location);
        url.searchParams.delete('highlight');
        url.searchParams.delete('email');
        url.searchParams.delete('search'); // Also clear search if it was set by highlight

        // Reload page without parameters
        window.location.href = url.toString();
    }
    </script>
</body>
</html>
