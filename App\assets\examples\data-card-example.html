<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Card Design Example</title>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    
    <!-- Bootstrap (Optional) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Unified Tables CSS -->
    <link rel="stylesheet" href="../css/root-variables.css">
    <link rel="stylesheet" href="../css/unified-tables.css">
    
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f8f9fa;
            padding: 20px;
        }
        .example-section {
            margin-bottom: 40px;
        }
        .example-title {
            color: #6c5ce7;
            margin-bottom: 20px;
            border-bottom: 2px solid #6c5ce7;
            padding-bottom: 10px;
        }
        .code-example {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #6c5ce7;
            margin: 15px 0;
        }
        .code-example pre {
            margin: 0;
            font-size: 13px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <h1 class="text-center mb-5">🎨 تصميم Data Card الموحد - أمثلة تطبيقية</h1>
        
        <!-- Example 1: Basic Data Card -->
        <div class="example-section">
            <h2 class="example-title">1. Data Card أساسي</h2>
            
            <div class="code-example">
                <strong>HTML Structure:</strong>
                <pre><code>&lt;div class="data-card"&gt;
    &lt;div class="card-header"&gt;
        &lt;h5&gt;&lt;i class="fas fa-users me-2"&gt;&lt;/i&gt; Users Management&lt;/h5&gt;
    &lt;/div&gt;
    &lt;div class="card-body p-0"&gt;
        &lt;div class="unified-table-responsive"&gt;
            &lt;table class="unified-table"&gt;
                &lt;!-- Table content --&gt;
            &lt;/table&gt;
        &lt;/div&gt;
    &lt;/div&gt;
&lt;/div&gt;</code></pre>
            </div>
            
            <div class="data-card">
                <div class="card-header">
                    <h5><i class="fas fa-users me-2"></i> Users Management</h5>
                </div>
                <div class="card-body p-0">
                    <div class="unified-table-responsive">
                        <table class="unified-table">
                            <thead>
                                <tr>
                                    <th><i class="fas fa-hashtag"></i>ID</th>
                                    <th><i class="fas fa-user"></i>Name</th>
                                    <th><i class="fas fa-envelope"></i>Email</th>
                                    <th><i class="fas fa-check-circle"></i>Status</th>
                                    <th><i class="fas fa-cogs"></i>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><span class="employee-code">001</span></td>
                                    <td>أحمد محمد</td>
                                    <td><EMAIL></td>
                                    <td><span class="status-badge status-active">Active</span></td>
                                    <td>
                                        <div class="table-actions">
                                            <a href="#" class="btn-table-action btn-edit" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="#" class="btn-table-action btn-view" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><span class="employee-code">002</span></td>
                                    <td>فاطمة علي</td>
                                    <td><EMAIL></td>
                                    <td><span class="status-badge status-inactive">Inactive</span></td>
                                    <td>
                                        <div class="table-actions">
                                            <a href="#" class="btn-table-action btn-edit" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="#" class="btn-table-action btn-archive" title="Archive">
                                                <i class="fas fa-archive"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Example 2: Data Card with Filters -->
        <div class="example-section">
            <h2 class="example-title">2. Data Card مع فلاتر</h2>
            
            <div class="data-card">
                <div class="card-header">
                    <h5><i class="fas fa-calendar-check me-2"></i> Leave Requests Management</h5>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <div class="table-filters">
                        <div class="filter-group">
                            <label>Department</label>
                            <select>
                                <option value="">All Departments</option>
                                <option value="it">IT</option>
                                <option value="hr">HR</option>
                                <option value="finance">Finance</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>Status</label>
                            <select>
                                <option value="">All Status</option>
                                <option value="pending">Pending</option>
                                <option value="approved">Approved</option>
                                <option value="rejected">Rejected</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>Date From</label>
                            <input type="date">
                        </div>
                        <div class="filter-group">
                            <label>Date To</label>
                            <input type="date">
                        </div>
                    </div>

                    <!-- Table -->
                    <div class="unified-table-responsive">
                        <table class="unified-table">
                            <thead>
                                <tr>
                                    <th><i class="fas fa-hashtag"></i>Code</th>
                                    <th><i class="fas fa-user"></i>Employee</th>
                                    <th><i class="fas fa-calendar-day"></i>From</th>
                                    <th><i class="fas fa-calendar-day"></i>To</th>
                                    <th><i class="fas fa-calculator"></i>Days</th>
                                    <th><i class="fas fa-check-circle"></i>Status</th>
                                    <th><i class="fas fa-cogs"></i>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>LV001</td>
                                    <td>محمد أحمد</td>
                                    <td>2024-02-01</td>
                                    <td>2024-02-05</td>
                                    <td>5</td>
                                    <td><span class="status-badge status-pending">Pending</span></td>
                                    <td>
                                        <div class="table-actions">
                                            <button class="btn-table-action btn-approved" title="Approve">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button class="btn-table-action btn-rejected" title="Reject">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>LV002</td>
                                    <td>سارة محمود</td>
                                    <td>2024-01-20</td>
                                    <td>2024-01-22</td>
                                    <td>3</td>
                                    <td><span class="status-badge status-approved">Approved</span></td>
                                    <td>
                                        <div class="table-actions">
                                            <button class="btn-table-action btn-view" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Example 3: Data Card with Footer -->
        <div class="example-section">
            <h2 class="example-title">3. Data Card مع Footer</h2>
            
            <div class="data-card">
                <div class="card-header">
                    <h5><i class="fas fa-archive me-2"></i> Archived Users</h5>
                </div>
                <div class="card-body p-0">
                    <div class="unified-table-responsive">
                        <table class="unified-table">
                            <thead>
                                <tr>
                                    <th><i class="fas fa-user"></i>Name</th>
                                    <th><i class="fas fa-envelope"></i>Email</th>
                                    <th><i class="fas fa-calendar"></i>Archived Date</th>
                                    <th><i class="fas fa-cogs"></i>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>علي حسن</td>
                                    <td><EMAIL></td>
                                    <td><span class="table-date">2024-01-15</span></td>
                                    <td>
                                        <div class="table-actions">
                                            <a href="#" class="btn-table-action btn-view" title="Restore">
                                                <i class="fas fa-undo"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>نور أحمد</td>
                                    <td><EMAIL></td>
                                    <td><span class="table-date">2024-01-10</span></td>
                                    <td>
                                        <div class="table-actions">
                                            <a href="#" class="btn-table-action btn-view" title="Restore">
                                                <i class="fas fa-undo"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer">
                    <i class="fas fa-info-circle me-1"></i>
                    Total archived users: <strong>2</strong> | Last updated: <strong>2024-01-20</strong>
                </div>
            </div>
        </div>

        <!-- Example 4: Multiple Data Cards -->
        <div class="example-section">
            <h2 class="example-title">4. عدة Data Cards</h2>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="data-card">
                        <div class="card-header">
                            <h5><i class="fas fa-chart-bar me-2"></i> Statistics</h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-4">
                                    <h3 class="text-primary">150</h3>
                                    <small>Total Users</small>
                                </div>
                                <div class="col-4">
                                    <h3 class="text-success">45</h3>
                                    <small>Active</small>
                                </div>
                                <div class="col-4">
                                    <h3 class="text-warning">12</h3>
                                    <small>Pending</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="data-card">
                        <div class="card-header">
                            <h5><i class="fas fa-bell me-2"></i> Recent Activities</h5>
                        </div>
                        <div class="card-body">
                            <div class="list-group list-group-flush">
                                <div class="list-group-item border-0 px-0">
                                    <small class="text-muted">2 minutes ago</small><br>
                                    <strong>أحمد محمد</strong> submitted a leave request
                                </div>
                                <div class="list-group-item border-0 px-0">
                                    <small class="text-muted">5 minutes ago</small><br>
                                    <strong>فاطمة علي</strong> updated profile
                                </div>
                                <div class="list-group-item border-0 px-0">
                                    <small class="text-muted">10 minutes ago</small><br>
                                    <strong>محمد حسن</strong> logged in
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Features Overview -->
        <div class="example-section">
            <h2 class="example-title">✨ مميزات Data Card</h2>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="data-card">
                        <div class="card-header">
                            <h5><i class="fas fa-star me-2"></i> Visual Features</h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    <strong>Gradient Header:</strong> تدرج لوني جذاب
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    <strong>Hover Effects:</strong> تأثيرات تفاعلية
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    <strong>Shine Animation:</strong> تأثير لمعان عند التمرير
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    <strong>Shadow Effects:</strong> ظلال ثلاثية الأبعاد
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    <strong>Responsive Design:</strong> متجاوب مع جميع الأجهزة
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="data-card">
                        <div class="card-header">
                            <h5><i class="fas fa-cogs me-2"></i> Technical Features</h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-check text-primary me-2"></i>
                                    <strong>Modular Structure:</strong> هيكل معياري
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-primary me-2"></i>
                                    <strong>CSS Variables:</strong> متغيرات قابلة للتخصيص
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-primary me-2"></i>
                                    <strong>Consistent Spacing:</strong> مسافات موحدة
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-primary me-2"></i>
                                    <strong>Icon Integration:</strong> تكامل مع Font Awesome
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-primary me-2"></i>
                                    <strong>Easy Maintenance:</strong> سهولة الصيانة
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Usage Guide -->
        <div class="example-section">
            <h2 class="example-title">📋 دليل الاستخدام</h2>
            
            <div class="data-card">
                <div class="card-header">
                    <h5><i class="fas fa-book me-2"></i> How to Use Data Cards</h5>
                </div>
                <div class="card-body">
                    <h6>1. Basic Structure:</h6>
                    <div class="code-example">
                        <pre><code>&lt;div class="data-card"&gt;
    &lt;div class="card-header"&gt;
        &lt;h5&gt;&lt;i class="fas fa-icon me-2"&gt;&lt;/i&gt; Title&lt;/h5&gt;
    &lt;/div&gt;
    &lt;div class="card-body"&gt;
        Content here
    &lt;/div&gt;
&lt;/div&gt;</code></pre>
                    </div>
                    
                    <h6>2. With Table (No Padding):</h6>
                    <div class="code-example">
                        <pre><code>&lt;div class="card-body p-0"&gt;
    &lt;div class="unified-table-responsive"&gt;
        &lt;table class="unified-table"&gt;
            &lt;!-- Table content --&gt;
        &lt;/table&gt;
    &lt;/div&gt;
&lt;/div&gt;</code></pre>
                    </div>
                    
                    <h6>3. With Footer:</h6>
                    <div class="code-example">
                        <pre><code>&lt;div class="card-footer"&gt;
    Footer content with additional info
&lt;/div&gt;</code></pre>
                    </div>
                    
                    <h6>4. CSS Classes Available:</h6>
                    <ul>
                        <li><code>.data-card</code> - Main container</li>
                        <li><code>.card-header</code> - Header section</li>
                        <li><code>.card-body</code> - Body section</li>
                        <li><code>.card-body.p-0</code> - Body without padding (for tables)</li>
                        <li><code>.card-footer</code> - Footer section</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS (Optional) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
