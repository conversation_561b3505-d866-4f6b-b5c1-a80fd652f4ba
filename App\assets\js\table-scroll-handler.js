/**
 * Table Scroll Handler
 * إدارة scroll الجداول ومؤشرات التمرير
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeTableScrollHandlers();
});

/**
 * Initialize scroll handlers for all responsive tables
 */
function initializeTableScrollHandlers() {
    const responsiveTables = document.querySelectorAll('.unified-table-responsive');
    
    responsiveTables.forEach(table => {
        setupScrollHandler(table);
        // Check initial scroll state
        updateScrollIndicators(table);
    });
}

/**
 * Setup scroll event handler for a table
 * @param {Element} tableContainer - The table container element
 */
function setupScrollHandler(tableContainer) {
    tableContainer.addEventListener('scroll', function() {
        updateScrollIndicators(this);
    });
    
    // Handle window resize
    window.addEventListener('resize', function() {
        updateScrollIndicators(tableContainer);
    });
}

/**
 * Update scroll indicators based on scroll position - DISABLED
 * @param {Element} container - The scrollable container
 */
function updateScrollIndicators(container) {
    // Scroll indicators disabled - no visual feedback needed
    return;
}

/**
 * Smooth scroll to specific position in table
 * @param {Element} container - The scrollable container
 * @param {number} position - Target scroll position
 * @param {number} duration - Animation duration in ms
 */
function smoothScrollTable(container, position, duration = 300) {
    const startPosition = container.scrollLeft;
    const distance = position - startPosition;
    const startTime = performance.now();
    
    function animation(currentTime) {
        const timeElapsed = currentTime - startTime;
        const progress = Math.min(timeElapsed / duration, 1);
        
        // Easing function (ease-out)
        const easeOut = 1 - Math.pow(1 - progress, 3);
        
        container.scrollLeft = startPosition + (distance * easeOut);
        
        if (progress < 1) {
            requestAnimationFrame(animation);
        }
    }
    
    requestAnimationFrame(animation);
}

/**
 * Add scroll navigation buttons to table
 * @param {Element} tableContainer - The table container
 */
function addScrollNavigation(tableContainer) {
    // Create navigation container
    const navContainer = document.createElement('div');
    navContainer.className = 'table-scroll-nav';
    
    // Create left scroll button
    const leftBtn = document.createElement('button');
    leftBtn.className = 'scroll-btn scroll-left';
    leftBtn.innerHTML = '<i class="fas fa-chevron-left"></i>';
    leftBtn.title = 'Scroll Left';
    
    // Create right scroll button
    const rightBtn = document.createElement('button');
    rightBtn.className = 'scroll-btn scroll-right';
    rightBtn.innerHTML = '<i class="fas fa-chevron-right"></i>';
    rightBtn.title = 'Scroll Right';
    
    // Add event listeners
    leftBtn.addEventListener('click', () => {
        const scrollAmount = tableContainer.clientWidth * 0.8;
        const newPosition = Math.max(0, tableContainer.scrollLeft - scrollAmount);
        smoothScrollTable(tableContainer, newPosition);
    });
    
    rightBtn.addEventListener('click', () => {
        const scrollAmount = tableContainer.clientWidth * 0.8;
        const maxScroll = tableContainer.scrollWidth - tableContainer.clientWidth;
        const newPosition = Math.min(maxScroll, tableContainer.scrollLeft + scrollAmount);
        smoothScrollTable(tableContainer, newPosition);
    });
    
    // Add buttons to navigation
    navContainer.appendChild(leftBtn);
    navContainer.appendChild(rightBtn);
    
    // Insert navigation before table
    tableContainer.parentNode.insertBefore(navContainer, tableContainer);
    
    // Update button states
    function updateNavButtons() {
        const scrollLeft = tableContainer.scrollLeft;
        const maxScroll = tableContainer.scrollWidth - tableContainer.clientWidth;
        
        leftBtn.disabled = scrollLeft <= 0;
        rightBtn.disabled = scrollLeft >= maxScroll - 1;
        
        // Hide navigation if table doesn't need scrolling
        if (maxScroll <= 0) {
            navContainer.style.display = 'none';
        } else {
            navContainer.style.display = 'flex';
        }
    }
    
    // Update on scroll and resize
    tableContainer.addEventListener('scroll', updateNavButtons);
    window.addEventListener('resize', updateNavButtons);
    
    // Initial update
    updateNavButtons();
}

/**
 * Initialize scroll navigation for all tables - DISABLED
 */
function initializeScrollNavigation() {
    // Navigation buttons disabled - scroll functionality remains through native scrolling
    return;
}

/**
 * Handle keyboard navigation for tables
 */
function initializeKeyboardNavigation() {
    document.addEventListener('keydown', function(e) {
        // Only handle if focus is on a table or its container
        const activeElement = document.activeElement;
        const tableContainer = activeElement.closest('.unified-table-responsive');
        
        if (!tableContainer) return;
        
        const scrollAmount = 100;
        
        switch(e.key) {
            case 'ArrowLeft':
                if (e.ctrlKey) {
                    e.preventDefault();
                    const newPos = Math.max(0, tableContainer.scrollLeft - scrollAmount);
                    smoothScrollTable(tableContainer, newPos);
                }
                break;
                
            case 'ArrowRight':
                if (e.ctrlKey) {
                    e.preventDefault();
                    const maxScroll = tableContainer.scrollWidth - tableContainer.clientWidth;
                    const newPos = Math.min(maxScroll, tableContainer.scrollLeft + scrollAmount);
                    smoothScrollTable(tableContainer, newPos);
                }
                break;
                
            case 'Home':
                if (e.ctrlKey) {
                    e.preventDefault();
                    smoothScrollTable(tableContainer, 0);
                }
                break;
                
            case 'End':
                if (e.ctrlKey) {
                    e.preventDefault();
                    const maxScroll = tableContainer.scrollWidth - tableContainer.clientWidth;
                    smoothScrollTable(tableContainer, maxScroll);
                }
                break;
        }
    });
}

/**
 * Add touch/swipe support for mobile devices
 */
function initializeTouchSupport() {
    const responsiveTables = document.querySelectorAll('.unified-table-responsive');
    
    responsiveTables.forEach(table => {
        let startX = 0;
        let scrollLeft = 0;
        let isDown = false;
        
        table.addEventListener('touchstart', (e) => {
            isDown = true;
            startX = e.touches[0].pageX - table.offsetLeft;
            scrollLeft = table.scrollLeft;
        });
        
        table.addEventListener('touchmove', (e) => {
            if (!isDown) return;
            e.preventDefault();
            const x = e.touches[0].pageX - table.offsetLeft;
            const walk = (x - startX) * 2; // Scroll speed multiplier
            table.scrollLeft = scrollLeft - walk;
        });
        
        table.addEventListener('touchend', () => {
            isDown = false;
        });
    });
}

/**
 * Initialize all table scroll features
 */
function initializeAllScrollFeatures() {
    initializeTableScrollHandlers();
    initializeScrollNavigation();
    initializeKeyboardNavigation();
    initializeTouchSupport();
}

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeAllScrollFeatures);
} else {
    initializeAllScrollFeatures();
}

// Export functions for manual initialization
window.TableScrollHandler = {
    initialize: initializeAllScrollFeatures,
    setupScrollHandler: setupScrollHandler,
    addScrollNavigation: addScrollNavigation,
    smoothScrollTable: smoothScrollTable
};
