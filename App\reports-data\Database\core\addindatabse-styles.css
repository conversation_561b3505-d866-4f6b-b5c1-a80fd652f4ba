/* تنسيقات خاصة بصفحة إضافة البيانات */

/* ألوان أساسية من النظام الموحد */
:root {
    --primary-color: #b580b0;
    --primary-dark: #a2649c;
    --primary-darker: #8f4888;
    --primary-light: #c89cc4;
    --primary-lighter: #dcc8e0;
    
    --success-color: #22c55e;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #3b82f6;
    
    --bg-white: #ffffff;
    --bg-light: #faf8fc;
    --text-primary: #691060;
    --text-secondary: #8f4888;
    --text-white: #ffffff;
    
    --border-light: #ede4f1;
    --border-medium: #dcc8e0;
    
    --shadow-sm: 0 1px 2px 0 rgba(200, 156, 196, 0.08);
    --shadow-md: 0 4px 6px -1px rgba(200, 156, 196, 0.12);
    --shadow-lg: 0 10px 15px -3px rgba(200, 156, 196, 0.12);
    --shadow-xl: 0 20px 25px -5px rgba(200, 156, 196, 0.12);
    
    --radius-sm: 4px;
    --radius-md: 6px;
    --radius-lg: 8px;
    --radius-xl: 12px;
    
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* تنسيقات أساسية */
body.add-database {
    font-size: 12px !important;
    line-height: 1.4 !important;
    background: linear-gradient(135deg, var(--primary-light), var(--primary-dark));
    min-height: 100vh;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.add-database .container {
    max-width: 100% !important;
    padding: 1rem !important;
}

/* تنسيقات البطاقات */
.add-database .card {
    border: none;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    margin-bottom: 1.5rem;
    background: var(--bg-white);
    transition: var(--transition);
    overflow: hidden;
}

.add-database .card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.add-database .card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--text-white);
    border: none;
    border-radius: var(--radius-xl) var(--radius-xl) 0 0;
    padding: 1rem 1.5rem;
    font-weight: 600;
    position: relative;
}

.add-database .card-header::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to right, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
}

.add-database .card-header h3 {
    font-size: 14px !important;
    margin: 0;
    display: flex;
    align-items: center;
    position: relative;
    z-index: 1;
}

.add-database .card-header i {
    margin-right: 0.5rem;
    font-size: 1.2em;
}

.add-database .card-body {
    padding: 1.5rem !important;
}

/* تنسيقات النماذج */
.add-database .floating-label {
    margin-bottom: 1rem !important;
    position: relative;
}

.add-database .floating-label label {
    font-size: 11px !important;
    font-weight: 500 !important;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
    display: block;
}

.add-database .floating-label label i {
    font-size: 10px !important;
    margin-right: 0.5rem;
    color: var(--primary-color);
}

.add-database .form-control, 
.add-database .form-select {
    font-size: 11px !important;
    padding: 0.6rem 0.8rem !important;
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    transition: var(--transition);
}

.add-database .form-control:focus, 
.add-database .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(181, 128, 176, 0.25);
}

/* تنسيقات الأزرار */
.add-database .btn {
    font-size: 12px !important;
    padding: 0.6rem 1.2rem !important;
    border-radius: var(--radius-md);
    font-weight: 500;
    transition: var(--transition);
}

.add-database .btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border: none;
    color: var(--text-white);
    box-shadow: var(--shadow-sm);
}

.add-database .btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-darker));
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    color: var(--text-white);
}

.add-database .btn-outline-secondary {
    border-color: #6c757d;
    color: #6c757d;
}

.add-database .btn-outline-secondary:hover {
    background-color: #6c757d;
    border-color: #6c757d;
    color: var(--text-white);
}

.add-database .btn-outline-info {
    border-color: var(--info-color);
    color: var(--info-color);
}

.add-database .btn-outline-info:hover {
    background-color: var(--info-color);
    border-color: var(--info-color);
    color: var(--text-white);
}

/* تنسيقات التنبيهات */
.add-database .alert {
    font-size: 11px !important;
    padding: 0.8rem 1rem;
    border-radius: var(--radius-md);
    border: none;
}

.add-database .alert-success {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(34, 197, 94, 0.05));
    color: var(--success-color);
    border-left: 4px solid var(--success-color);
}

.add-database .alert-danger {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(239, 68, 68, 0.05));
    color: var(--danger-color);
    border-left: 4px solid var(--danger-color);
}

.add-database .alert-warning {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(245, 158, 11, 0.05));
    color: var(--warning-color);
    border-left: 4px solid var(--warning-color);
}

/* تنسيقات الشعار */
.add-database .logo-container {
    text-align: center;
    margin-bottom: 2rem;
    background: var(--bg-white);
    padding: 2rem;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
}

.add-database .logo-container img {
    max-height: 60px;
    width: auto;
    transition: transform 0.3s ease;
}

.add-database .logo-container img:hover {
    transform: scale(1.05);
}

.add-database .logo-container h1 {
    font-size: 18px !important;
    color: var(--primary-color);
    margin-top: 1rem;
    margin-bottom: 0.5rem;
}

.add-database .user-info .badge {
    font-size: 10px !important;
    padding: 0.4rem 0.8rem;
    background-color: var(--bg-light);
    color: var(--text-secondary);
    border: 1px solid var(--border-light);
}

/* تنسيقات إضافية */
.add-database .other-field {
    margin-top: 0.5rem !important;
    display: none;
}

.add-database textarea {
    min-height: 80px !important;
    resize: vertical;
}

.add-database .form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.add-database .form-check-label {
    color: var(--text-secondary);
    font-size: 11px !important;
}

.add-database .fade-in {
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
}

.add-database .fade-in.show {
    opacity: 1;
}

.add-database .badge {
    font-size: 10px !important;
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius-sm);
}

.add-database .icon-3d {
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

.add-database .text-danger {
    color: var(--danger-color) !important;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .add-database .container {
        padding: 0.5rem !important;
    }
    
    .add-database .card-body {
        padding: 1rem !important;
    }
    
    .add-database .logo-container {
        padding: 1rem;
    }
    
    .add-database .logo-container h1 {
        font-size: 16px !important;
    }
}

/* تحسينات للطباعة */
@media print {
    body.add-database {
        background: var(--bg-white) !important;
    }
    
    .add-database .card {
        box-shadow: none !important;
        border: 1px solid var(--border-light) !important;
    }
}
