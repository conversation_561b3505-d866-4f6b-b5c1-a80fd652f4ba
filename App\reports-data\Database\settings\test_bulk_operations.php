<?php
/**
 * Test Bulk Operations - Test delete and sync functionality
 */

session_start();
include $_SERVER['DOCUMENT_ROOT'].'/config/Databasehost.php';
include '../system/database_config.php';

// Check session and access
if (!isset($_SESSION['username']) || !isset($_SESSION['random_code'])) {
    header("Location: ../../../../Loginpage.php");
    exit();
}

$access = $_SESSION['access_level'];
$email = $_SESSION['username'];

// Include the functions from field_settings.php
require_once __DIR__ . '/field_functions_fixed.php';

echo "<!DOCTYPE html>";
echo "<html><head>";
echo "<title>Test Bulk Operations</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'>";
echo "<style>";
echo "body { background: #f8f9fa; font-family: 'Segoe UI', sans-serif; }";
echo ".debug-info { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin: 10px 0; font-family: monospace; font-size: 14px; }";
echo ".success-msg { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 8px; margin: 10px 0; }";
echo ".error-msg { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 8px; margin: 10px 0; }";
echo ".field-item { background: white; padding: 10px; margin: 5px 0; border-radius: 5px; border: 1px solid #dee2e6; }";
echo "</style>";
echo "</head><body>";

echo "<div class='container mt-4'>";
echo "<h1>🧪 Test Bulk Operations</h1>";

// Handle POST actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    echo "<div class='debug-info'>";
    echo "<strong>🔄 Processing Action:</strong> " . htmlspecialchars($action) . "<br>";
    echo "<strong>User:</strong> " . $email . "<br>";
    echo "<strong>Access:</strong> " . $access . "<br>";
    echo "<strong>POST Data:</strong><br>";
    echo "<pre>" . print_r($_POST, true) . "</pre>";
    echo "</div>";
    
    try {
        switch ($action) {
            case 'test_sync':
                echo "<h3>🔄 Testing Sync with Database</h3>";
                $result = syncWithDatabase();
                break;
                
            case 'test_bulk_delete':
                echo "<h3>🗑️ Testing Bulk Delete</h3>";
                $result = bulkDeleteFields();
                break;
                
            case 'test_save':
                echo "<h3>💾 Testing Save Configuration</h3>";
                $result = saveFieldConfiguration();
                break;
                
            default:
                $result = ['success' => false, 'message' => 'Unknown action'];
        }
        
        if ($result['success']) {
            echo "<div class='success-msg'>✅ " . $result['message'] . "</div>";
        } else {
            echo "<div class='error-msg'>❌ " . $result['message'] . "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='error-msg'>❌ Exception: " . $e->getMessage() . "</div>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
}

// Load current configuration
try {
    $config = loadFieldConfiguration();
    
    echo "<div class='row mt-4'>";
    echo "<div class='col-md-6'>";
    echo "<div class='card'>";
    echo "<div class='card-header bg-primary text-white'>";
    echo "<h5 class='mb-0'>📋 Current Configuration</h5>";
    echo "</div>";
    echo "<div class='card-body'>";
    
    if (empty($config)) {
        echo "<p class='text-muted'>No configuration found</p>";
    } else {
        foreach ($config as $sectionKey => $section) {
            echo "<h6><i class='fas {$section['icon']} me-2'></i>{$section['title']}</h6>";
            
            if (isset($section['fields']) && !empty($section['fields'])) {
                echo "<div class='ms-3'>";
                foreach ($section['fields'] as $fieldName => $fieldConfig) {
                    $enabled = isset($fieldConfig['enabled']) && $fieldConfig['enabled'] ? 'enabled' : 'disabled';
                    $color = $enabled === 'enabled' ? 'success' : 'secondary';
                    
                    echo "<div class='field-item'>";
                    echo "<input type='checkbox' class='form-check-input me-2' name='test_fields[]' value='{$sectionKey}|{$fieldName}' id='field_{$sectionKey}_{$fieldName}'>";
                    echo "<label for='field_{$sectionKey}_{$fieldName}' class='form-check-label'>";
                    echo "<span class='badge bg-{$color} me-2'>{$enabled}</span>";
                    echo "<strong>{$fieldName}</strong> ({$fieldConfig['type']})";
                    echo "</label>";
                    echo "</div>";
                }
                echo "</div>";
            } else {
                echo "<p class='text-muted ms-3'>No fields in this section</p>";
            }
            echo "<hr>";
        }
    }
    
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    // Test actions
    echo "<div class='col-md-6'>";
    echo "<div class='card'>";
    echo "<div class='card-header bg-success text-white'>";
    echo "<h5 class='mb-0'>🎮 Test Actions</h5>";
    echo "</div>";
    echo "<div class='card-body'>";
    
    // Sync test
    echo "<form method='POST' class='mb-3'>";
    echo "<input type='hidden' name='action' value='test_sync'>";
    echo "<button type='submit' class='btn btn-info btn-lg w-100'>";
    echo "<i class='fas fa-sync me-2'></i>Test Sync with Database";
    echo "</button>";
    echo "</form>";
    
    // Bulk delete test
    echo "<form method='POST' class='mb-3' id='bulkDeleteForm'>";
    echo "<input type='hidden' name='action' value='test_bulk_delete'>";
    echo "<div id='selectedFieldsContainer'></div>";
    echo "<button type='button' class='btn btn-warning btn-lg w-100' onclick='prepareBulkDelete()'>";
    echo "<i class='fas fa-trash me-2'></i>Test Bulk Delete Selected";
    echo "</button>";
    echo "</form>";
    
    // Save test
    echo "<form method='POST' class='mb-3'>";
    echo "<input type='hidden' name='action' value='test_save'>";
    echo "<input type='hidden' name='sections[test_section][title]' value='Test Section'>";
    echo "<input type='hidden' name='sections[test_section][icon]' value='fa-test'>";
    echo "<input type='hidden' name='fields[test_section][test_field][enabled]' value='1'>";
    echo "<input type='hidden' name='fields[test_section][test_field][type]' value='text'>";
    echo "<input type='hidden' name='fields[test_section][test_field][label]' value='Test Field'>";
    echo "<button type='submit' class='btn btn-success btn-lg w-100'>";
    echo "<i class='fas fa-save me-2'></i>Test Save Configuration";
    echo "</button>";
    echo "</form>";
    
    echo "</div>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error-msg'>❌ Error loading configuration: " . $e->getMessage() . "</div>";
}

// Database info
echo "<div class='card mt-4'>";
echo "<div class='card-header bg-info text-white'>";
echo "<h5 class='mb-0'>🗄️ Database Information</h5>";
echo "</div>";
echo "<div class='card-body'>";

try {
    $result = $conn->query("DESCRIBE databasehc");
    if ($result) {
        echo "<p><strong>Database Columns:</strong> " . $result->num_rows . "</p>";
        echo "<details><summary>Show all columns</summary>";
        echo "<div class='row'>";
        $count = 0;
        while ($row = $result->fetch_assoc()) {
            echo "<div class='col-md-4 mb-2'>";
            echo "<span class='badge bg-primary'>{$row['Field']}</span> ";
            echo "<small class='text-muted'>({$row['Type']})</small>";
            echo "</div>";
            $count++;
        }
        echo "</div>";
        echo "</details>";
    }
} catch (Exception $e) {
    echo "<div class='error-msg'>❌ Database error: " . $e->getMessage() . "</div>";
}

echo "</div>";
echo "</div>";

// Navigation
echo "<div class='card mt-4'>";
echo "<div class='card-body text-center'>";
echo "<h5>🔗 Navigation</h5>";
echo "<a href='field_settings.php' class='btn btn-primary me-2'>🔙 Field Settings</a>";
echo "<a href='field_settings.php?debug=1' class='btn btn-info me-2'>🔍 Field Settings (Debug)</a>";
echo "<a href='field_diagnostics.php' class='btn btn-secondary'>📊 Field Diagnostics</a>";
echo "</div>";
echo "</div>";

echo "</div>"; // container

echo "<script>";
echo "function prepareBulkDelete() {";
echo "  const checkboxes = document.querySelectorAll('input[name=\"test_fields[]\"]:checked');";
echo "  const container = document.getElementById('selectedFieldsContainer');";
echo "  container.innerHTML = '';";
echo "  ";
echo "  if (checkboxes.length === 0) {";
echo "    alert('Please select at least one field to delete');";
echo "    return;";
echo "  }";
echo "  ";
echo "  if (!confirm(`Delete ${checkboxes.length} selected fields?`)) {";
echo "    return;";
echo "  }";
echo "  ";
echo "  checkboxes.forEach(checkbox => {";
echo "    const input = document.createElement('input');";
echo "    input.type = 'hidden';";
echo "    input.name = 'selected_fields[]';";
echo "    input.value = checkbox.value;";
echo "    container.appendChild(input);";
echo "  });";
echo "  ";
echo "  document.getElementById('bulkDeleteForm').submit();";
echo "}";
echo "</script>";

echo "</body></html>";
?>
