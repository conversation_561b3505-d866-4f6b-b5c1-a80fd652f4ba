@import url('root-variables.css');

/* ==================== SIDEBAR STYLES ==================== */
/* ملف CSS مخصص للقائمة الجانبية */

/* ==================== SIDEBAR CONTAINER ==================== */

.sidebar {
    width: 220px;
    height: 100vh;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--white);
    position: fixed;
    top: 0;
    left: 0;
    transition: var(--transition);
    z-index: 1000;
    box-shadow: 4px 0 15px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

.sidebar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* Collapsed State */
.sidebar.collapsed {
    width: 60px;
}

.sidebar.collapsed .sidebar-header img {
    width: 35px;
    height: 35px;
}

.sidebar.collapsed .sidebar-menu a span {
    display: none;
}

.sidebar.collapsed .sidebar-footer .logout-btn span,
.sidebar.collapsed .sidebar-footer .toggle-btn span {
    display: none;
}

.sidebar.collapsed .sidebar-footer .logout-btn,
.sidebar.collapsed .sidebar-footer .toggle-btn {
    padding: 10px;
    justify-content: center;
}

/* ==================== SIDEBAR HEADER ==================== */

.sidebar-header {
    padding: 20px 15px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    flex-shrink: 0;
}

.sidebar-header img {
    max-width: 100%;
    height: auto;
    max-height: 50px;
    transition: var(--transition);
    filter: brightness(1.1);
}

.sidebar-header img:hover {
    transform: scale(1.05);
    filter: brightness(1.2);
}

/* ==================== SIDEBAR MENU ==================== */

/* Container for scrollable menu */
.sidebar-menu-container {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 10px 0;
}

.sidebar-menu-container::-webkit-scrollbar {
    width: 4px;
}

.sidebar-menu-container::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
}

.sidebar-menu-container::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
}

.sidebar-menu-container::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.4);
}

.sidebar-menu {
    padding: 5px 0;
}

.sidebar-menu a {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    color: var(--white);
    text-decoration: none;
    transition: var(--transition);
    border-left: 3px solid transparent;
    position: relative;
    margin: 1px 0;
}

.sidebar-menu a::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    transition: var(--transition);
    z-index: -1;
}

.sidebar-menu a:hover::before {
    width: 100%;
}

.sidebar-menu a:hover,
.sidebar-menu a.active {
    background: rgba(255, 255, 255, 0.15);
    border-left: 3px solid var(--white);
    transform: translateX(3px);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.sidebar-menu a.active {
    background: rgba(255, 255, 255, 0.2);
    font-weight: 600;
}

.sidebar-menu a i {
    margin-right: 12px;
    font-size: 16px;
    min-width: 22px;
    text-align: center;
    transition: var(--transition);
}

.sidebar-menu a:hover i,
.sidebar-menu a.active i {
    transform: scale(1.1);
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.sidebar-menu a span {
    transition: var(--transition);
    font-size: 13px;
    font-weight: 500;
    letter-spacing: 0.3px;
}

.sidebar-menu a:hover span,
.sidebar-menu a.active span {
    font-weight: 600;
}

/* ==================== SIDEBAR FOOTER ==================== */

.sidebar-footer {
    padding: 15px;
    border-top: 1px solid rgba(255, 255, 255, 0.15);
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    flex-shrink: 0;
}

.logout-btn,
.toggle-btn {
    width: 100%;
    padding: 10px 12px;
    margin-bottom: 8px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--white);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-size: 13px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    text-decoration: none;
}

.logout-btn:hover,
.toggle-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    color: var(--white);
    text-decoration: none;
}

.logout-btn:active,
.toggle-btn:active {
    transform: translateY(0);
}

.logout-btn i,
.toggle-btn i {
    font-size: 14px;
    transition: var(--transition);
}

.logout-btn:hover i,
.toggle-btn:hover i {
    transform: scale(1.1);
}

/* ==================== MOBILE MENU BUTTON ==================== */

.mobile-menu-btn {
    display: none;
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1001;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--white);
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    cursor: pointer;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    transition: var(--transition);
}

.mobile-menu-btn:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.mobile-menu-btn:active {
    transform: scale(0.95);
}

.mobile-menu-btn i {
    font-size: 18px;
    transition: var(--transition);
}

.mobile-menu-btn:hover i {
    transform: rotate(90deg);
}

/* ==================== ANIMATIONS ==================== */

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutLeft {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(-100%);
        opacity: 0;
    }
}

.sidebar.show {
    animation: slideInLeft 0.3s ease-out;
}

.sidebar.hide {
    animation: slideOutLeft 0.3s ease-out;
}

/* ==================== RESPONSIVE DESIGN ==================== */

/* Large Screens */
@media (min-width: 1200px) {
    .sidebar {
        width: 240px;
    }

    .sidebar.collapsed {
        width: 65px;
    }

    .sidebar-menu a {
        padding: 14px 20px;
    }

    .sidebar-menu a i {
        font-size: 17px;
        margin-right: 14px;
    }

    .sidebar-menu a span {
        font-size: 14px;
    }
}

/* Tablet and Small Desktop */
@media (max-width: 992px) {
    .sidebar {
        transform: translateX(-100%);
        width: 220px;
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .sidebar.collapsed {
        width: 220px;
        transform: translateX(-100%);
    }

    .sidebar.collapsed.show {
        transform: translateX(0);
    }

    .mobile-menu-btn {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Overlay for mobile */
    .sidebar.show::after {
        content: '';
        position: fixed;
        top: 0;
        left: 220px;
        width: calc(100vw - 220px);
        height: 100vh;
        background: rgba(0, 0, 0, 0.5);
        z-index: -1;
    }
}

/* Mobile Phones */
@media (max-width: 768px) {
    .sidebar {
        width: 250px;
    }

    .sidebar.show::after {
        left: 250px;
        width: calc(100vw - 250px);
    }

    .sidebar-header {
        padding: 18px 12px;
    }

    .sidebar-menu a {
        padding: 10px 12px;
    }

    .sidebar-menu a i {
        font-size: 15px;
        margin-right: 10px;
    }

    .sidebar-menu a span {
        font-size: 12px;
    }

    .sidebar-footer {
        padding: 12px;
    }

    .logout-btn,
    .toggle-btn {
        padding: 8px 10px;
        font-size: 12px;
    }
}

/* Very Small Screens */
@media (max-width: 480px) {
    .sidebar {
        width: 100vw;
    }

    .sidebar.show::after {
        display: none;
    }

    .mobile-menu-btn {
        width: 45px;
        height: 45px;
        top: 15px;
        left: 15px;
    }

    .mobile-menu-btn i {
        font-size: 16px;
    }
}

/* ==================== ACCESSIBILITY ==================== */

/* Focus States */
.sidebar-menu a:focus,
.logout-btn:focus,
.toggle-btn:focus,
.mobile-menu-btn:focus {
    outline: 2px solid var(--white);
    outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .sidebar {
        background: var(--dark-color);
        border-right: 2px solid var(--white);
    }

    .sidebar-menu a:hover,
    .sidebar-menu a.active {
        background: var(--white);
        color: var(--dark-color);
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .sidebar,
    .sidebar-menu a,
    .sidebar-menu a i,
    .sidebar-menu a span,
    .logout-btn,
    .toggle-btn,
    .mobile-menu-btn {
        transition: none;
    }

    .sidebar-menu a:hover,
    .sidebar-menu a.active {
        transform: none;
    }

    .mobile-menu-btn:hover i {
        transform: none;
    }
}

/* ==================== MAIN CONTENT INTERACTION ==================== */

/* تحسين التفاعل مع المحتوى الرئيسي */
.main-content {
    margin-left: 220px;
    transition: var(--transition);
    padding: 20px;
    min-height: 100vh;
}

/* عند تصغير القائمة الجانبية */
.sidebar.collapsed ~ .main-content,
body:has(.sidebar.collapsed) .main-content {
    margin-left: 60px;
}

/* للشاشات الكبيرة */
@media (min-width: 1200px) {
    .main-content {
        margin-left: 240px;
    }

    .sidebar.collapsed ~ .main-content,
    body:has(.sidebar.collapsed) .main-content {
        margin-left: 65px;
    }
}

/* للشاشات المتوسطة والصغيرة */
@media (max-width: 992px) {
    .main-content {
        margin-left: 0;
    }

    .sidebar.collapsed ~ .main-content,
    body:has(.sidebar.collapsed) .main-content {
        margin-left: 0;
    }
}

/* ==================== PRINT STYLES ==================== */

@media print {
    .sidebar,
    .mobile-menu-btn {
        display: none;
    }

    .main-content {
        margin-left: 0 !important;
    }
}
