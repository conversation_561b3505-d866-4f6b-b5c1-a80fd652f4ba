<?php
/**
 * Fix script for addindatabse.php issues
 * This script attempts to fix common issues with the add database form
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
session_start();

// Include database connection
include $_SERVER['DOCUMENT_ROOT'].'/config/Databasehost.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>Fix Add Database Issues</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head><body class='container py-4'>";

echo "<h1>Add Database Form - Fix Issues</h1>";

$fixes = [];

// Fix 1: Check and create missing configuration files
echo "<div class='card mb-4'>";
echo "<div class='card-header'><h3>1. Configuration Files Fix</h3></div>";
echo "<div class='card-body'>";

$configDir = __DIR__ . '/../settings/';
$configFile = $configDir . 'field_config.json';
$defaultConfigFile = $configDir . 'default_field_config.json';

// Create default configuration if missing
if (!file_exists($defaultConfigFile)) {
    $defaultConfig = [
        "basic_info" => [
            "title" => "Basic Information",
            "icon" => "fa-user",
            "fields" => [
                "InterpreterName" => [
                    "enabled" => true,
                    "type" => "text",
                    "col" => 6,
                    "icon" => "fa-user",
                    "label" => "Interpreter Name",
                    "required" => true
                ],
                "FGEmails" => [
                    "enabled" => true,
                    "type" => "email",
                    "col" => 6,
                    "icon" => "fa-envelope",
                    "label" => "Email Address",
                    "required" => true
                ],
                "Language" => [
                    "enabled" => true,
                    "type" => "text",
                    "col" => 6,
                    "icon" => "fa-globe",
                    "label" => "Language"
                ],
                "Status" => [
                    "enabled" => true,
                    "type" => "select",
                    "col" => 6,
                    "icon" => "fa-info-circle",
                    "label" => "Status",
                    "source_type" => "custom",
                    "options" => ["Active", "Inactive", "Pending", "Training"]
                ]
            ]
        ],
        "work_details" => [
            "title" => "Work Details",
            "icon" => "fa-briefcase",
            "fields" => [
                "Module" => [
                    "enabled" => true,
                    "type" => "select",
                    "col" => 6,
                    "icon" => "fa-cube",
                    "label" => "Module",
                    "source_type" => "custom",
                    "options" => ["Healthcare", "Legal", "Education", "Business"]
                ],
                "Account" => [
                    "enabled" => true,
                    "type" => "text",
                    "col" => 6,
                    "icon" => "fa-building",
                    "label" => "Account"
                ],
                "Startdate" => [
                    "enabled" => true,
                    "type" => "date",
                    "col" => 6,
                    "icon" => "fa-calendar",
                    "label" => "Start Date"
                ]
            ]
        ]
    ];
    
    if (file_put_contents($defaultConfigFile, json_encode($defaultConfig, JSON_PRETTY_PRINT))) {
        echo "<div class='alert alert-success'>✓ Created default_field_config.json</div>";
        $fixes[] = "Created default configuration file";
    } else {
        echo "<div class='alert alert-danger'>✗ Failed to create default_field_config.json</div>";
    }
} else {
    echo "<div class='alert alert-info'>✓ default_field_config.json already exists</div>";
}

// Copy default to main config if main config doesn't exist
if (!file_exists($configFile) && file_exists($defaultConfigFile)) {
    if (copy($defaultConfigFile, $configFile)) {
        echo "<div class='alert alert-success'>✓ Created field_config.json from default</div>";
        $fixes[] = "Created main configuration file";
    } else {
        echo "<div class='alert alert-danger'>✗ Failed to create field_config.json</div>";
    }
} else {
    echo "<div class='alert alert-info'>✓ field_config.json already exists</div>";
}

echo "</div></div>";

// Fix 2: Check database table structure
echo "<div class='card mb-4'>";
echo "<div class='card-header'><h3>2. Database Structure Fix</h3></div>";
echo "<div class='card-body'>";

if (isset($conn) && !$conn->connect_error) {
    // Check if databasehc table exists
    $result = $conn->query("SHOW TABLES LIKE 'databasehc'");
    if ($result->num_rows > 0) {
        echo "<div class='alert alert-success'>✓ Table 'databasehc' exists</div>";
        
        // Check for required columns
        $requiredColumns = ['InterpreterName', 'FGEmails', 'Language', 'Status', 'Module', 'Account', 'Startdate'];
        $result = $conn->query("DESCRIBE databasehc");
        $existingColumns = [];
        while ($row = $result->fetch_assoc()) {
            $existingColumns[] = $row['Field'];
        }
        
        $missingColumns = array_diff($requiredColumns, $existingColumns);
        if (empty($missingColumns)) {
            echo "<div class='alert alert-success'>✓ All required columns exist</div>";
        } else {
            echo "<div class='alert alert-warning'>⚠ Missing columns: " . implode(', ', $missingColumns) . "</div>";
            echo "<p>You may need to add these columns to your database table.</p>";
        }
        
    } else {
        echo "<div class='alert alert-danger'>✗ Table 'databasehc' does not exist</div>";
        echo "<p>Please create the database table first.</p>";
    }
} else {
    echo "<div class='alert alert-danger'>✗ Database connection failed</div>";
}

echo "</div></div>";

// Fix 3: Check file permissions
echo "<div class='card mb-4'>";
echo "<div class='card-header'><h3>3. File Permissions Fix</h3></div>";
echo "<div class='card-body'>";

$filesToCheck = [
    $configFile,
    $defaultConfigFile,
    __DIR__ . '/addindatabse.php'
];

foreach ($filesToCheck as $file) {
    if (file_exists($file)) {
        if (is_readable($file)) {
            echo "<div class='alert alert-success'>✓ " . basename($file) . " is readable</div>";
        } else {
            echo "<div class='alert alert-danger'>✗ " . basename($file) . " is not readable</div>";
        }
        
        if (is_writable(dirname($file))) {
            echo "<div class='alert alert-success'>✓ " . dirname($file) . " is writable</div>";
        } else {
            echo "<div class='alert alert-warning'>⚠ " . dirname($file) . " is not writable</div>";
        }
    }
}

echo "</div></div>";

// Fix 4: Test form functionality
echo "<div class='card mb-4'>";
echo "<div class='card-header'><h3>4. Form Functionality Test</h3></div>";
echo "<div class='card-body'>";

echo "<p>Testing form with sample data...</p>";

// Simulate form submission
$_POST = [
    'InterpreterName' => 'Test User',
    'FGEmails' => '<EMAIL>',
    'Language' => 'English',
    'Status' => 'Active',
    'Module' => 'Healthcare'
];

// Test the form processing function
if (function_exists('processFormSubmission')) {
    echo "<div class='alert alert-info'>Form processing function exists</div>";
} else {
    echo "<div class='alert alert-warning'>Form processing function not found - this is normal if running separately</div>";
}

echo "</div></div>";

// Summary
echo "<div class='card mb-4'>";
echo "<div class='card-header'><h3>Summary</h3></div>";
echo "<div class='card-body'>";

if (!empty($fixes)) {
    echo "<h5>Fixes Applied:</h5>";
    echo "<ul>";
    foreach ($fixes as $fix) {
        echo "<li class='text-success'>✓ $fix</li>";
    }
    echo "</ul>";
} else {
    echo "<div class='alert alert-info'>No fixes were needed - everything appears to be configured correctly.</div>";
}

echo "<h5>Next Steps:</h5>";
echo "<ol>";
echo "<li><a href='test_addindatabse.php' class='btn btn-sm btn-info'>Run Diagnostic Test</a></li>";
echo "<li><a href='addindatabse.php' class='btn btn-sm btn-primary'>Try Add Database Form</a></li>";
echo "<li>Check server error logs if issues persist</li>";
echo "<li>Verify database connection settings</li>";
echo "</ol>";

echo "</div></div>";

echo "</body></html>";
?>
