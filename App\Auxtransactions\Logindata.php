<?php
include $_SERVER['DOCUMENT_ROOT'].'/config/Databasehost.php';

// Rows per page
$rows_per_page = 400;

// Get unique filter values
$device_query = "SELECT DISTINCT DeviceName FROM usersloginandlogout ORDER BY DeviceName";
$device_result = $conn->query($device_query);
$devices = [];
while ($row = $device_result->fetch_assoc()) {
    $devices[] = $row['DeviceName'];
}

// Get total row count
$sql_count = "SELECT COUNT(*) AS total FROM usersloginandlogout";
$result_count = $conn->query($sql_count);
$row_count = $result_count->fetch_assoc();
$total_rows = $row_count['total'];

// Calculate total pages
$total_pages = ceil($total_rows / $rows_per_page);

// Determine current page
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$page = max($page, 1);
$offset = ($page - 1) * $rows_per_page;

// Filtering
$filter_device = isset($_GET['device']) ? $_GET['device'] : '';
$filter_start_date = isset($_GET['start_date']) ? $_GET['start_date'] : '';
$filter_end_date = isset($_GET['end_date']) ? $_GET['end_date'] : '';
$filter_accuracy = isset($_GET['accuracy_blank']) ? "AND Accuracy = ''" : '';

$sql = "SELECT Email, Logintime, Logouttime, DeviceName, BrowserName, IPAddress, Accuracy, locationLink 
        FROM usersloginandlogout 
        WHERE 1=1 
        ORDER BY Logintime DESC";

if ($filter_device) {
    $sql .= " AND DeviceName = '" . $conn->real_escape_string($filter_device) . "'";
}
if ($filter_start_date && $filter_end_date) {
    $sql .= " AND DATE(Logintime) BETWEEN '" . $conn->real_escape_string($filter_start_date) . "' AND '" . $conn->real_escape_string($filter_end_date) . "'";
}
$sql .= " $filter_accuracy LIMIT $offset, $rows_per_page";

$result = $conn->query($sql);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login and Logout Records</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link href="<!-- Google Fonts replaced with system fonts for CSP compliance -->" rel="stylesheet">
       <link rel="stylesheet" href="../assets/css/secondstyle.css">

</head>
<body>
    <div class="container mt-4">
        <h2 class="text-center mb-4"><i class="fas fa-user-clock"></i> Login and Logout Records</h2>
        
        <form method="GET" class="mb-3">
            <div class="row g-3">
                <div class="col-md-4">
                    <label for="start_date" class="form-label">Filter from Date:</label>
                    <input type="date" name="start_date" id="start_date" class="form-control" value="<?php echo $filter_start_date; ?>">
                </div>
                <div class="col-md-4">
                    <label for="end_date" class="form-label">Filter to Date:</label>
                    <input type="date" name="end_date" id="end_date" class="form-control" value="<?php echo $filter_end_date; ?>">
                </div>
                <div class="col-md-4">
                    <label for="device" class="form-label">Filter by Device:</label>
                    <select name="device" id="device" class="form-control">
                        <option value="">All</option>
                        <?php foreach ($devices as $device): ?>
                            <option value="<?php echo $device; ?>" <?php echo ($filter_device == $device) ? 'selected' : ''; ?>><?php echo $device; ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-4">
                    <label class="form-label d-block">Filter by Blank Accuracy:</label>
                    <input type="checkbox" name="accuracy_blank" value="1" <?php echo isset($_GET['accuracy_blank']) ? 'checked' : ''; ?>> Show records with blank accuracy
                </div>
            </div>
            <button type="submit" class="btn btn-primary mt-3 w-100"><i class="fas fa-filter"></i> Apply Filters</button>
        </form>

        <table class="table table-bordered table-striped">
            <thead>
                <tr>
                    <th>Email</th>
                    <th>Logintime</th>
                    <th>Logouttime</th>
                    <th>Device Name</th>
                    <th>Browser Name</th>
                    <th>IP Address</th>
                    <th>Accuracy</th>
                    <th>Location</th>
                </tr>
            </thead>
            <tbody>
                <?php while ($row = $result->fetch_assoc()): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($row['Email']); ?></td>
                        <td><?php echo htmlspecialchars($row['Logintime']); ?></td>
                        <td><?php echo htmlspecialchars($row['Logouttime']); ?></td>
                        <td><?php echo htmlspecialchars($row['DeviceName']); ?></td>
                        <td><?php echo htmlspecialchars($row['BrowserName']); ?></td>
                        <td><?php echo htmlspecialchars($row['IPAddress']); ?></td>
                        <td><?php echo htmlspecialchars($row['Accuracy']); ?></td>
                        <td>
                            <?php if (!empty($row['locationLink'])): ?>
                                <a href="<?php echo htmlspecialchars($row['locationLink']); ?>" target="_blank" class="btn btn-sm btn-outline-dark">View Location</a>
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php endwhile; ?>
            </tbody>
        </table>

        <!-- Pagination -->
        <nav>
            <ul class="pagination justify-content-center">
                <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                    <li class="page-item <?php echo ($i == $page) ? 'active' : ''; ?>">
                        <a class="page-link" href="?page=<?php echo $i; ?>"> <?php echo $i; ?> </a>
                    </li>
                <?php endfor; ?>
            </ul>
        </nav>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
