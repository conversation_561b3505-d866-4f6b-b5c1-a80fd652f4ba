<?php
/**
 * اقتراح لجعل الفلاتر ديناميكية بالكامل
 * Suggestion for making filters completely dynamic
 */

// بدلاً من الفلاتر الثابتة، استخدم هذا النهج:
function generateDynamicFilters($conn, $availableColumns) {
    $filters = [];
    
    // تحديد الأعمدة التي تحتاج فلاتر
    $filterableColumns = [
        'Module', 'Status', 'Leader', 'Dedication', 'SeatLang', 'Startdate',
        'Crowd', 'Legal', 'Certified', 'IMS', 'TTG', 'MW', 'Lango', 'Bar',
        'GlB', 'Eff', 'Foc', 'TUS', 'Mav', 'Abs', 'Inlin', 'LB', 'Propio'
    ];
    
    foreach ($filterableColumns as $column) {
        if (in_array($column, $availableColumns)) {
            $filters[$column] = getFilterOptionsIfExists($conn, $column, $availableColumns);
        }
    }
    
    return $filters;
}

// أو حتى أفضل - اكتشاف تلقائي للأعمدة القابلة للفلترة:
function autoDetectFilterableColumns($conn) {
    $columns = [];
    $result = $conn->query("DESCRIBE databasehc");
    
    while ($row = $result->fetch_assoc()) {
        $columnName = $row['Field'];
        $columnType = strtolower($row['Type']);
        
        // تخطي الأعمدة غير المناسبة للفلترة
        if (in_array($columnName, ['ID', 'InterpreterName', 'FGEmails'])) {
            continue;
        }
        
        // تضمين الأعمدة المناسبة للفلترة
        if (strpos($columnType, 'enum') !== false || 
            strpos($columnType, 'varchar') !== false ||
            strpos($columnType, 'text') !== false ||
            strpos($columnType, 'date') !== false) {
            
            $columns[] = $columnName;
        }
    }
    
    return $columns;
}

// استخدام النظام الديناميكي:
$availableColumns = []; // من الكود الموجود
$dynamicFilters = generateDynamicFilters($conn, $availableColumns);

// أو
$filterableColumns = autoDetectFilterableColumns($conn);
$autoFilters = [];
foreach ($filterableColumns as $column) {
    $autoFilters[$column] = getFilterOptionsIfExists($conn, $column, $availableColumns);
}
?>

<!-- مثال على HTML ديناميكي للفلاتر -->
<?php foreach ($dynamicFilters as $columnName => $filterData): ?>
    <?php if ($filterData): ?>
        <div class="filter-group">
            <label><?php echo ucfirst($columnName); ?></label>
            <select name="<?php echo $columnName; ?>[]" multiple>
                <?php while ($row = $filterData->fetch_assoc()): ?>
                    <option value="<?php echo htmlspecialchars($row[$columnName]); ?>">
                        <?php echo htmlspecialchars($row[$columnName]); ?>
                    </option>
                <?php endwhile; ?>
            </select>
        </div>
    <?php endif; ?>
<?php endforeach; ?>
