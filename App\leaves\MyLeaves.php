<?php
session_start();
if (!isset($_SESSION['username'])) {
    header("Location: ../../Loginpage.php");
    exit();
}

$email = $_SESSION['username'];
include $_SERVER['DOCUMENT_ROOT'].'/config/Databasehost.php';

// تضمين نظام الصلاحيات
include_once $_SERVER['DOCUMENT_ROOT'].'/App/includes/permissions.php';

// التحقق من صلاحية الوصول للصفحة
checkPagePermission('leaves', $_SESSION['access_level'] ?? 'Viewer');

$access = $_SESSION['access_level'];

// Get user profile image
$sql = "SELECT Image FROM usersprofile WHERE Email = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("s", $email);
$stmt->execute();
$result = $stmt->get_result();
$imageData = ($result->num_rows > 0) ? $result->fetch_assoc()['Image'] : '';

// Get leaves data
$leaves_sql = "SELECT LeavesCode, Email, `From`, `To`, TotalDays, Status, Approver, WFMComment, requestdate, LeavesName, Attached, Description FROM leaves WHERE Email = ?";
$leaves_stmt = $conn->prepare($leaves_sql);
$leaves_stmt->bind_param("s", $email);
$leaves_stmt->execute();
$leaves_result = $leaves_stmt->get_result();

// Process name from email
$beforeAt = explode('@', $email)[0];
if (strpos($beforeAt, '.') !== false) {
    $nameParts = explode('.', $beforeAt);
    $firstName = ucfirst($nameParts[0]);
    $lastName = ucfirst($nameParts[1]);
} else {
    $firstName = ucfirst($beforeAt);
    $lastName = '';
}
include $_SERVER['DOCUMENT_ROOT'].'/App/includes/sidebar.php';

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard | My Leaves</title>
    <!-- External Libraries -->
    <?php include $_SERVER['DOCUMENT_ROOT'].'/App/includes/external-libraries.php';?>
    <!-- DataTables CSS already included in external-libraries.php -->
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/unified.css">
    <link rel="stylesheet" href="../assets/css/sidebar.css">
    <link rel="stylesheet" href="../assets/css/unified-tables.css">

    <!-- Fix for DataTables column count issue -->
    <style>
        #leavesTable {
            table-layout: auto !important;
            width: 100% !important;
        }
        #leavesTable thead th,
        #leavesTable tbody td {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        /* Ensure responsive wrapper doesn't interfere */
        .dataTables_wrapper {
            overflow-x: auto;
        }
        /* Hide no-data row when DataTable shows its own empty message */
        .dataTables_wrapper .no-data-row {
            display: none !important;
        }
        /* Style for empty table message */
        .dataTables_empty {
            text-align: center !important;
            font-style: italic;
            color: #6c757d;
            padding: 2rem !important;
        }
        /* Ensure proper column alignment */
        #leavesTable th, #leavesTable td {
            vertical-align: middle;
            text-align: center;
        }
        #leavesTable th:first-child, #leavesTable td:first-child {
            text-align: left;
        }
    </style>
</head>
<body>
    <!-- Mobile Menu Button -->
    <button class="mobile-menu-btn" onclick="toggleMobileMenu()">
        <i class="fas fa-bars"></i>
    </button>

       <?php include $_SERVER['DOCUMENT_ROOT'].'/App/includes/sidebar.php';?>


  <!-- Main Content -->
  <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <?php
            // تحضير المتغيرات للـ welcome section الموحد
            include $_SERVER['DOCUMENT_ROOT'].'/App/includes/unified-welcome.php';
            ?>
        </div>

        <div class="d-flex justify-content-end mb-3">
            <a href="CreateLeaves.php" class="create-leaves-btn">
                <i class="fas fa-plus-circle me-2"></i> New Leave
            </a>
        </div>

        <div class="data-card">
            <div class="card-header">
                <h5><i class="fas fa-calendar-check me-2"></i> My Leave Requests</h5>
            </div>
            <div class="card-body p-0">
                <div class="unified-table-responsive">
                    <table id="leavesTable" class="unified-table table-sm">
                        <thead>
                            <tr>
                                <th><i class="fas fa-hashtag me-1"></i>Code</th>
                                <th><i class="fas fa-calendar-alt me-1"></i>Period</th>
                                <th><i class="fas fa-calculator me-1"></i>Days</th>
                                <th><i class="fas fa-check-circle me-1"></i>Status</th>
                                <th><i class="fas fa-user-check me-1"></i>Approver</th>
                                <th><i class="fas fa-comment me-1"></i>Comments</th>
                                <th><i class="fas fa-calendar-plus me-1"></i>Requested</th>
                                <th><i class="fas fa-tag me-1"></i>Type</th>
                                <th><i class="fas fa-paperclip me-1"></i>Attachment</th>
                                <th><i class="fas fa-info-circle me-1"></i>Details</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if ($leaves_result->num_rows > 0): ?>
                                <?php while ($row = $leaves_result->fetch_assoc()): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($row['LeavesCode']); ?></td>
                                        <td>
                                            <?php echo date('M d', strtotime($row['From'])); ?> -
                                            <?php echo date('M d, Y', strtotime($row['To'])); ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($row['TotalDays']); ?></td>
                                        <td>
                                            <span class="status-badge status-<?php echo strtolower($row['Status']); ?>">
                                                <?php echo htmlspecialchars($row['Status']); ?>
                                            </span>
                                        </td>
                                        <td><?php echo htmlspecialchars($row['Approver'] ?: 'N/A'); ?></td>
                                        <td><?php echo htmlspecialchars($row['WFMComment'] ?: '-'); ?></td>
                                        <td><?php echo date('M d, Y', strtotime($row['requestdate'])); ?></td>
                                        <td><?php echo htmlspecialchars($row['LeavesName']); ?></td>
                                        <td>
                                            <?php if (!empty($row['Attached'])): ?>
                                                <a href="<?php echo htmlspecialchars('../../' . ltrim($row['Attached'], '/')); ?>" target="_blank" class="attachment-link">
                                                    <i class="fas fa-paperclip me-1"></i> View
                                                </a>
                                            <?php else: ?>
                                                <span class="text-muted">None</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary" data-bs-toggle="tooltip"
                                                title="<?php echo htmlspecialchars($row['Description'] ?: 'No description'); ?>">
                                                <i class="fas fa-info-circle"></i>
                                            </button>
                                        </td>
                                    </tr>
                                <?php endwhile; ?>
                            <?php else: ?>
                                <tr class="no-data-row">
                                    <td class="text-center py-3">-</td>
                                    <td class="text-center py-3">-</td>
                                    <td class="text-center py-3">-</td>
                                    <td class="text-center py-3">-</td>
                                    <td class="text-center py-3">-</td>
                                    <td class="text-center py-3">-</td>
                                    <td class="text-center py-3">-</td>
                                    <td class="text-center py-3">-</td>
                                    <td class="text-center py-3">-</td>
                                    <td class="text-center py-3">-</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Simple pagination for large datasets -->
            <?php if ($leaves_result->num_rows > 20): ?>
                <div class="card-footer">
                    <?php
                    include $_SERVER['DOCUMENT_ROOT'].'/App/includes/unified-pagination.php';
                    echo '<div class="pagination-info">Total: <strong>' . $leaves_result->num_rows . '</strong> leave requests</div>';
                    ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Logout modal is now included in sidebar.php -->

    <!-- External Scripts -->
    <?php include $_SERVER['DOCUMENT_ROOT'].'/App/includes/external-scripts.php';?>

    <script>
        // Initialize DataTable with proper column validation
        $(document).ready(function() {
            // Small delay to ensure all scripts are loaded
            setTimeout(function() {
                // Check if DataTable is already initialized
                if ($.fn.DataTable.isDataTable('#leavesTable')) {
                    $('#leavesTable').DataTable().destroy();
                }

                try {
                    // Validate table structure before initialization
                    var headerCount = $('#leavesTable thead tr th').length;
                    var bodyRows = $('#leavesTable tbody tr');
                    var hasData = bodyRows.length > 0 && !bodyRows.first().find('td[colspan]').length;

                    console.log('Header columns:', headerCount);
                    console.log('Has data rows:', hasData);

                    if (hasData) {
                        var firstRowCount = bodyRows.first().find('td').length;
                        console.log('First row columns:', firstRowCount);

                        // Only initialize if column counts match
                        if (headerCount !== firstRowCount) {
                            console.error('Column count mismatch! Header:', headerCount, 'Body:', firstRowCount);
                            return;
                        }
                    }

                    // DataTable configuration
                    var tableConfig = {
                        dom: 'rt',  // Only show table and responsive
                        pageLength: -1,  // Show all records
                        lengthChange: false,
                        searching: false,  // Disable built-in search
                        paging: false,     // Disable built-in pagination
                        info: false,       // Hide info text
                        autoWidth: false,  // Prevent automatic width calculation
                        responsive: {
                            details: {
                                type: 'column',
                                target: 'tr'
                            }
                        },
                        columnDefs: [
                            {
                                targets: '_all',
                                className: 'text-nowrap',
                                responsivePriority: 1
                            },
                            {
                                targets: [0, 1, 3], // Code, Period, Status - always visible
                                responsivePriority: 1
                            },
                            {
                                targets: [4, 5, 6, 7, 8, 9], // Less important columns
                                responsivePriority: 2
                            }
                        ],
                        language: {
                            zeroRecords: "No leave requests found",
                            emptyTable: "No leave requests found"
                        },
                        drawCallback: function() {
                            // Re-initialize tooltips after draw
                            $('[data-bs-toggle="tooltip"]').tooltip('dispose').tooltip();
                        },
                        initComplete: function() {
                            console.log('DataTable initialized successfully');
                        },
                        error: function(xhr, error, thrown) {
                            console.error('DataTable error:', error, thrown);
                        }
                    };

                    // Initialize DataTable
                    var table = $('#leavesTable').DataTable(tableConfig);

                    // Handle DataTable errors
                    table.on('error.dt', function(e, settings, techNote, message) {
                        console.error('DataTable error:', message);
                        // Hide the error and show a user-friendly message
                        $('.dataTables_processing').hide();
                    });

                } catch (error) {
                    console.error('DataTable initialization error:', error);
                    // Fallback: just initialize tooltips without DataTable
                    $('[data-bs-toggle="tooltip"]').tooltip();

                    // Show a user-friendly message
                    if ($('#leavesTable tbody tr').length === 0 || $('#leavesTable tbody tr').hasClass('no-data-row')) {
                        $('#leavesTable tbody').html('<tr><td colspan="10" class="text-center py-4 text-muted"><i class="fas fa-info-circle me-2"></i>No leave requests found</td></tr>');
                    }
                }

            }, 200); // Increased delay to 200ms
        });

        // Sidebar and modal functions are now handled by unified.js

        // Logout function is now handled by sidebar.php

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById("confirmModal");
            if (event.target == modal) {
                closeModal();
            }
        }
    </script>

    <!-- Include Unified Admin JS -->
    <script src="../assets/js/table-scroll-handler.js"></script>
    <script src="../assets/js/unified.js"></script>
</body>
</html>
<?php
// Close database connections
$leaves_stmt->close();
$conn->close();
?>
