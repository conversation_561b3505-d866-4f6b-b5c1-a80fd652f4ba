@import url('root-variables.css');

/* ==================== forced logout , setting , logindata  ==================== */
/* ==================== GENERAL STYLES ==================== */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body.settings-page {
    font-family: 'Roboto', sans-serif;
    background-color: var(--settings-light-color);
    color: var(--settings-text-dark);
    min-height: 100vh;
    overflow-x: hidden;
}

/* ==================== SIDEBAR STYLES ==================== */
.settings-sidebar {
    width: 260px;
    background: linear-gradient(180deg, var(--settings-primary-dark), var(--settings-primary-color));
    color: var(--settings-white);
    height: 100vh;
    position: fixed;
    overflow-y: auto;
    z-index: 1000;
    padding: 15px;
    box-shadow: 2px 0 15px rgba(0, 0, 0, 0.1);
    transition: var(--settings-transition);
    left: 0;
}

.settings-sidebar-header {
    display: flex;
    justify-content: center;
    padding: 20px 0;
    margin-bottom: 30px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.settings-sidebar-header img {
    width: 180px;
    height: auto;
    object-fit: contain;
}

.settings-sidebar-nav {
    padding: 10px 0;
}

.settings-sidebar-nav a {
    color: var(--settings-white);
    text-decoration: none;
    display: flex;
    align-items: center;
    padding: 12px 15px;
    margin: 5px 0;
    border-radius: var(--settings-border-radius);
    transition: var(--settings-transition);
    position: relative;
    overflow: hidden;
}

.settings-sidebar-nav a i {
    margin-right: 15px;
    font-size: 1.1rem;
    min-width: 24px;
    text-align: center;
}

.settings-sidebar-nav a::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 3px;
    background-color: var(--settings-accent-color);
    transform: translateX(-100%);
    transition: var(--settings-transition);
}

.settings-sidebar-nav a:hover::before,
.settings-sidebar-nav a:focus::before {
    transform: translateX(0);
}

.settings-sidebar-nav a:hover,
.settings-sidebar-nav a:focus {
    background-color: rgba(255, 255, 255, 0.05);
    transform: translateX(5px);
    outline: none;
}

/* Dropdown Navigation */
.settings-dropdown-content {
    display: none;
    padding-left: 15px;
    background-color: var(--settings-primary-light);
    border-radius: var(--settings-border-radius);
    margin: 8px 0;
    overflow: hidden;
    animation: settings-fadeIn 0.3s ease-out;
}

.settings-dropdown:hover .settings-dropdown-content,
.settings-dropdown:focus-within .settings-dropdown-content {
    display: block;
}

.settings-dropdown-content a {
    padding: 10px 15px;
    margin: 0;
    border-radius: 0;
}

/* ==================== MAIN CONTENT ==================== */
.settings-content {
    flex: 1;
    padding: 30px;
    margin-left: 260px;
    transition: var(--settings-transition);
}

.settings-title {
    color: var(--settings-primary-dark);
    margin-bottom: 30px;
    font-size: 2rem;
    position: relative;
}

.settings-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 60px;
    height: 4px;
    background-color: var(--settings-primary-color);
    border-radius: 2px;
}

/* ==================== FORM STYLES ==================== */
.settings-password-form-container {
    max-width: 600px;
    margin: 0 auto;
    animation: settings-fadeIn 0.5s ease-out;
}

.settings-password-form {
    background: var(--settings-white);
    padding: 30px;
    border-radius: var(--settings-border-radius);
    box-shadow: var(--settings-shadow);
    transition: var(--settings-transition);
    border: 1px solid var(--settings-border-color);
}

.settings-password-form:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.settings-form-group {
    margin-bottom: 25px;
    position: relative;
}

.settings-form-group label {
    display: block;
    margin-bottom: 10px;
    color: var(--settings-text-dark);
    font-weight: 500;
    font-size: 1rem;
}

.settings-form-control {
    width: 100%;
    padding: 14px 16px;
    border: 2px solid var(--settings-border-color);
    border-radius: var(--settings-border-radius);
    font-size: 16px;
    transition: var(--settings-transition);
    background-color: var(--settings-white);
}

.settings-form-control:focus {
    border-color: var(--settings-primary-light);
    outline: none;
    box-shadow: 0 0 0 3px rgba(106, 27, 154, 0.1);
}

/* ==================== BUTTON STYLES ==================== */
.settings-btn {
    display: inline-block;
    padding: 14px 24px;
    background: linear-gradient(135deg, var(--settings-primary-color), var(--settings-primary-dark));
    color: var(--settings-white);
    border: none;
    border-radius: var(--settings-border-radius);
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--settings-transition);
    text-align: center;
    width: 100%;
    letter-spacing: 0.5px;
    text-transform: uppercase;
}

.settings-btn:hover,
.settings-btn:focus {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(106, 27, 154, 0.3);
    outline: none;
}

.settings-btn:active {
    transform: translateY(0);
}

/* ==================== ALERT & MESSAGES ==================== */
.settings-alert {
    padding: 15px;
    border-radius: var(--settings-border-radius);
    margin-bottom: 25px;
    position: relative;
    animation: settings-slideIn 0.3s ease-out;
}

.settings-alert i {
    margin-right: 8px;
}

.settings-alert-success {
    background-color: rgba(0, 200, 83, 0.1);
    color: var(--settings-success-color);
    border-left: 4px solid var(--settings-success-color);
}

.settings-alert-danger {
    background-color: rgba(213, 0, 0, 0.1);
    color: var(--settings-danger-color);
    border-left: 4px solid var(--settings-danger-color);
}

.settings-error-message {
    color: var(--settings-danger-color);
    font-size: 14px;
    margin-top: 8px;
    display: block;
    animation: settings-shake 0.5s;
}

/* ==================== MODAL STYLES ==================== */
.settings-modal {
    display: none;
    position: fixed;
    z-index: 1050;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(3px);
    animation: settings-fadeInModal 0.3s ease-out;
    overflow-y: auto;
}

.settings-modal.show {
    display: block;
}

.settings-modal-content {
    background-color: var(--settings-white);
    margin: 10vh auto;
    padding: 30px;
    border-radius: var(--settings-border-radius);
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    max-width: 400px;
    width: 90%;
    animation: settings-slideDown 0.3s ease-out;
    border-top: 4px solid var(--settings-primary-color);
}

.settings-modal-title {
    margin-bottom: 20px;
    color: var(--settings-primary-dark);
}

.settings-modal-text {
    margin-bottom: 25px;
    color: var(--settings-text-medium);
}

.settings-modal-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.settings-modal-btn {
    padding: 12px 24px;
    border: none;
    border-radius: var(--settings-border-radius);
    font-weight: 500;
    cursor: pointer;
    transition: var(--settings-transition);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 100px;
}

.settings-modal-btn i {
    margin-right: 8px;
}

.settings-modal-btn-confirm {
    background: linear-gradient(135deg, var(--settings-success-color), #00b248);
    color: var(--settings-white);
}

.settings-modal-btn-cancel {
    background: linear-gradient(135deg, var(--settings-danger-color), #b71c1c);
    color: var(--settings-white);
}

.settings-modal-btn:hover {
    opacity: 0.9;
    transform: translateY(-2px);
}

/* ==================== MOBILE STYLES ==================== */
.settings-mobile-menu-btn {
    display: none;
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1100;
    background: var(--settings-primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 45px;
    height: 45px;
    font-size: 22px;
    cursor: pointer;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    transition: var(--settings-transition);
}

.settings-mobile-menu-btn:hover {
    transform: scale(1.1);
}

.settings-mobile-menu-btn:active {
    transform: scale(0.95);
}

/* ==================== LOADER & OVERLAY ==================== */
.settings-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.settings-loader i {
    color: white;
    font-size: 3rem;
    animation: fa-spin 2s infinite linear;
}

.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    z-index: 999;
}

/* ==================== ANIMATIONS ==================== */
@keyframes settings-fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes settings-slideIn {
    from { opacity: 0; transform: translateX(-15px); }
    to { opacity: 1; transform: translateX(0); }
}

@keyframes settings-shake {
    0%, 100% { transform: translateX(0); }
    20%, 60% { transform: translateX(-5px); }
    40%, 80% { transform: translateX(5px); }
}

@keyframes settings-fadeInModal {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes settings-slideDown {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* ==================== RESPONSIVE DESIGN ==================== */
@media (max-width: 768px) {
    .settings-sidebar {
        width: 260px;
        transform: translateX(-100%);
    }

    .settings-sidebar-active {
        transform: translateX(0);
        box-shadow: 2px 0 20px rgba(0, 0, 0, 0.2);
    }

    .settings-content {
        margin-left: 0;
        padding: 20px 15px;
    }

    .settings-mobile-menu-btn {
        display: block;
    }

    .settings-password-form {
        padding: 20px;
    }

    .settings-form-control {
        padding: 12px 14px;
    }

    .settings-btn {
        padding: 12px 20px;
    }
}

@media (max-width: 480px) {
    .settings-content {
        padding: 15px;
    }

    .settings-password-form-container {
        padding: 0 10px;
    }

    .settings-modal-content {
        width: 95%;
        padding: 20px;
    }

    .settings-modal-buttons {
        flex-direction: column;
        gap: 10px;
    }

    .settings-modal-btn {
        width: 100%;
    }
}

/* ==================== ACCESSIBILITY ==================== */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
}
/* ==================== login-data ==================== */

.container {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
}
h2 {
    color: var(--primary-dark);
    font-weight: 600;
}
.btn-primary {
    background-color: var(--primary-dark);
    border: none;
}
.btn-primary:hover {
    background-color: var(--primary-darker);
}
.table thead {
    background-color: var(--primary-dark);
    color: var(--white);
}
.pagination .page-item.active .page-link {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}
.form-control {
    border-radius: var(--border-radius);
}

/* ==================== forced logout ==================== */



.admin-forced-offline-body {
    background-color: var(--settings-light-color);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    padding: 20px;
}

.admin-forced-offline-container {
    max-width: 1800px;
    margin: 0 auto;
}

.admin-forced-offline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 15px;
}

.admin-forced-offline-title {
    color: var(--settings-primary-dark);
    font-weight: 700;
    margin: 0;
}

.admin-forced-offline-stats .badge {
    font-size: 1rem;
    padding: 8px 12px;
    border-radius: var(--settings-border-radius);
}

.admin-forced-offline-filters {
    box-shadow: var(--settings-shadow);
    border-radius: var(--settings-border-radius);
    border: none;
}

.admin-forced-offline-table {
    box-shadow: var(--settings-shadow);
    border-radius: var(--settings-border-radius);
    border: none;
    overflow: hidden;
}

.table thead th {
    background-color: var(--settings-primary-color);
    color: white;
    font-weight: 600;
    text-align: center;
    vertical-align: middle;
}

.table tbody tr {
    transition: var(--settings-transition);
}

.table tbody tr:hover {
    background-color: rgba(199, 154, 199, 0.1);
}

.table tbody td {
    vertical-align: middle;
    text-align: center;
}

.btn-action {
    padding: 5px 10px;
    font-size: 0.875rem;
    border-radius: var(--settings-border-radius);
    transition: var(--settings-transition);
}

.btn-offline {
    background-color: var(--settings-danger-color);
    color: white;
    border: none;
}

.btn-offline:hover {
    background-color: #d32f2f;
    color: white;
}

.status-online {
    color: var(--settings-success-color);
    font-weight: 600;
}

.status-offline {
    color: var(--settings-danger-color);
    font-weight: 600;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .admin-forced-offline-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .admin-forced-offline-stats {
        margin-top: 10px;
    }
    
    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
}
       
/* Default Avatar Styles */
.top-user-info {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    background-color: #fff;
    border-bottom: 1px solid #eee;
}

.top-user-img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--primary-light);
    margin-right: 15px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.top-user-img.default-avatar {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(145deg, #f8f8f8, #ffffff);
    border: 2px solid var(--primary-light);
    width: 50px;
    height: 50px;
    border-radius: 50%;
}

.top-user-img.default-avatar i {
    font-size: 1.4em;
    color: var(--primary-dark);
}

.user-name-info h3 {
    margin: 0;
    font-size: 1.1em;
    color: #333;
}

.user-name-info p {
    margin: 2px 0 0;
    font-size: 0.9em;
    color: #777;
}
