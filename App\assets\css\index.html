<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Call System</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #d7dde4;
            --primary-light: #d3dbe3;
            --primary-dark: #aacdf3;
            --primary-100: #e8f3fd;
            --primary-200: #d3e8fb;
            --primary-300: #bedcfa;
            --primary-400: #aacdf3;
            --primary-500: #87b8e8;
            --primary-600: #5fa0da;
            --primary-700: #3c86c4;
            --primary-800: #216ca7;
            --primary-900: #0c5284;
            --text-color: #333;
            --bg-color: #f9f5f9;
            --card-bg: #ffffff;
            --success: #2ecc71;
            --danger: #e74c3c;
        }

        body {
            background-color: var(--bg-color);
            height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: var(--text-color);
        }
        
        .call-container {
            background-color: var(--card-bg);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(0, 0, 0, 0.05);
            max-width: 400px;
            margin: 0 auto;
            position: relative;
        }
        
        .call-status-indicator {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: var(--danger);
        }
        
        .active-call .call-status-indicator {
            background-color: var(--success);
            animation: pulse 2s infinite;
        }
        
        .call-avatar {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            border: 3px solid var(--primary-200);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            margin: 0 auto 20px;
            background: linear-gradient(45deg, var(--primary-100), var(--primary-300));
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .call-avatar i {
            font-size: 3.5rem;
            color: var(--primary-800);
        }
        
        .call-title {
            font-weight: 700;
            color: var(--primary-900);
            margin-bottom: 5px;
        }
        
        .caller-number {
            color: var(--primary-700);
            font-size: 1.1rem;
            min-height: 1.5rem;
        }
        
        .btn-call {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            font-size: 24px;
            border: none;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
        }
        
        .btn-call:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }
        
        .btn-accept {
            background: linear-gradient(45deg, var(--primary-600), var(--primary-800));
        }
        
        .btn-reject {
            background: linear-gradient(45deg, var(--danger), #c0392b);
        }
        
        .btn-end {
            background: linear-gradient(45deg, var(--primary-700), var(--primary-900));
        }
        
        .call-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 30px;
        }
        
        .call-timer {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-800);
            margin-top: 20px;
        }
        
        .hidden {
            display: none !important;
        }
        
        @keyframes pulse {
            0% { transform: scale(0.95); box-shadow: 0 0 0 0 rgba(46, 213, 115, 0.7); }
            70% { transform: scale(1); box-shadow: 0 0 0 10px rgba(46, 213, 115, 0); }
            100% { transform: scale(0.95); box-shadow: 0 0 0 0 rgba(46, 213, 115, 0); }
        }
        
        .permission-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        
        .permission-content {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            max-width: 500px;
            text-align: center;
        }
    </style>
</head>
<body class="d-flex align-items-center">
    <!-- Permission Request Modal -->
    <div class="permission-modal" id="permissionModal">
        <div class="permission-content">
            <h3>Permission Required</h3>
            <p>This application needs microphone permissions to make and receive calls.</p>
            <button class="btn btn-primary" id="grantPermissionBtn">Grant Permission</button>
        </div>
    </div>

    <div class="container">
        <div class="call-container">
            <div class="call-status-indicator"></div>
            <div class="caller-info text-center">
                <div class="call-avatar">
                    <i class="fas fa-phone-alt"></i>
                </div>
                <h2 class="call-title" id="callStatusText">Ready for calls</h2>
                <p class="caller-number" id="callerNumber"></p>
                <div class="call-timer hidden" id="callTimer">00:00</div>
            </div>
            
            <div class="call-buttons">
                <button id="acceptBtn" class="btn-call btn-accept hidden">
                    <i class="fas fa-phone-alt"></i>
                </button>
                <button id="rejectBtn" class="btn-call btn-reject hidden">
                    <i class="fas fa-phone-slash"></i>
                </button>
                <button id="endBtn" class="btn-call btn-end hidden">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Audio elements -->
    <audio id="ringtone" loop>
        <source src="https://assets.mixkit.co/sfx/preview/mixkit-classic-phone-ring-943.mp3" type="audio/mpeg">
        Your browser does not support the audio element.
    </audio>
    
    <audio id="hangupSound">
        <source src="https://assets.mixkit.co/sfx/preview/mixkit-quick-jump-arcade-game-239.mp3" type="audio/mpeg">
        Your browser does not support the audio element.
    </audio>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Twilio Client SDK -->
    <script src="https://sdk.twilio.com/js/client/v1.13/twilio.min.js"></script>
    <script>
        // UI Elements
        const elements = {
            container: document.querySelector('.call-container'),
            callStatusText: document.getElementById('callStatusText'),
            callerNumber: document.getElementById('callerNumber'),
            callTimer: document.getElementById('callTimer'),
            acceptBtn: document.getElementById('acceptBtn'),
            rejectBtn: document.getElementById('rejectBtn'),
            endBtn: document.getElementById('endBtn'),
            ringtone: document.getElementById('ringtone'),
            hangupSound: document.getElementById('hangupSound'),
            permissionModal: document.getElementById('permissionModal'),
            grantPermissionBtn: document.getElementById('grantPermissionBtn')
        };

        // Call state variables
        let device;
        let currentConnection = null;
        let callTimerInterval;
        let callStartTime;

        // Initialize application
        function initApp() {
            requestMicrophonePermission();
            setupEventListeners();
        }

        // Request microphone permission
        function requestMicrophonePermission() {
            // Check if we already have permission
            if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                navigator.mediaDevices.getUserMedia({ audio: true })
                    .then(() => {
                        // Permission already granted
                        elements.permissionModal.style.display = 'none';
                        initTwilio();
                    })
                    .catch(() => {
                        // Show permission modal
                        elements.permissionModal.style.display = 'flex';
                    });
            } else {
                // Browser doesn't support permissions API
                alert("Your browser doesn't support microphone access. Please use a modern browser.");
            }
        }

        // Setup event listeners
        function setupEventListeners() {
            elements.acceptBtn.addEventListener('click', acceptCall);
            elements.rejectBtn.addEventListener('click', rejectCall);
            elements.endBtn.addEventListener('click', endCall);
            elements.grantPermissionBtn.addEventListener('click', grantPermission);
        }

        // Grant permission button handler
        function grantPermission() {
            navigator.mediaDevices.getUserMedia({ audio: true })
                .then(() => {
                    elements.permissionModal.style.display = 'none';
                    initTwilio();
                })
                .catch(error => {
                    console.error('Permission denied:', error);
                    alert('Microphone permission is required for this application to work.');
                });
        }

        // Initialize Twilio device
        function initTwilio() {
            fetch('token.php')
                .then(response => response.text())
                .then(token => {
                    device = new Twilio.Device(token, { debug: true });

                    device.on('ready', () => {
                        updateUI('ready');
                        console.log('Twilio Device is ready');
                    });
                    
                    device.on('error', handleError);
                    device.on('incoming', handleIncomingCall);
                })
                .catch(error => {
                    console.error('Error initializing Twilio:', error);
                    updateUI('error', 'Failed to initialize');
                });
        }

        // Handle incoming call
        function handleIncomingCall(connection) {
            currentConnection = connection;
            const callerId = connection.parameters.From || 'Unknown';
            
            // Play ringtone
            playRingtone();
            
            // Set up call event handlers
            connection.on('accept', () => {
                stopRingtone();
                startCallTimer();
                updateUI('active', callerId);
            });
            
            connection.on('disconnect', () => {
                playHangupSound();
                endCall(false);
            });
            
            connection.on('cancel', () => {
                playHangupSound();
                endCall(false);
            });

            updateUI('incoming', callerId);
        }

        // Play ringtone
        function playRingtone() {
            elements.ringtone.currentTime = 0;
            elements.ringtone.play()
                .then(() => console.log('Ringtone playing'))
                .catch(error => console.error('Error playing ringtone:', error));
        }

        // Stop ringtone
        function stopRingtone() {
            elements.ringtone.pause();
        }

        // Play hangup sound
        function playHangupSound() {
            elements.hangupSound.currentTime = 0;
            elements.hangupSound.play()
                .then(() => console.log('Hangup sound played'))
                .catch(error => console.error('Error playing hangup sound:', error));
        }

        // Accept call
        function acceptCall() {
            if (currentConnection) {
                currentConnection.accept();
            }
        }

        // Reject call
        function rejectCall() {
            if (currentConnection) {
                currentConnection.reject();
                endCall(true);
            }
        }

        // End active call
        function endCall(manual) {
            if (currentConnection) {
                currentConnection.disconnect();
            }
            
            clearInterval(callTimerInterval);
            currentConnection = null;
            updateUI('ended');
        }

        // Start call timer
        function startCallTimer() {
            callStartTime = new Date();
            clearInterval(callTimerInterval);
            callTimerInterval = setInterval(updateTimer, 1000);
        }

        // Update call timer display
        function updateTimer() {
            const now = new Date();
            const elapsed = new Date(now - callStartTime);
            const minutes = elapsed.getUTCMinutes().toString().padStart(2, '0');
            const seconds = elapsed.getUTCSeconds().toString().padStart(2, '0');
            elements.callTimer.textContent = `${minutes}:${seconds}`;
        }

        // Update UI based on state
        function updateUI(state, callerId = '') {
            // Reset all UI elements
            elements.container.classList.remove('active-call');
            elements.acceptBtn.classList.add('hidden');
            elements.rejectBtn.classList.add('hidden');
            elements.endBtn.classList.add('hidden');
            elements.callTimer.classList.add('hidden');
            
            // Update based on state
            switch(state) {
                case 'ready':
                    elements.callStatusText.textContent = 'Ready for calls';
                    elements.callerNumber.textContent = '';
                    break;
                    
                case 'incoming':
                    elements.callStatusText.textContent = 'Incoming Call';
                    elements.callerNumber.textContent = formatNumber(callerId);
                    elements.acceptBtn.classList.remove('hidden');
                    elements.rejectBtn.classList.remove('hidden');
                    break;
                    
                case 'active':
                    elements.callStatusText.textContent = 'Active Call';
                    elements.callerNumber.textContent = formatNumber(callerId);
                    elements.endBtn.classList.remove('hidden');
                    elements.callTimer.classList.remove('hidden');
                    elements.container.classList.add('active-call');
                    break;
                    
                case 'ended':
                    elements.callStatusText.textContent = 'Call Ended';
                    setTimeout(() => updateUI('ready'), 2000);
                    break;
                    
                case 'error':
                    elements.callStatusText.textContent = 'Connection Error';
                    elements.callerNumber.textContent = 'Please refresh the page';
                    break;
            }
        }

        // Format phone number
        function formatNumber(number) {
            // Simple formatting - customize as needed
            return number.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
        }

        // Handle errors
        function handleError(error) {
            console.error('Twilio Error:', error.message);
            updateUI('error', error.message);
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', initApp);
    </script>
</body>
</html>