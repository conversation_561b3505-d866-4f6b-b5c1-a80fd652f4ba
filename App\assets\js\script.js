// Sidebar and modal functions are now handled by unified.js
// Keeping only specific functions for Home.php

// Logout function
function logout() {
    console.log('🚀 Starting logout process from script.js...');

    // إرسال طلب AJAX لتحديث وقت تسجيل الخروج
    var xhr = new XMLHttpRequest();
    xhr.open("POST", "/App/Logpage/logout.php", true);
    xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
    xhr.setRequestHeader("X-Requested-With", "XMLHttpRequest");
    xhr.timeout = 5000;
    xhr.onreadystatechange = function () {
        if (xhr.readyState === XMLHttpRequest.DONE) {
            console.log('📤 Logout request completed with status:', xhr.status);

            if (xhr.status === 200) {
                try {
                    var response = JSON.parse(xhr.responseText);
                    console.log('✅ Logout response:', response);

                    if (response.status === 'success') {
                        console.log('🚀 Logout successful, redirecting...');
                        window.location.href = '/Loginpage';
                    } else {
                        console.error('❌ Logout failed:', response.message);
                        // Still redirect even if failed
                        window.location.href = '/Loginpage';
                    }
                } catch (e) {
                    console.error('❌ JSON parse error:', e);
                    console.log('Raw response:', xhr.responseText);
                    // Still redirect even if parsing failed
                    window.location.href = '/Loginpage';
                }
            } else {
                console.error('❌ Logout request failed with status:', xhr.status);
                // Still redirect even if request failed
                window.location.href = '/Loginpage';
            }
        }
    };

    // Add timeout handler
    xhr.ontimeout = function() {
        console.error('⏰ Logout request timed out');
        window.location.href = '/Loginpage';
    };

    // Add error handler
    xhr.onerror = function() {
        console.error('🌐 Network error during logout');
        window.location.href = '/Loginpage';
    };

    xhr.send("update_logout_time=1");
}

// Initialize chart
function initChart(chartData) {
    console.log('🚀 initChart called with data:', chartData);

    try {
        // تحقق من وجود Highcharts
        if (typeof Highcharts === 'undefined') {
            console.error('❌ Highcharts library is not loaded');
            showChartError('Chart library not loaded');
            return;
        }
        console.log('✅ Highcharts library found, version:', Highcharts.version);

        // تحقق من صحة البيانات
        let data;
        try {
            data = JSON.parse(chartData);
            console.log('✅ Chart data parsed successfully:', data);
        } catch (e) {
            console.error('❌ Invalid chart data JSON:', e);
            console.error('Raw chart data:', chartData);
            showChartError('Invalid chart data: ' + e.message);
            return;
        }

        // إذا كانت البيانات فارغة، أظهر رسالة
        if (!data || data.length === 0) {
            console.warn('⚠️ No chart data available');
            showChartError('No data available for chart');
            return;
        }
        console.log('📊 Chart data validation passed, data points:', data.length);

        // التحقق من حالات خاصة (No Data أو Error)
        if (data.length === 1) {
            const firstItem = data[0];
            console.log('🔍 Single data point detected:', firstItem);
            if (firstItem.name === 'No Data') {
                console.warn('⚠️ No Data message detected');
                showChartError('No online hours data available for current month');
                return;
            }
            if (firstItem.name === 'Error') {
                console.error('❌ Error message detected:', firstItem.message);
                showChartError(firstItem.message || 'Error loading chart data');
                return;
            }
        }

        // الحصول على الألوان من CSS variables
        console.log('🎨 Getting CSS variables...');
        const rootStyles = getComputedStyle(document.documentElement);
        const primaryColor = rootStyles.getPropertyValue('--primary-500').trim();
        const primaryLight = rootStyles.getPropertyValue('--primary-300').trim();
        const primaryDark = rootStyles.getPropertyValue('--primary-600').trim();
        const grayColor = rootStyles.getPropertyValue('--gray-500').trim();
        const textDark = rootStyles.getPropertyValue('--text-dark').trim();
        const white = rootStyles.getPropertyValue('--white').trim();

        console.log('🎨 CSS Variables loaded:', {
            primaryColor,
            primaryLight,
            primaryDark,
            grayColor,
            textDark,
            white
        });

        // إنشاء الشارت
        console.log('📊 Creating Highcharts chart...');
        const chart = Highcharts.chart('chart', {
            chart: {
                type: 'column',
                backgroundColor: 'transparent',
                height: 350
            },
            title: {
                text: 'Daily Online Hours',
                style: {
                    color: primaryDark || '#7d71ac',
                    fontFamily: 'Poppins, sans-serif',
                    fontSize: '18px',
                    fontWeight: '600'
                }
            },
            subtitle: {
                text: 'Total online duration per day from interpretersaux',
                style: {
                    color: grayColor || '#757575',
                    fontFamily: 'Poppins, sans-serif'
                }
            },
            xAxis: {
                type: 'category',
                title: {
                    text: 'Date',
                    style: {
                        color: grayColor || '#757575',
                        fontFamily: 'Poppins, sans-serif'
                    }
                },
                labels: {
                    style: {
                        color: grayColor || '#757575',
                        fontFamily: 'Poppins, sans-serif'
                    },
                    formatter: function() {
                        // تنسيق التاريخ بصيغة كاملة (Dec 1, Dec 2, etc.)
                        const date = new Date(this.value);
                        return date.toLocaleDateString('en-US', {
                            month: 'short',
                            day: 'numeric'
                        });
                    },
                    rotation: -45, // دوران النص لتجنب التداخل
                    step: 1 // عرض جميع التواريخ
                }
            },
            yAxis: {
                title: {
                    text: 'Online Hours',
                    style: {
                        color: grayColor || '#757575',
                        fontFamily: 'Poppins, sans-serif'
                    }
                },
                labels: {
                    style: {
                        color: grayColor || '#757575',
                        fontFamily: 'Poppins, sans-serif'
                    }
                },
                min: 0
            },
            legend: {
                enabled: false
            },
            plotOptions: {
                column: {
                    color: {
                        linearGradient: { x1: 0, y1: 0, x2: 0, y2: 1 },
                        stops: [
                            [0, primaryLight || '#d6d1e9'],
                            [1, primaryColor || '#9e90d7']
                        ]
                    },
                    borderWidth: 0,
                    borderRadius: 3,
                    dataLabels: {
                        enabled: true,
                        format: '{point.y}h',
                        style: {
                            color: textDark || '#333',
                            textOutline: 'none',
                            fontFamily: 'Poppins, sans-serif',
                            fontSize: '11px',
                            fontWeight: '500'
                        }
                    },
                    states: {
                        hover: {
                            color: primaryDark || '#7d71ac'
                        }
                    }
                }
            },
            tooltip: {
                backgroundColor: 'rgba(255, 255, 255, 0.95)',
                borderColor: primaryLight || '#d6d1e9',
                borderRadius: 8,
                style: {
                    fontFamily: 'Poppins, sans-serif'
                },
                formatter: function() {
                    const date = new Date(this.point.name);
                    const formattedDate = date.toLocaleDateString('en-US', {
                        weekday: 'short',
                        month: 'short',
                        day: 'numeric',
                        year: 'numeric'
                    });
                    return `<b>${formattedDate}</b><br/>Online Hours: <b>${this.point.y}</b>`;
                }
            },
            series: [{
                name: 'Online Hours',
                data: data
            }],
            credits: {
                enabled: false
            },
            responsive: {
                rules: [{
                    condition: {
                        maxWidth: 500
                    },
                    chartOptions: {
                        chart: {
                            height: 300
                        },
                        title: {
                            style: {
                                fontSize: '16px'
                            }
                        },
                        plotOptions: {
                            column: {
                                dataLabels: {
                                    enabled: false
                                }
                            }
                        }
                    }
                }]
            }
        });

        console.log('✅ Highcharts chart created successfully:', chart);

    } catch (error) {
        console.error('❌ Error initializing chart:', error);
        console.error('Error details:', {
            name: error.name,
            message: error.message,
            stack: error.stack
        });
        showChartError('Failed to initialize chart: ' + error.message);
    }
}

// عرض رسالة خطأ في حاوي الشارت
function showChartError(message) {
    console.log('📢 Showing chart error:', message);
    const chartContainer = document.getElementById('chart');
    if (chartContainer) {
        // تحديد نوع الرسالة والأيقونة المناسبة
        let icon = 'fas fa-chart-column';
        let iconColor = '#d0a4d0';
        let additionalInfo = 'Chart data will appear here when available';

        if (message.includes('No online hours data')) {
            icon = 'fas fa-calendar-times';
            iconColor = '#ffa726';
            additionalInfo = 'Data may be available for previous months. Check interpretersaux table for current month data.';
        } else if (message.includes('Error')) {
            icon = 'fas fa-exclamation-triangle';
            iconColor = '#ef5350';
            additionalInfo = 'Please check the database connection and try again.';
        }

        chartContainer.innerHTML = `
            <div style="
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                height: 350px;
                background: #f8f9fa;
                border-radius: 8px;
                border: 2px dashed ${iconColor};
                color: #757575;
                font-family: 'Poppins', sans-serif;
            ">
                <i class="${icon}" style="font-size: 48px; margin-bottom: 15px; color: ${iconColor};"></i>
                <h3 style="margin: 0 0 10px 0; color: #b38cb3;">${message}</h3>
                <p style="margin: 0; font-size: 14px; max-width: 400px; line-height: 1.4; text-align: center;">${additionalInfo}</p>
            </div>
        `;
        console.log('📢 Chart error message displayed');
    } else {
        console.error('❌ Chart container not found for error display');
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('📄 script.js DOM loaded event fired');
    const chartElement = document.getElementById('chart');

    if (chartElement) {
        console.log('✅ Chart element found');
        const chartData = chartElement.getAttribute('data-chart');

        if (chartData) {
            console.log('✅ Chart data attribute found, length:', chartData.length);
            initChart(chartData);
        } else {
            console.warn('⚠️ No chart data attribute found');
        }
    } else {
        console.warn('⚠️ Chart element not found');
    }
});
