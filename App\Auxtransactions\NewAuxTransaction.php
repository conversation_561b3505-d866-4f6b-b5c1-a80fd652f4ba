<?php
// Include the database connection file
include $_SERVER['DOCUMENT_ROOT'].'/config/Databasehost.php';

// Number of rows to display per page
$limit = 700;

// Fetch unique AuxName values
$auxNames = [];
$auxQuery = "SELECT DISTINCT AuxName FROM interpretersaux";
$auxResult = $conn->query($auxQuery);
if ($auxResult->num_rows > 0) {
    while ($row = $auxResult->fetch_assoc()) {
        $auxNames[] = $row['AuxName'];
    }
}

// Default filter values
$startDate = $_GET['start_date'] ?? '';
$endDate = $_GET['end_date'] ?? '';
$selectedAuxName = $_GET['aux_name'] ?? '';
$searchQuery = $_GET['search'] ?? '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$offset = ($page - 1) * $limit;

// SQL query to fetch the required data with filters
$sql = "SELECT Email, AuxName, Duration, Starttime, Endtime FROM interpretersaux WHERE 1=1";

// Add date filter if dates are provided
if (!empty($startDate)) {
    $sql .= " AND Starttime >= '$startDate'";
}
if (!empty($endDate)) {
    $sql .= " AND Endtime <= '$endDate'";
}

// Add filter for AuxName if provided
if (!empty($selectedAuxName)) {
    $sql .= " AND AuxName = '" . $conn->real_escape_string($selectedAuxName) . "'";
}

// Add search filter if search query is provided
if (!empty($searchQuery)) {
    $safeSearch = $conn->real_escape_string($searchQuery);
    $sql .= " AND (
        Email LIKE '%$safeSearch%' OR 
        AuxName LIKE '%$safeSearch%' OR 
        Duration LIKE '%$safeSearch%' OR 
        Starttime LIKE '%$safeSearch%' OR 
        Endtime LIKE '%$safeSearch%'
    )";
}

// Order the results
$sql .= " ORDER BY Starttime DESC LIMIT $limit OFFSET $offset";

// Execute the query
$result = $conn->query($sql);

// Create an array to store the results
$data = [];
if ($result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $data[] = $row;
}}

// Calculate the total number of records for pagination
$totalQuery = "SELECT COUNT(*) as total FROM interpretersaux WHERE 1=1";
if (!empty($startDate)) {
    $totalQuery .= " AND Starttime >= '$startDate'";
}
if (!empty($endDate)) {
    $totalQuery .= " AND Endtime <= '$endDate'";
}
if (!empty($selectedAuxName)) {
    $totalQuery .= " AND AuxName = '" . $conn->real_escape_string($selectedAuxName) . "'";
}
if (!empty($searchQuery)) {
    $totalQuery .= " AND (
        Email LIKE '%$safeSearch%' OR 
        AuxName LIKE '%$safeSearch%' OR 
        Duration LIKE '%$safeSearch%' OR 
        Starttime LIKE '%$safeSearch%' OR 
        Endtime LIKE '%$safeSearch%'
    )";
}

$totalResult = $conn->query($totalQuery);
$totalRow = $totalResult->fetch_assoc();
$totalRecords = $totalRow['total'];
$totalPages = ceil($totalRecords / $limit);

// Export data to CSV when the export button is clicked
if (isset($_POST['export_csv'])) {
    // Set the file headers
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="data.csv"');

    // Open write stream
    $output = fopen('php://output', 'w');

    // Add column headers
    fputcsv($output, ['Email', 'AuxName', 'Duration', 'Starttime', 'Endtime']);

    // Add data rows
    foreach ($data as $row) {
        fputcsv($output, $row);
    }

    // Close the write stream
    fclose($output);
    exit;
}

// Close the database connection
$conn->close();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data View</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background-color: #f4f4f9;
            margin: 0;
            padding: 0;
        }

        h1 {
            text-align: center;
            color: #333;
            margin-top: 20px;
        }

        .container {
            width: 80%;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            margin-top: 30px;
        }

        form {
            margin-bottom: 20px;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
        }

        label {
            margin: 5px 10px 5px 0;
            font-weight: bold;
        }

        input[type="date"], input[type="text"], select, button {
            padding: 8px;
            margin: 5px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }

        input[type="date"], input[type="text"], select {
            width: 200px;
        }

        button {
            background-color: #007bff;
            color: white;
            border: none;
            cursor: pointer;
        }

        button:hover {
            background-color: #0056b3;
        }

        .export-btn {
            background-color: #28a745;
        }

        .export-btn:hover {
            background-color: #218838;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        table, th, td {
            border: 1px solid #ddd;
        }

        th, td {
            padding: 12px 15px;
            text-align: left;
        }

        th {
            background-color: #f2f2f2;
            color: #333;
        }

        tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        tr:hover {
            background-color: #f1f1f1;
        }

        .pagination {
            margin-top: 20px;
            text-align: center;
        }

        .pagination a {
            padding: 8px 15px;
            margin: 0 5px;
            text-decoration: none;
            background-color: #f1f1f1;
            border: 1px solid #ddd;
            border-radius: 4px;
            color: #007bff;
        }

        .pagination a:hover {
            background-color: #007bff;
            color: white;
        }

        .pagination a.active {
            background-color: #007bff;
            color: white;
        }

        .no-data {
            text-align: center;
            font-size: 18px;
            color: #777;
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Data Table</h1>

        <!-- Filter form -->
        <form method="GET">
            <label for="start_date">From Date:</label>
            <input type="date" id="start_date" name="start_date" value="<?php echo htmlspecialchars($startDate); ?>">

            <label for="end_date">To Date:</label>
            <input type="date" id="end_date" name="end_date" value="<?php echo htmlspecialchars($endDate); ?>">

            <label for="aux_name">Select AuxName:</label>
            <select id="aux_name" name="aux_name">
                <option value="">-- All --</option>
                <?php
                // Add options to the dropdown
                foreach ($auxNames as $auxName) {
                    $selected = ($auxName == $selectedAuxName) ? 'selected' : '';
                    echo "<option value='" . htmlspecialchars($auxName) . "' $selected>" . htmlspecialchars($auxName) . "</option>";
                }
                ?>
            </select>

            <label for="search">Search:</label>
            <input type="text" id="search" name="search" value="<?php echo htmlspecialchars($searchQuery); ?>" placeholder="Search here...">

            <button type="submit">Filter</button>
        </form>

        <!-- Export to CSV button -->
        <form method="POST">
            <button type="submit" name="export_csv" class="export-btn">Export to CSV</button>
        </form>

        <!-- Data Table -->
        <table>
            <thead>
                <tr>
                    <th>Email</th>
                    <th>AuxName</th>
                    <th>Duration</th>
                    <th>Starttime</th>
                    <th>Endtime</th>
                </tr>
            </thead>
            <tbody>
                <?php
                // Display data in the table
                if (!empty($data)) {
                    foreach ($data as $row) {
                        echo "<tr>
                                <td>" . htmlspecialchars($row['Email']) . "</td>
                                <td>" . htmlspecialchars($row['AuxName']) . "</td>
                                <td>" . htmlspecialchars($row['Duration']) . "</td>
                                <td>" . htmlspecialchars($row['Starttime']) . "</td>
                                <td>" . htmlspecialchars($row['Endtime']) . "</td>
                              </tr>";
                    }
                } else {
                    echo "<tr class='no-data'><td colspan='5'>No data available to display.</td></tr>";
                }
                ?>
            </tbody>
        </table>

        <!-- Pagination links -->
        <div class="pagination">
            <?php for ($i = 1; $i <= $totalPages; $i++) { ?>
                <a href="?page=<?php echo $i; ?>&start_date=<?php echo htmlspecialchars($startDate); ?>&end_date=<?php echo htmlspecialchars($endDate); ?>&aux_name=<?php echo htmlspecialchars($selectedAuxName); ?>&search=<?php echo htmlspecialchars($searchQuery); ?>" class="<?php echo ($i == $page) ? 'active' : ''; ?>"><?php echo $i; ?></a>
            <?php } ?>
        </div>
    </div>
</body>
</html>
