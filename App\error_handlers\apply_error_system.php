<?php
/**
 * Apply Enhanced Error System to Existing Files
 * تطبيق نظام معالجة الأخطاء المحسن على الملفات الموجودة
 * 
 * @version 1.0
 * <AUTHOR> CX Security Team
 */

// منع الوصول المباشر من المتصفح
if (php_sapi_name() !== 'cli' && !isset($_GET['apply_system'])) {
    die('This script should be run from command line or with ?apply_system=1 parameter');
}

require_once __DIR__ . '/error_config.php';
require_once __DIR__ . '/secure_error_handler.php';

/**
 * قائمة الملفات المراد تحديثها
 */
$files_to_update = [
    // الملفات الرئيسية
    ['path' => '../../Loginpage.php', 'type' => 'login'],
    
    // ملفات App
    ['path' => '../../App/Home.php', 'type' => 'admin'],
    ['path' => '../../App/Reports.php', 'type' => 'admin'],
    ['path' => '../../App/Settings.php', 'type' => 'admin'],
    ['path' => '../../App/Realtime.php', 'type' => 'admin'],
    
    // ملفات Memberpages
    ['path' => '../../Memberpages/MemberHome.php', 'type' => 'member'],
    ['path' => '../../Memberpages/Reportsmember.php', 'type' => 'member'],
    ['path' => '../../Memberpages/SettingsMember.php', 'type' => 'member'],
    
    // ملفات API
    ['path' => '../../Memberpages/api/user-data.php', 'type' => 'api'],
    ['path' => '../../Memberpages/api/activity-tracker.php', 'type' => 'api'],
    
    // ملفات قاعدة البيانات
    ['path' => '../../App/reports-data/Database/Database.php', 'type' => 'database'],
    ['path' => '../../App/reports-data/Database/upload_excel.php', 'type' => 'file_upload'],
];

/**
 * إحصائيات التطبيق
 */
$stats = [
    'total_files' => 0,
    'updated_files' => 0,
    'skipped_files' => 0,
    'failed_files' => 0,
    'errors' => []
];

/**
 * تطبيق النظام على ملف واحد
 */
function applyErrorSystemToSingleFile($file_path, $file_type) {
    global $stats;
    
    $full_path = __DIR__ . '/' . $file_path;
    $stats['total_files']++;
    
    echo "Processing: $file_path (type: $file_type)\n";
    
    // التحقق من وجود الملف
    if (!file_exists($full_path)) {
        echo "  ❌ File not found: $full_path\n";
        $stats['failed_files']++;
        $stats['errors'][] = "File not found: $file_path";
        return false;
    }
    
    // قراءة محتوى الملف
    $content = file_get_contents($full_path);
    if ($content === false) {
        echo "  ❌ Cannot read file: $full_path\n";
        $stats['failed_files']++;
        $stats['errors'][] = "Cannot read file: $file_path";
        return false;
    }
    
    // التحقق من وجود النظام مسبقاً
    if (strpos($content, 'init_error_system.php') !== false) {
        echo "  ⏭️  Error system already applied\n";
        $stats['skipped_files']++;
        return true;
    }
    
    // إنشاء كود التهيئة حسب نوع الملف
    $init_code = generateInitCode($file_type);
    
    // البحث عن أول <?php وإدراج الكود بعده
    if (preg_match('/^<\?php\s*/', $content)) {
        $content = preg_replace('/^(<\?php\s*)/', '$1' . "\n" . $init_code . "\n", $content, 1);
    } else {
        // إذا لم يبدأ بـ <?php، أضف الكود في البداية
        $content = "<?php\n" . $init_code . "\n?>\n" . $content;
    }
    
    // إنشاء نسخة احتياطية
    $backup_path = $full_path . '.backup.' . date('Y-m-d-H-i-s');
    if (!copy($full_path, $backup_path)) {
        echo "  ⚠️  Warning: Cannot create backup\n";
    } else {
        echo "  💾 Backup created: " . basename($backup_path) . "\n";
    }
    
    // كتابة الملف المحدث
    if (file_put_contents($full_path, $content) !== false) {
        echo "  ✅ Successfully updated\n";
        $stats['updated_files']++;
        
        // تسجيل التحديث
        secureErrorLog("Error system applied to file", 'info', [
            'file_path' => $file_path,
            'file_type' => $file_type,
            'backup_created' => basename($backup_path)
        ]);
        
        return true;
    } else {
        echo "  ❌ Failed to write updated file\n";
        $stats['failed_files']++;
        $stats['errors'][] = "Failed to write file: $file_path";
        return false;
    }
}

/**
 * توليد كود التهيئة حسب نوع الملف
 */
function generateInitCode($file_type) {
    $base_path = "require_once __DIR__ . '/App/error_handlers/init_error_system.php';";
    
    switch ($file_type) {
        case 'login':
            return "// تهيئة نظام معالجة الأخطاء - صفحة تسجيل الدخول\n" .
                   $base_path . "\n" .
                   "initializeLoginPageErrorHandling();";
                   
        case 'admin':
            return "// تهيئة نظام معالجة الأخطاء - صفحة إدارية\n" .
                   $base_path . "\n" .
                   "initializeAdminPageErrorHandling();";
                   
        case 'member':
            return "// تهيئة نظام معالجة الأخطاء - صفحة أعضاء\n" .
                   $base_path . "\n" .
                   "initializeMemberPageErrorHandling();";
                   
        case 'api':
            return "// تهيئة نظام معالجة الأخطاء - API\n" .
                   $base_path . "\n" .
                   "initializeApiErrorHandling();";
                   
        case 'database':
            return "// تهيئة نظام معالجة الأخطاء - قاعدة البيانات\n" .
                   $base_path . "\n" .
                   "initializeDatabaseErrorHandling();";
                   
        case 'file_upload':
            return "// تهيئة نظام معالجة الأخطاء - رفع الملفات\n" .
                   $base_path . "\n" .
                   "initializeFileUploadErrorHandling();";
                   
        default:
            return "// تهيئة نظام معالجة الأخطاء\n" .
                   $base_path . "\n" .
                   "initializeErrorSystemForFile('$file_type');";
    }
}

/**
 * تحديث ملف .htaccess لإعادة التوجيه للمعالج الجديد
 */
function updateHtaccessForNewErrorHandler() {
    $htaccess_path = __DIR__ . '/../../.htaccess';
    
    if (!file_exists($htaccess_path)) {
        echo "⚠️  .htaccess file not found\n";
        return false;
    }
    
    $content = file_get_contents($htaccess_path);
    
    // البحث عن قواعد معالجة الأخطاء الموجودة
    $error_rules = [
        'ErrorDocument 400 /App/error_handlers/enhanced_error_handler.php',
        'ErrorDocument 401 /App/error_handlers/enhanced_error_handler.php',
        'ErrorDocument 403 /App/error_handlers/enhanced_error_handler.php',
        'ErrorDocument 404 /App/error_handlers/enhanced_error_handler.php',
        'ErrorDocument 500 /App/error_handlers/enhanced_error_handler.php'
    ];
    
    // إضافة القواعد الجديدة إذا لم تكن موجودة
    $updated = false;
    foreach ($error_rules as $rule) {
        if (strpos($content, $rule) === false) {
            $content .= "\n" . $rule;
            $updated = true;
        }
    }
    
    if ($updated) {
        // إنشاء نسخة احتياطية
        copy($htaccess_path, $htaccess_path . '.backup.' . date('Y-m-d-H-i-s'));
        
        if (file_put_contents($htaccess_path, $content)) {
            echo "✅ .htaccess updated with new error handlers\n";
            return true;
        } else {
            echo "❌ Failed to update .htaccess\n";
            return false;
        }
    } else {
        echo "⏭️  .htaccess already contains error handler rules\n";
        return true;
    }
}

/**
 * عرض الإحصائيات النهائية
 */
function displayStats() {
    global $stats;
    
    echo "\n" . str_repeat("=", 50) . "\n";
    echo "📊 FINAL STATISTICS\n";
    echo str_repeat("=", 50) . "\n";
    echo "Total files processed: " . $stats['total_files'] . "\n";
    echo "Successfully updated: " . $stats['updated_files'] . "\n";
    echo "Skipped (already updated): " . $stats['skipped_files'] . "\n";
    echo "Failed: " . $stats['failed_files'] . "\n";
    
    if (!empty($stats['errors'])) {
        echo "\n❌ ERRORS:\n";
        foreach ($stats['errors'] as $error) {
            echo "  - $error\n";
        }
    }
    
    $success_rate = $stats['total_files'] > 0 ? 
        round(($stats['updated_files'] + $stats['skipped_files']) / $stats['total_files'] * 100, 2) : 0;
    
    echo "\n🎯 Success Rate: $success_rate%\n";
    
    if ($success_rate >= 90) {
        echo "🎉 Excellent! Error system successfully applied.\n";
    } elseif ($success_rate >= 70) {
        echo "✅ Good! Most files updated successfully.\n";
    } else {
        echo "⚠️  Warning! Many files failed to update. Please check errors.\n";
    }
}

// تشغيل السكريبت
echo "🚀 Starting Enhanced Error System Application\n";
echo str_repeat("=", 50) . "\n";

// تطبيق النظام على كل ملف
foreach ($files_to_update as $file_info) {
    applyErrorSystemToSingleFile($file_info['path'], $file_info['type']);
    echo "\n";
}

// تحديث .htaccess
echo "Updating .htaccess file...\n";
updateHtaccessForNewErrorHandler();

// عرض الإحصائيات
displayStats();

// تسجيل اكتمال العملية
secureErrorLog("Enhanced error system application completed", 'info', [
    'total_files' => $stats['total_files'],
    'updated_files' => $stats['updated_files'],
    'success_rate' => round(($stats['updated_files'] + $stats['skipped_files']) / $stats['total_files'] * 100, 2)
]);

echo "\n🛡️ Enhanced Error System Application Completed!\n";
echo "Check the logs for detailed information.\n";

?>
