@import url('root-variables.css');

body.add-user-page {
            background-color: var(--light-color);
            font-family: 'Segoe UI', Arial, sans-serif;
        }
        
        .add-user-container {
            max-width: 1000px;
            margin: 40px auto;
        }
        
        .add-user-card {
            background-color: var(--white);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 30px;
        }
        
        .add-user-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .add-user-title {
            color: var(--primary-dark);
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .add-user-login-info {
            background-color: var(--primary-light);
            padding: 10px 15px;
            border-radius: var(--border-radius);
            display: inline-block;
            margin-bottom: 20px;
            font-weight: 500;
            color: var(--dark-color);
        }
        
        .add-user-form-label {
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 8px;
        }
        
        .add-user-form-control {
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 10px 15px;
            margin-bottom: 15px;
            transition: var(--transition);
        }
        
        .add-user-form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(199, 154, 199, 0.25);
        }
        
        .add-user-btn {
            background-color: var(--primary-color);
            border: none;
            padding: 12px 25px;
            font-weight: 600;
            transition: var(--transition);
            width: 100%;
            margin-top: 20px;
        }
        
        .add-user-btn:hover {
            background-color: var(--primary-dark);
        }
        
        .add-user-section-title {
            color: var(--primary-dark);
            border-bottom: 2px solid var(--primary-light);
            padding-bottom: 8px;
            margin-bottom: 20px;
            font-size: 1.2rem;
            grid-column: 1 / -1;
        }
        
        .add-user-form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
        }
        
        @media (max-width: 768px) {
            .add-user-card {
                padding: 20px;
            }
            
            .add-user-form-grid {
                grid-template-columns: 1fr;
            }
        }
          body.user-edit-page {
            background-color: var(--light-color);
            font-family: 'Segoe UI', Arial, sans-serif;
            padding-top: 20px;
        }
        
        .user-edit-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .user-edit-header {
            background-color: var(--primary-color);
            color: white;
            border-radius: var(--border-radius);
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: var(--shadow);
        }
        
        .user-edit-card {
            background-color: var(--white);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 25px;
            margin-bottom: 30px;
        }
        
        .user-email-display {
            background-color: var(--primary-light);
            padding: 10px 15px;
            border-radius: var(--border-radius);
            display: inline-block;
            margin-bottom: 20px;
            font-weight: bold;
            color: var(--dark-color);
        }
        
        .user-edit-form-label {
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 8px;
        }
        
        .user-edit-form-control {
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 10px 15px;
            margin-bottom: 15px;
            transition: var(--transition);
        }
        
        .user-edit-form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(199, 154, 199, 0.25);
        }
        
        .user-edit-btn {
            background-color: var(--primary-color);
            border: none;
            padding: 12px 25px;
            font-weight: 600;
            transition: var(--transition);
            width: 100%;
        }
        
        .user-edit-btn:hover {
            background-color: var(--primary-dark);
        }
        
        .user-edit-message {
            padding: 15px;
            border-radius: var(--border-radius);
            margin-bottom: 20px;
            text-align: center;
            font-weight: 600;
        }
        
        .user-edit-message.success {
            background-color: rgba(76, 175, 80, 0.1);
            color: var(--success-color);
            border: 1px solid var(--success-color);
        }
        
        .user-edit-message.error {
            background-color: rgba(244, 67, 54, 0.1);
            color: var(--danger-color);
            border: 1px solid var(--danger-color);
        }
        
        .user-edit-section-title {
            color: var(--primary-dark);
            border-bottom: 2px solid var(--primary-light);
            padding-bottom: 8px;
            margin-bottom: 20px;
            font-size: 1.2rem;
        }
        
        @media (max-width: 768px) {
            .user-edit-card {
                padding: 15px;
            }
        }