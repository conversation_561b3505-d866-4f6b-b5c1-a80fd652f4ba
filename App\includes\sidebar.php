<?php
// تضمين نظام الصلاحيات
include_once $_SERVER['DOCUMENT_ROOT'].'/App/includes/permissions.php';

// الحصول على صلاحية المستخدم الحالي
$current_user_permission = $_SESSION['access_level'] ?? 'Viewer';
?>

<!-- Sidebar Logout Styles -->
<link rel="stylesheet" href="/App/assets/css/sidebar-logout.css">

<!-- Sidebar -->
<div class="sidebar" id="sidebar">
    <!-- Header Section - Fixed -->
    <div class="sidebar-header">
        <img src="/Logos/kalam.png" alt="Company Logo">
    </div>

    <!-- Scrollable Menu Section -->
    <div class="sidebar-menu-container">
        <div class="sidebar-menu">
            <?php if (canAccessSidebarItem('profile', $current_user_permission)): ?>
            <a href="/App/profile/UserProfile" class="<?php echo basename($_SERVER['PHP_SELF']) == 'UserProfile.php' ? 'active' : ''; ?>">
                <i class="fas fa-user-circle"></i>
                <span>My Profile</span>
            </a>
            <?php endif; ?>

            <?php if (canAccessSidebarItem('dashboard', $current_user_permission)): ?>
            <a href="/App/Home" class="<?php echo basename($_SERVER['PHP_SELF']) == 'Home.php' ? 'active' : ''; ?>">
                <i class="fas fa-home"></i>
                <span>Dashboard</span>
            </a>
            <?php endif; ?>

            <?php if (canAccessSidebarItem('reports', $current_user_permission)): ?>
            <a href="/App/Reports" class="<?php echo basename($_SERVER['PHP_SELF']) == 'Reports.php' ? 'active' : ''; ?>">
                <i class="fas fa-chart-bar"></i>
                <span>Reports</span>
            </a>
            <?php endif; ?>



            <?php if (canAccessSidebarItem('users', $current_user_permission)): ?>
            <a href="/App/users/users" class="<?php echo basename($_SERVER['PHP_SELF']) == 'users.php' ? 'active' : ''; ?>">
                <i class="fas fa-users"></i>
                <span>Users</span>
            </a>
            <?php endif; ?>

            <?php if (canAccessSidebarItem('users_archive', $current_user_permission)): ?>
            <a href="/App/users/ArchiveUsers" class="<?php echo basename($_SERVER['PHP_SELF']) == 'ArchiveUsers.php' ? 'active' : ''; ?>">
                <i class="fas fa-archive"></i>
                <span>User Archive</span>
            </a>
            <?php endif; ?>

            <?php if (canAccessSidebarItem('leaves', $current_user_permission)): ?>
            <a href="/App/leaves/MyLeaves" class="<?php echo basename($_SERVER['PHP_SELF']) == 'MyLeaves.php' ? 'active' : ''; ?>">
                <i class="fas fa-calendar-alt"></i>
                <span>My Leaves</span>
            </a>
            <?php endif; ?>

            <?php if (canAccessSidebarItem('realtime', $current_user_permission)): ?>
            <a href="/App/Realtime" class="<?php echo basename($_SERVER['PHP_SELF']) == 'Realtime.php' ? 'active' : ''; ?>">
                <i class="fas fa-clock"></i>
                <span>Real-Time</span>
            </a>
            <?php endif; ?>

            <?php if (canAccessSidebarItem('transactions', $current_user_permission)): ?>
            <a href="/App/Auxtransactions/AuxTransaction" class="<?php echo basename($_SERVER['PHP_SELF']) == 'AuxTransaction.php' ? 'active' : ''; ?>">
                <i class="fas fa-exchange-alt"></i>
                <span>Transactions</span>
            </a>
            <?php endif; ?>

            <?php if (canAccessSidebarItem('settings', $current_user_permission)): ?>
            <a href="/App/Settings" class="<?php echo basename($_SERVER['PHP_SELF']) == 'Settings.php' ? 'active' : ''; ?>">
                <i class="fas fa-cogs"></i>
                <span>Settings</span>
            </a>
            <?php endif; ?>
        </div>
    </div>

    <!-- Footer Section - Fixed -->
    <div class="sidebar-footer">
        <button class="logout-btn" onclick="showUnifiedLogoutModal()">
            <i class="fas fa-sign-out-alt"></i>
            <span>Logout</span>
        </button>
        <button class="toggle-btn" onclick="toggleSidebar()">
            <i class="fas fa-bars"></i>
            <span>Collapse</span>
        </button>
    </div>
</div>

<!-- Include Unified Logout Modal -->
<?php include $_SERVER['DOCUMENT_ROOT'].'/App/includes/unified-logout-modal.php';?>

<!-- Sidebar JavaScript -->
<script>
// Sidebar toggle functionality (logout is now handled by unified-logout-modal.php)

// Sidebar toggle functionality
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.querySelector('.main-content');

    if (sidebar) {
        sidebar.classList.toggle('collapsed');
    }

    if (mainContent) {
        mainContent.classList.toggle('sidebar-collapsed');
    }

    // Save state in localStorage
    const isCollapsed = sidebar ? sidebar.classList.contains('collapsed') : false;
    localStorage.setItem('sidebarCollapsed', isCollapsed);
}

// Restore sidebar state on page load
document.addEventListener('DOMContentLoaded', function() {
    const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.querySelector('.main-content');

    if (isCollapsed && sidebar) {
        sidebar.classList.add('collapsed');
        if (mainContent) {
            mainContent.classList.add('sidebar-collapsed');
        }
    }
});
</script>
