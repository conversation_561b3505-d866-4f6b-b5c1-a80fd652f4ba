<?php
/**
 * Test file for addindatabse.php
 * This file helps diagnose issues with the add database form
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
session_start();

// Include database connection
include $_SERVER['DOCUMENT_ROOT'].'/config/Databasehost.php';
include '../system/database_config.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>Add Database Test</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head><body class='container py-4'>";

echo "<h1>Add Database Form - Diagnostic Test</h1>";

// Test 1: Database Connection
echo "<div class='card mb-4'>";
echo "<div class='card-header'><h3>1. Database Connection Test</h3></div>";
echo "<div class='card-body'>";

if (isset($conn) && $conn instanceof mysqli && !$conn->connect_error) {
    echo "<div class='alert alert-success'><i class='fas fa-check'></i> Database connection successful</div>";
    
    // Test table structure
    $result = $conn->query("DESCRIBE databasehc");
    if ($result) {
        $columns = [];
        while ($row = $result->fetch_assoc()) {
            $columns[] = $row['Field'];
        }
        echo "<p><strong>Available columns:</strong> " . count($columns) . "</p>";
        echo "<details><summary>Show columns</summary>";
        echo "<ul>";
        foreach ($columns as $col) {
            echo "<li>$col</li>";
        }
        echo "</ul></details>";
    }
} else {
    echo "<div class='alert alert-danger'><i class='fas fa-times'></i> Database connection failed</div>";
}

echo "</div></div>";

// Test 2: Configuration Files
echo "<div class='card mb-4'>";
echo "<div class='card-header'><h3>2. Configuration Files Test</h3></div>";
echo "<div class='card-body'>";

$configFile = __DIR__ . '/../settings/field_config.json';
$defaultConfigFile = __DIR__ . '/../settings/default_field_config.json';

echo "<h5>Field Configuration Files:</h5>";
echo "<ul>";

if (file_exists($configFile)) {
    echo "<li class='text-success'><i class='fas fa-check'></i> field_config.json exists</li>";
    $config = json_decode(file_get_contents($configFile), true);
    if ($config) {
        echo "<li class='text-success'><i class='fas fa-check'></i> field_config.json is valid JSON</li>";
        echo "<li>Sections found: " . count($config) . "</li>";
        
        $totalFields = 0;
        foreach ($config as $section) {
            if (isset($section['fields'])) {
                $totalFields += count($section['fields']);
            }
        }
        echo "<li>Total fields: $totalFields</li>";
    } else {
        echo "<li class='text-danger'><i class='fas fa-times'></i> field_config.json is invalid JSON</li>";
    }
} else {
    echo "<li class='text-warning'><i class='fas fa-exclamation'></i> field_config.json not found</li>";
}

if (file_exists($defaultConfigFile)) {
    echo "<li class='text-success'><i class='fas fa-check'></i> default_field_config.json exists</li>";
} else {
    echo "<li class='text-warning'><i class='fas fa-exclamation'></i> default_field_config.json not found</li>";
}

echo "</ul>";
echo "</div></div>";

// Test 3: Session Check
echo "<div class='card mb-4'>";
echo "<div class='card-header'><h3>3. Session Test</h3></div>";
echo "<div class='card-body'>";

if (isset($_SESSION['username']) && isset($_SESSION['random_code'])) {
    echo "<div class='alert alert-success'><i class='fas fa-check'></i> Session is valid</div>";
    echo "<p><strong>Username:</strong> " . htmlspecialchars($_SESSION['username']) . "</p>";
    echo "<p><strong>Access Level:</strong> " . htmlspecialchars($_SESSION['access_level'] ?? 'Not set') . "</p>";
} else {
    echo "<div class='alert alert-warning'><i class='fas fa-exclamation'></i> Session not found - you may need to login</div>";
    echo "<p>For testing purposes, setting temporary session...</p>";
    $_SESSION['username'] = '<EMAIL>';
    $_SESSION['random_code'] = 'test123';
    $_SESSION['access_level'] = 'admin';
}

echo "</div></div>";

// Test 4: Include the actual form
echo "<div class='card mb-4'>";
echo "<div class='card-header'><h3>4. Form Loading Test</h3></div>";
echo "<div class='card-body'>";

try {
    echo "<p>Attempting to load the add database form...</p>";
    echo "<a href='addindatabse.php' class='btn btn-primary' target='_blank'>Open Add Database Form</a>";
    echo "<p class='mt-2'><small>The form should open in a new tab. Check for any errors or missing elements.</small></p>";
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>Error loading form: " . $e->getMessage() . "</div>";
}

echo "</div></div>";

// Test 5: CSS and Assets
echo "<div class='card mb-4'>";
echo "<div class='card-header'><h3>5. Assets Test</h3></div>";
echo "<div class='card-body'>";

$cssFile = __DIR__ . '/../../../assets/css/Database.css';
$rootVariablesFile = __DIR__ . '/../../../assets/css/root-variables.css';
echo "<h5>CSS Files:</h5>";
echo "<ul>";

if (file_exists($cssFile)) {
    echo "<li class='text-success'><i class='fas fa-check'></i> Database.css exists</li>";
} else {
    echo "<li class='text-danger'><i class='fas fa-times'></i> Database.css not found at: $cssFile</li>";
}

if (file_exists($rootVariablesFile)) {
    echo "<li class='text-success'><i class='fas fa-check'></i> root-variables.css exists</li>";
} else {
    echo "<li class='text-danger'><i class='fas fa-times'></i> root-variables.css not found at: $rootVariablesFile</li>";
}

echo "</ul>";
echo "</div></div>";

echo "<div class='alert alert-info'>";
echo "<h5>Next Steps:</h5>";
echo "<ol>";
echo "<li>If all tests pass, try submitting the form with test data</li>";
echo "<li>Check the browser console for any JavaScript errors</li>";
echo "<li>Check the server error logs for any PHP errors</li>";
echo "<li>Verify that data is being inserted into the database</li>";
echo "</ol>";
echo "</div>";

echo "</body></html>";
?>
