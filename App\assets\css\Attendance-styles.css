@import url('root-variables.css');

/* ملف CSS موحد لصفحات الحضور والانصراف */

/* تنسيقات عامة */
body {
    font-family: 'Poppins', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--primary-lighter);
    color: var(--text-dark);
    line-height: 1.5;
    margin: 0;
    padding: 0;
}

/* تنسيقات النافبار */
.navbar {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    box-shadow: var(--shadow);
}

.navbar-brand {
    font-weight: 700;
    color: var(--white);
    display: flex;
    align-items: center;
}

.navbar-brand img {
    height: 30px;
    margin-right: 10px;
}

.nav-link {
    color: var(--white) !important;
    font-weight: 500;
    padding: 0.5rem 1rem;
    margin: 0 0.2rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.nav-link:hover, .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* تنسيقات الحاويات */
.main-container {
    background-color: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-top: 2rem;
    padding: 2rem;
    min-height: 500px;
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.main-container:hover {
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

/* تنسيقات البطاقات */
.card {
    border: none;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
    margin-bottom: 1.5rem;
    height: 100%;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    font-weight: 600;
    border-bottom: none;
    border-radius: 10px 10px 0 0 !important;
}

.summary-card {
    background: linear-gradient(135deg, var(--primary-light), white);
    border-left: 5px solid var(--primary-color);
}

.summary-title {
    color: var(--primary-dark);
    font-weight: bold;
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.summary-value {
    font-size: 1.4rem;
    font-weight: bold;
    color: var(--primary-dark);
    line-height: 1.2;
}

/* تنسيقات الجداول */
.table-responsive {
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table {
    margin-bottom: 0;
    font-size: 0.9rem;
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
}

.table th {
    background-color: var(--primary-color) !important;
    color: white;
    font-weight: 500;
    padding: 0.75rem;
    border-bottom: none;
    position: sticky;
    top: 0;
    z-index: 10;
    text-align: center;
    vertical-align: middle;
    white-space: nowrap;
}

.table td {
    padding: 0.5rem;
    vertical-align: middle;
    border-top: 1px solid var(--border-color);
}

.table-hover tbody tr:hover td {
    background-color: rgba(199, 154, 199, 0.05);
}

/* تنسيقات الأزرار */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}

.btn-success {
    background-color: var(--success-color);
    border: none;
    border-radius: var(--border-radius);
    padding: 0.375rem 0.75rem;
    font-weight: 500;
}

/* تنسيقات الفورم */
.form-control, .form-select {
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    padding: 0.375rem 0.75rem;
    font-size: 0.9rem;
}

.input-group-text {
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
    font-size: 0.9rem;
    padding: 0.375rem 0.75rem;
}

/* تنسيقات الحالات */
.status-present {
    color: var(--success-color);
    font-weight: 500;
}

.status-absent {
    color: var(--danger-color);
    font-weight: 500;
}

.status-late {
    color: var(--warning-color);
    font-weight: 500;
}

/* تنسيقات الشارات */
.badge-filter {
    background-color: var(--primary-light);
    color: var(--dark-color);
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 50px;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
    display: inline-flex;
    align-items: center;
    font-size: 0.75rem;
}

/* تنسيقات الترقيم الصفحي */
.pagination .page-item .page-link {
    color: var(--primary-color);
    border: 1px solid var(--border-color);
    margin: 0 2px;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    padding: 0.375rem 0.75rem;
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
}

/* تنسيقات التابات */
.nav-tabs {
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 1rem;
}

.nav-tabs .nav-link {
    color: var(--text-medium) !important;
    border: none;
    border-bottom: 3px solid transparent;
    border-radius: 0;
    padding: 0.5rem 1rem;
    margin-right: 0.5rem;
    font-weight: 500;
}

.nav-tabs .nav-link.active {
    color: var(--primary-color) !important;
    background-color: transparent;
    border-bottom: 3px solid var(--primary-color);
}

/* تنسيقات الشعار */
.logo {
    height: 30px;
    width: auto;
    vertical-align: middle;
    margin-right: 8px;
    transition: var(--transition);
}

/* تنسيقات الفلاتر */
.filter-card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    padding: 1rem;
    margin-bottom: 1rem;
}

/* تنسيقات الرسائل الفارغة */
.empty-state {
    padding: 2rem;
    text-align: center;
    color: var(--text-light);
}

/* تنسيقات السكرولبار */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--light-color);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-light);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

/* تنسيقات DataTables */
.dataTables_wrapper .dataTables_length, 
.dataTables_wrapper .dataTables_filter, 
.dataTables_wrapper .dataTables_info, 
.dataTables_wrapper .dataTables_processing, 
.dataTables_wrapper .dataTables_paginate {
    color: var(--text-medium);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    color: var(--primary-color) !important;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    margin: 0 2px;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background: var(--primary-color) !important;
    color: white !important;
    border-color: var(--primary-color);
}

/* تنسيقات أزرار DataTables */
.buttons-pdf, .buttons-excel, .buttons-csv, .buttons-print {
    background-color: var(--primary-color) !important;
    color: white !important;
    border: none !important;
    border-radius: var(--border-radius) !important;
    margin-right: 5px !important;
}

.buttons-pdf:before {
    content: "\f1c1";
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
    margin-right: 5px;
}

.buttons-excel:before {
    content: "\f1c3";
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
    margin-right: 5px;
}

.buttons-csv:before {
    content: "\f6dd";
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
    margin-right: 5px;
}

.buttons-print:before {
    content: "\f02f";
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
    margin-right: 5px;
}

/* تنسيقات خاصة بصفحة attendancereport.php */
.attendancereport-status-badge {
    padding: 5px 10px;
    border-radius: 20px;
    font-weight: bold;
    font-size: 0.8rem;
    text-transform: capitalize;
    display: inline-block;
    min-width: 80px;
    text-align: center;
}

.attendancereport-status-absent {
    background-color: #ffebee;
    color: var(--danger-color);
    animation: attendancereport-pulse 2s infinite;
}

.attendancereport-status-present {
    background-color: #e8f5e9;
    color: var(--success-color);
}

.attendancereport-status-off {
    background-color: #e0e0e0;
    color: #616161;
}

.attendancereport-status-vacation {
    background-color: #e3f2fd;
    color: #1976d2;
}

.attendancereport-status-attended {
    background-color: #e8f5e9;
    color: var(--success-color);
}

.attendancereport-status-shift-not-started {
    background-color: #e3f2fd;
    color: var(--info-color);
}

@keyframes attendancereport-pulse {
    0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.4); }
    70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
    100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
}

.attendancereport-search-box {
    position: relative;
    margin-bottom: 15px;
}

.attendancereport-search-box input {
    padding-left: 40px;
    border-radius: 20px;
    border: 2px solid var(--primary-light);
}

.attendancereport-search-box i {
    position: absolute;
    top: 10px;
    left: 15px;
    color: var(--primary-color);
}

.attendancereport-btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-dark);
}

.attendancereport-btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}

.attendancereport-summary-card {
    background: linear-gradient(135deg, var(--primary-light), white);
    border-left: 5px solid var(--primary-color);
    height: 80px; /* تصغير ارتفاع الكروت */
}

.attendancereport-summary-card .card-body {
    padding: 0.75rem; /* تقليل padding */
}

.attendancereport-summary-title {
    color: var(--primary-dark);
    font-weight: bold;
    font-size: 0.75rem; /* تصغير حجم الخط */
    margin-bottom: 3px; /* تقليل المسافة */
}

.attendancereport-summary-value {
    font-size: 1.2rem; /* تصغير حجم الرقم */
    font-weight: bold;
    color: var(--primary-dark);
    line-height: 1.1;
}

.attendancereport-leader-summary-container {
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    height: 100%;
    min-height: 200px; /* ضمان ارتفاع أدنى */
}

.attendancereport-leader-summary-card {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.attendancereport-leader-summary-card .card-header {
    padding: 0.5rem 0.75rem; /* تقليل padding للعنوان */
    font-size: 0.85rem; /* تصغير خط العنوان */
    font-weight: 600;
}

.attendancereport-leader-summary-card .card-body {
    flex: 1;
    overflow: hidden;
}

.attendancereport-filters-section {
    margin-bottom: 15px;
}

.attendancereport-filters-card {
    border-radius: 12px;
    overflow: hidden;
}

.attendancereport-filters-card .card-header {
    background: linear-gradient(135deg, #9c6fa3, #7a4d82);
    color: white;
    font-weight: 600;
    padding: 12px 20px;
    border-bottom: none;
}

.attendancereport-filters-card .card-body {
    padding: 15px;
    background-color: #faf5fb;
}

.attendancereport-filter-item {
    height: 100%;
    display: flex;
    flex-direction: column;
    margin-bottom: 10px;
}

.attendancereport-filter-item label {
    font-weight: 600;
    color: #5a3a61;
    font-size: 0.85rem;
    margin-bottom: 6px;
    display: flex;
    align-items: center;
}

/* تنسيقات خاصة بصفحة AttendanceMTD.php */
.att-body {
    font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
    background-color: var(--secondary-color);
    color: var(--text-dark);
    line-height: 1.5;
    font-size: 0.875rem;
}

.att-container-fluid {
    max-width: 1800px;
    padding: 1rem;
}

.att-header-card {
    background-color: var(--primary-color);
    color: var(--white);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 1rem;
}

.att-header-card .d-flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* العنوان على اليسار */
.att-header-card .d-flex > div:first-child {
    flex: 1;
}

/* مجموعة التاريخ واللوجو على اليمين */
.att-header-card .text-end {
    display: flex;
    align-items: center;
    margin-right: 10px;
    padding-left: 20px;
}

.att-logo-container {
    display: flex;
    align-items: center;
    margin-left: 50px; /* زيادة المسافة بين اللوجو والتاريخ */
}

.att-logo-container img {
    height: 40px;
    width: auto;
}

.att-filter-card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    padding: 1rem;
    margin-bottom: 1rem;
}

.att-table-container {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.att-table {
    margin-bottom: 0;
    font-size: 0.8125rem;
}

.att-table th {
    background-color: var(--primary-color) !important;
    color: var(--white);
    font-weight: 500;
    padding: 0.75rem;
    border-bottom: none;
    position: sticky;
    top: 0;
    z-index: 10;
}

.att-table td {
    padding: 0.5rem;
    vertical-align: middle;
    border-top: 1px solid var(--border-color);
}

.att-table tr:last-child td {
    border-bottom: none;
}

.att-table-hover tbody tr:hover td {
    background-color: rgba(199, 154, 199, 0.05);
}

.att-btn-primary {
    background-color: var(--primary-color);
    border: none;
    border-radius: var(--border-radius);
    padding: 0.375rem 0.75rem;
    font-weight: 500;
}

.att-btn-success {
    background-color: var(--success-color);
    border: none;
    border-radius: var(--border-radius);
    padding: 0.375rem 0.75rem;
    font-weight: 500;
    color: white;
}

.att-btn-success:hover {
    background-color: #3d8b40;
}

.att-logo-container img {
    height: 40px;
    width: auto;
}

.att-badge {
    padding: 0.35em 0.65em;
    font-size: 0.75em;
    font-weight: 700;
    border-radius: 50px;
    display: inline-block;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
}

.att-badge-success {
    background-color: var(--success-color);
    color: white;
}

.att-badge-danger {
    background-color: var(--danger-color);
    color: white;
}

.att-badge-warning {
    background-color: var(--warning-color);
    color: white;
}

.att-badge-info {
    background-color: var(--info-color);
    color: white;
}

.att-badge-secondary {
    background-color: var(--gray-color);
    color: white;
}

/* تنسيقات خاصة بصفحة attendanceanalysis.php */
.attendanceanalysis-container {
    padding: 1.5rem;
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 2rem;
}

.attendanceanalysis-summary-card {
    background: linear-gradient(135deg, rgba(218, 179, 218, 0.2), rgba(255, 255, 255, 0.9)); /* لون خفيف جدًا */
    border-left: 3px solid var(--primary-color);
    transition: transform 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.attendanceanalysis-summary-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.attendanceanalysis-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.attendanceanalysis-table th {
    background-color: var(--primary-color);
    color: white;
    font-weight: 500;
    padding: 0.75rem;
    text-align: left;
    border-bottom: none;
}

.attendanceanalysis-table td {
    padding: 0.75rem;
    border-top: 1px solid var(--border-color);
}

.attendanceanalysis-table tr:hover td {
    background-color: rgba(199, 154, 199, 0.05);
}

.attendanceanalysis-filter-container {
    background-color: var(--light-color);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.attendanceanalysis-filter-label {
    font-weight: 600;
    color: var(--primary-dark);
    margin-bottom: 0.5rem;
    display: block;
}

.attendanceanalysis-filter-select {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: white;
}

.attendanceanalysis-chart-container {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 1rem;
    margin-bottom: 1.5rem;
    height: 300px;
}

.attendanceanalysis-tab-content {
    padding: 1.5rem;
    background-color: white;
    border: 1px solid var(--border-color);
    border-top: none;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
}

.attendanceanalysis-nav-tabs {
    border-bottom: none;
}

.attendanceanalysis-nav-tabs .nav-link {
    color: var(--text-medium);
    background-color: var(--light-color);
    border: 1px solid var(--border-color);
    border-bottom: none;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    margin-right: 0.25rem;
    font-weight: 500;
}

.attendanceanalysis-nav-tabs .nav-link.active {
    color: var(--primary-color);
    background-color: white;
    border-bottom-color: white;
}

/* تنسيقات مشتركة للطباعة */
@media print {
    body {
        background-color: white !important;
        font-size: 12pt;
    }
    
    .no-print {
        display: none !important;
    }
    
    .container, .container-fluid {
        width: 100%;
        padding: 0;
        margin: 0;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #ddd;
    }
    
    .table {
        width: 100% !important;
        border-collapse: collapse !important;
    }
    
    .table th {
        background-color: #f1f1f1 !important;
        color: black !important;
    }
    
    .table td, .table th {
        border: 1px solid #ddd !important;
    }
    
    .page-break {
        page-break-before: always;
    }
}

/* تنسيقات للشاشات الصغيرة */
@media (max-width: 768px) {
    .container, .container-fluid {
        padding: 0.5rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .table-responsive {
        overflow-x: auto;
    }
    
    .nav-tabs .nav-link {
        padding: 0.5rem;
        font-size: 0.8rem;
    }
    
    .summary-value {
        font-size: 1.2rem;
    }
    
    .attendanceanalysis-container,
    .att-container-fluid {
        padding: 0.75rem;
    }
    
    .attendancereport-summary-card,
    .attendanceanalysis-summary-card {
        margin-bottom: 1rem;
    }
}

/* تنسيقات للتحميل والانتظار */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid var(--primary-light);
    border-top: 5px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تنسيقات للتنبيهات */
.alert {
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 1rem;
    border: none;
}

.alert-success {
    background-color: #e8f5e9;
    color: var(--success-color);
    border-left: 4px solid var(--success-color);
}

.alert-danger {
    background-color: #ffebee;
    color: var(--danger-color);
    border-left: 4px solid var(--danger-color);
}

.alert-warning {
    background-color: #fff8e1;
    color: var(--warning-color);
    border-left: 4px solid var(--warning-color);
}

.alert-info {
    background-color: #e3f2fd;
    color: var(--info-color);
    border-left: 4px solid var(--info-color);
}

/* تنسيقات للتوولتيب */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 120px;
    background-color: var(--dark-color);
    color: var(--white);
    text-align: center;
    border-radius: 6px;
    padding: 5px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -60px;
    opacity: 0;
    transition: opacity 0.3s;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* تنسيقات للتأثيرات */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-in {
    animation: slideIn 0.5s ease-in;
}

@keyframes slideIn {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* تنسيقات إضافية للأيقونات */
.icon-circle {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: var(--primary-light);
    color: var(--primary-dark);
    margin-right: 0.5rem;
}

.icon-circle i {
    font-size: 1rem;
}

/* تنسيقات للإحصائيات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat-card {
    background: linear-gradient(135deg, var(--primary-light), white);
    border-radius: var(--border-radius);
    padding: 1rem;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-title {
    font-size: 0.9rem;
    color: var(--text-medium);
    margin-bottom: 0.5rem;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--primary-dark);
}

.stat-icon {
    font-size: 2rem;
    color: var(--primary-color);
    opacity: 0.2;
    position: absolute;
    top: 1rem;
    right: 1rem;
}

/* تعديل تنسيقات الكروت في صفحة attendancereport */
.attendancereport-stats-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr) 2fr; /* 5 كروت صغيرة + كارت Leaders Summary أكبر */
    gap: 12px;
    margin-bottom: 20px;
}

.attendancereport-stat-card {
    display: flex;
    flex-direction: column;
    height: 100%;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: var(--transition);
    min-height: 100px; /* تحديد ارتفاع أدنى للكروت */
}

.attendancereport-stat-card:hover {
    transform: translateY(-3px);
}

.card-body-flex {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 10px; /* تقليل الهوامش الداخلية */
}

/* تصغير حجم النص في الكروت */
.attendancereport-summary-title {
    color: var(--primary-dark);
    font-weight: bold;
    font-size: 0.85rem; /* تصغير حجم العنوان */
    margin-bottom: 3px;
}

.attendancereport-summary-value {
    font-size: 1.2rem; /* تصغير حجم القيمة */
    font-weight: bold;
    color: var(--primary-dark);
    line-height: 1.2;
}

/* تنسيق خاص لكارت ملخص القادة */
.attendancereport-leader-summary-container {
    grid-column: 6; /* وضعه في العمود السادس */
    grid-row: 1; /* وضعه في الصف الأول */
}

.attendancereport-leader-summary-body {
    max-height: 200px; /* زيادة ارتفاع جدول القادة */
    overflow-y: auto;
}

/* تصغير خط جدول القادة */
.attendancereport-leader-summary-body table {
    font-size: 0.75rem; /* تصغير حجم الخط */
}

.attendancereport-leader-summary-body th,
.attendancereport-leader-summary-body td {
    padding: 0.25rem 0.5rem; /* تقليل padding للخلايا */
    vertical-align: middle;
}

.attendancereport-leader-summary-body th {
    font-size: 0.7rem; /* تصغير خط العناوين أكثر */
    font-weight: 600;
}

/* تعديلات للشاشات المتوسطة */
@media (max-width: 1200px) {
    .attendancereport-stats-grid {
        grid-template-columns: repeat(3, 1fr); /* 3 كروت في الصف للشاشات المتوسطة */
    }

    .attendancereport-leader-summary-container {
        grid-column: span 3; /* يأخذ الصف كاملاً في الشاشات المتوسطة */
        grid-row: auto; /* إعادة ترتيب الصفوف تلقائيًا */
        min-height: 180px; /* تقليل الارتفاع للشاشات المتوسطة */
    }

    .attendancereport-summary-card {
        height: 70px; /* تصغير أكثر للشاشات المتوسطة */
    }
}

/* تعديلات للشاشات الصغيرة */
@media (max-width: 768px) {
    .attendancereport-stats-grid {
        grid-template-columns: repeat(2, 1fr); /* 2 كروت في الصف للشاشات الصغيرة */
    }

    .attendancereport-leader-summary-container {
        grid-column: span 2; /* يأخذ الصف كاملاً في الشاشات الصغيرة */
        min-height: 160px; /* تقليل الارتفاع للشاشات الصغيرة */
    }

    .attendancereport-summary-card {
        height: 65px; /* تصغير أكثر للشاشات الصغيرة */
    }

    .attendancereport-summary-title {
        font-size: 0.7rem; /* تصغير الخط أكثر */
    }

    .attendancereport-summary-value {
        font-size: 1.1rem; /* تصغير الرقم أكثر */
    }
}

/* تعديلات للشاشات الصغيرة جدًا */
@media (max-width: 576px) {
    .attendancereport-stats-grid {
        grid-template-columns: 1fr; /* كارت واحد في الصف للشاشات الصغيرة جدًا */
    }
    
    .attendancereport-leader-summary-container {
        grid-column: span 1;
    }
}

/* تعديل تنسيقات أزرار DataTables */
.btn-outline-secondary {
    color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    background-color: transparent !important;
}

.btn-outline-secondary:hover {
    color: white !important;
    background-color: var(--primary-color) !important;
    border-color: var(--primary-dark) !important;
}

/* تنسيق خاص لزر Copy */
.buttons-copy {
    color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    background-color: transparent !important;
}

.buttons-copy:hover {
    color: white !important;
    background-color: var(--primary-color) !important;
    border-color: var(--primary-dark) !important;
}

/* تنسيق خاص لزر Column */
.buttons-colvis {
    color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    background-color: transparent !important;
}

.buttons-colvis:hover {
    color: white !important;
    background-color: var(--primary-color) !important;
    border-color: var(--primary-dark) !important;
}

/* تنسيق عام لأزرار DataTables */
.dt-button {
    border-radius: var(--border-radius) !important;
    font-size: 0.85rem !important;
    padding: 0.375rem 0.75rem !important;
    margin-right: 0.25rem !important;
    transition: all 0.3s ease !important;
}

.dt-button:focus, .dt-button:active {
    box-shadow: 0 0 0 0.2rem rgba(199, 154, 199, 0.25) !important;
}

/* تنسيق قائمة الأعمدة */
.dt-button-collection {
    border-radius: var(--border-radius) !important;
    padding: 0.5rem !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1) !important;
}

.dt-button-collection .dt-button {
    display: block;
    width: 100%;
    text-align: left;
    margin-bottom: 0.25rem;
}

/* تنسيقات فلاتر التاريخ */
.attendanceanalysis-filter-box {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.attendanceanalysis-filter-box .form-label {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.attendanceanalysis-filter-box .form-control,
.attendanceanalysis-filter-box .form-select {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 0.5rem 0.75rem;
    transition: var(--transition);
}

.attendanceanalysis-filter-box .form-control:focus,
.attendanceanalysis-filter-box .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
}

/* أزرار الفترات السريعة */
.btn-group .btn-outline-primary {
    border-color: var(--primary-color);
    color: var(--primary-color);
    transition: var(--transition);
}

.btn-group .btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
    transform: translateY(-1px);
}

.btn-group .btn-outline-primary:active,
.btn-group .btn-outline-primary.active {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    color: var(--white);
}

/* تحسينات للتواريخ */
input[type="date"] {
    position: relative;
}

input[type="date"]::-webkit-calendar-picker-indicator {
    color: var(--primary-color);
    cursor: pointer;
}

/* تنسيقات متجاوبة للفلاتر */
@media (max-width: 768px) {
    .attendanceanalysis-filter-box {
        padding: 1rem;
    }

    .btn-group {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .btn-group .btn {
        flex: 1;
        min-width: 120px;
    }
}


