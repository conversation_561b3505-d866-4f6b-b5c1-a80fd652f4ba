<?php
/**
 * Universal Error Handler
 * معالج الأخطاء العام
 * 
 * يتعامل مع جميع أنواع أخطاء HTTP ويوجه المستخدمين للصفحات المناسبة
 */

// Start secure session if not already started
if (session_status() === PHP_SESSION_NONE) {
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_secure', 1);
    ini_set('session.use_only_cookies', 1);
    ini_set('session.cookie_samesite', 'Strict');
    session_start();
}

// Include database connection
include_once $_SERVER['DOCUMENT_ROOT'].'/config/Databasehost.php';

/**
 * Get error code from query parameter or server variable
 */
function getErrorCode() {
    // Check query parameter first
    if (isset($_GET['error']) && is_numeric($_GET['error'])) {
        return (int)$_GET['error'];
    }
    
    // Check server variable
    if (isset($_SERVER['REDIRECT_STATUS']) && is_numeric($_SERVER['REDIRECT_STATUS'])) {
        return (int)$_SERVER['REDIRECT_STATUS'];
    }
    
    // Default to 403 if no specific error code
    return 403;
}

/**
 * Get error details based on error code
 */
function getErrorDetails($error_code) {
    $errors = [
        400 => [
            'title' => 'Bad Request',
            'message' => 'The request could not be understood by the server.',
            'icon' => 'fas fa-exclamation-triangle',
            'color' => 'warning'
        ],
        401 => [
            'title' => 'Unauthorized',
            'message' => 'Authentication is required to access this resource.',
            'icon' => 'fas fa-lock',
            'color' => 'warning'
        ],
        403 => [
            'title' => 'Access Denied',
            'message' => 'You don\'t have permission to access this resource.',
            'icon' => 'fas fa-shield-alt',
            'color' => 'danger'
        ],
        404 => [
            'title' => 'Page Not Found',
            'message' => 'The requested page could not be found.',
            'icon' => 'fas fa-search',
            'color' => 'info'
        ],
        500 => [
            'title' => 'Server Error',
            'message' => 'An internal server error occurred.',
            'icon' => 'fas fa-server',
            'color' => 'danger'
        ]
    ];
    
    return $errors[$error_code] ?? $errors[403];
}

/**
 * Log error events
 */
function logErrorEvent($error_code, $details = []) {
    global $conn;
    try {
        if (!isset($conn) || $conn === null) {
            error_log("Database connection not available for error logging");
            return;
        }

        // Check if security_logs table exists
        $check_table = $conn->query("SHOW TABLES LIKE 'security_logs'");
        if ($check_table->num_rows == 0) {
            $create_table = "CREATE TABLE IF NOT EXISTS security_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                event_type VARCHAR(100) NOT NULL,
                user_email VARCHAR(255),
                ip_address VARCHAR(45),
                event_data TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )";
            $conn->query($create_table);
        }

        $sql = "INSERT INTO security_logs (event_type, user_email, ip_address, event_data, created_at)
                VALUES (?, ?, ?, ?, NOW())";
        $stmt = $conn->prepare($sql);
        if ($stmt) {
            $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
            $user = $_SESSION['username'] ?? 'anonymous';
            $details_json = json_encode($details);
            $event_type = "http_error_{$error_code}";
            $stmt->bind_param("ssss", $event_type, $user, $ip, $details_json);
            $stmt->execute();
            $stmt->close();
        }
    } catch (Exception $e) {
        error_log("Error logging failed: " . $e->getMessage());
    }
}

/**
 * Get redirect destination based on error code and user session
 */
function getRedirectDestination($error_code) {
    // For authentication errors, always redirect to login
    if (in_array($error_code, [401, 403]) && !isset($_SESSION['username'])) {
        return '/Loginpage.php?error=access_denied';
    }
    
    // For logged-in users, redirect to appropriate dashboard
    if (isset($_SESSION['username']) && isset($_SESSION['access_level'])) {
        $access_level = $_SESSION['access_level'];
        
        switch ($access_level) {
            case 'Super Admin':
            case 'Admin':
            case 'Editor':
            case 'Manager':
            case 'Leader':
            case 'Viewer':
                return '/App/Home';
            case 'Member':
                return '/MemberHome.php';
            default:
                return '/App/Home';
        }
    }
    
    // Default redirect for non-authenticated users
    return '/Loginpage.php';
}

// Get error information
$error_code = getErrorCode();
$error_details = getErrorDetails($error_code);
$redirect_url = getRedirectDestination($error_code);

// Log the error event
$log_details = [
    'error_code' => $error_code,
    'requested_url' => $_SERVER['REQUEST_URI'] ?? 'unknown',
    'referer' => $_SERVER['HTTP_REFERER'] ?? 'direct',
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
    'method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown',
    'timestamp' => date('Y-m-d H:i:s'),
    'session_exists' => isset($_SESSION['username']) ? 'yes' : 'no',
    'access_level' => $_SESSION['access_level'] ?? 'none'
];

logErrorEvent($error_code, $log_details);

// Set appropriate HTTP status code
http_response_code($error_code);

// Check if this is an AJAX request
$is_ajax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';

if ($is_ajax) {
    header('Content-Type: application/json');
    echo json_encode([
        'error' => true,
        'code' => $error_code,
        'title' => $error_details['title'],
        'message' => $error_details['message'],
        'redirect' => $redirect_url
    ]);
    exit();
}

// For regular requests, redirect immediately for 403 errors, show page for others
if ($error_code == 403) {
    header("Location: $redirect_url");
    exit();
}

// For other errors, show error page with redirect
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($error_details['title']); ?> - Redirecting...</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .error-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 40px;
            text-align: center;
            max-width: 500px;
            width: 90%;
        }
        .error-icon {
            font-size: 4rem;
            margin-bottom: 20px;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .countdown {
            font-size: 1.2rem;
            font-weight: bold;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon text-<?php echo $error_details['color']; ?>">
            <i class="<?php echo $error_details['icon']; ?>"></i>
        </div>
        <h2 class="text-<?php echo $error_details['color']; ?> mb-3">
            <?php echo htmlspecialchars($error_details['title']); ?>
        </h2>
        <p class="text-muted mb-4">
            <?php echo htmlspecialchars($error_details['message']); ?>
        </p>
        
        <div class="spinner"></div>
        
        <p class="countdown">Redirecting in <span id="countdown">3</span> seconds...</p>
        
        <div class="mt-4">
            <a href="<?php echo htmlspecialchars($redirect_url); ?>" class="btn btn-primary">
                <i class="fas fa-home me-2"></i>Go Now
            </a>
        </div>
        
        <div class="mt-3">
            <small class="text-muted">Error Code: <?php echo $error_code; ?></small>
        </div>
    </div>

    <script>
        let countdown = 3;
        const countdownElement = document.getElementById('countdown');
        
        const timer = setInterval(() => {
            countdown--;
            countdownElement.textContent = countdown;
            
            if (countdown <= 0) {
                clearInterval(timer);
                window.location.href = '<?php echo addslashes($redirect_url); ?>';
            }
        }, 1000);
    </script>
</body>
</html>
