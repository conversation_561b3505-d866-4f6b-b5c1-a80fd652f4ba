<?php
session_start();

if (!isset($_SESSION['username'])) {
    header("Location: /Loginpage.php");
    exit();
}

// تضمين نظام الصلاحيات
include_once $_SERVER['DOCUMENT_ROOT'].'/App/includes/permissions.php';

// التحقق من صلاحية الوصول للصفحة
checkPagePermission('transactions', $_SESSION['access_level'] ?? 'Viewer');

$access = $_SESSION['access_level'];

function fetchData() {
    include $_SERVER['DOCUMENT_ROOT'].'/config/Databasehost.php';
    $sql = "SELECT Email, AuxName, Auxtime, Starttime, Endtime, Leader, Language, shift, B1, L, B2
            FROM realtime
            ORDER BY Auxtime DESC";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $result = $stmt->get_result();

    $data = [];
    while ($row = $result->fetch_assoc()) {
        $data[] = $row;
    }

    return json_encode($data);
}

function fetchUniqueAuxNames() {
    include $_SERVER['DOCUMENT_ROOT'].'/config/Databasehost.php';
    $sql = "SELECT DISTINCT AuxName FROM realtime";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $result = $stmt->get_result();

    $auxNames = [];
    while ($row = $result->fetch_assoc()) {
        $auxNames[] = $row['AuxName'];
    }

    return json_encode($auxNames);
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    include $_SERVER['DOCUMENT_ROOT'].'/config/Databasehost.php';
    if ($_POST['action'] == 'fetch_data') {
        echo fetchData();
    } elseif ($_POST['action'] == 'fetch_auxnames') {
        echo fetchUniqueAuxNames();
    } elseif ($_POST['action'] == 'set_offline') {
        $email = $_POST['email'];
        $sql = "UPDATE realtime SET AuxName = 'Offline', Starttime = NOW() WHERE Email = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('s', $email);
        if ($stmt->execute()) {
            echo 'success';
        } else {
            echo 'error';
        }
    }
    exit();
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forced Offline - Admin Panel</title>
    <!-- Bootstrap 5 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/secondstyle.css">
</head>
<body class="admin-forced-offline-body">
    <!-- Toast Notification -->
    <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
        <div id="offlineToast" class="toast hide" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header bg-success text-white">
                <strong class="me-auto">Success</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">
                User status set to Offline successfully!
            </div>
        </div>
    </div>

    <div class="admin-forced-offline-container">
        <div class="admin-forced-offline-header">
            <h2 class="admin-forced-offline-title">
                <i class="fas fa-user-slash me-2"></i>Forced Offline Management
            </h2>
            <div class="admin-forced-offline-stats">
                <span class="badge bg-danger me-2">
                    <i class="fas fa-user-times me-1"></i>
                    <span id="count-Offline">0</span> Offline
                </span>
                <span class="badge bg-success">
                    <i class="fas fa-user-check me-1"></i>
                    <span id="count-Online">0</span> Online
                </span>
            </div>
        </div>

        <div class="admin-forced-offline-filters card mb-4">
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-6">
                        <label for="auxname-filter" class="form-label">Filter by Status</label>
                        <select id="auxname-filter" class="form-select" onchange="filterTable()">
                            <option value="all">All Statuses</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label for="search-input" class="form-label">Search by Email</label>
                        <div class="input-group">
                            <input type="text" id="search-input" class="form-control" placeholder="Search by email...">
                            <button class="btn btn-outline-secondary" type="button" onclick="filterTable()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="admin-forced-offline-table card">
            <div class="card-body table-responsive">
                <table class="table table-hover table-striped">
                    <thead class="table-dark">
                        <tr>
                            <th>Email</th>
                            <th>Status</th>
                            <th>Duration</th>
                            <th>Start Time</th>
                            <th>End Time</th>
                            <th>Leader</th>
                            <th>Language</th>
                            <th>Shift</th>
                            <th>Break 1</th>
                            <th>Lunch</th>
                            <th>Break 2</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="data-table-body"></tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
    // Function to fetch data from server
    function fetchData() {
        const xhr = new XMLHttpRequest();
        xhr.open('POST', '', true);
        xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
        xhr.onload = function() {
            if (this.status === 200) {
                const data = JSON.parse(this.responseText);
                const tableBody = document.getElementById('data-table-body');
                tableBody.innerHTML = '';

                data.forEach(row => {
                    const tr = document.createElement('tr');

                    // Determine status class
                    const statusClass = row.AuxName === 'Offline' ? 'status-offline' : 'status-online';

                    tr.innerHTML = `
                        <td>${row.Email}</td>
                        <td class="${statusClass}">${row.AuxName}</td>
                        <td>${row.Auxtime || 'N/A'}</td>
                        <td>${row.Starttime || 'N/A'}</td>
                        <td>${row.Endtime || 'N/A'}</td>
                        <td>${row.Leader || 'N/A'}</td>
                        <td>${row.Language || 'N/A'}</td>
                        <td>${row.shift || 'N/A'}</td>
                        <td>${row.B1 || 'N/A'}</td>
                        <td>${row.L || 'N/A'}</td>
                        <td>${row.B2 || 'N/A'}</td>
                        <td>
                            <button class="btn btn-action btn-offline" onclick="setOffline('${row.Email}')">
                                <i class="fas fa-power-off me-1"></i>Offline
                            </button>
                        </td>
                    `;
                    tableBody.appendChild(tr);
                });

                filterTable();
                updateCounts(data);
            }
        };
        xhr.send('action=fetch_data');
    }

    // Function to fetch unique AuxNames
    function fetchAuxNames() {
        const xhr = new XMLHttpRequest();
        xhr.open('POST', '', true);
        xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
        xhr.onload = function() {
            if (this.status === 200) {
                const auxNames = JSON.parse(this.responseText);
                const dropdown = document.getElementById('auxname-filter');

                // Clear existing options except "All"
                while (dropdown.options.length > 1) {
                    dropdown.remove(1);
                }

                auxNames.forEach(aux => {
                    if (aux !== 'Offline' && aux !== 'Online') {
                        const option = document.createElement('option');
                        option.value = aux;
                        option.textContent = aux;
                        dropdown.appendChild(option);
                    }
                });

                // Add Online and Offline options
                const onlineOption = document.createElement('option');
                onlineOption.value = 'Online';
                onlineOption.textContent = 'Online';
                dropdown.appendChild(onlineOption);

                const offlineOption = document.createElement('option');
                offlineOption.value = 'Offline';
                offlineOption.textContent = 'Offline';
                dropdown.appendChild(offlineOption);
            }
        };
        xhr.send('action=fetch_auxnames');
    }

    // Function to filter table
    function filterTable() {
        const auxFilter = document.getElementById('auxname-filter').value.toLowerCase();
        const searchInput = document.getElementById('search-input').value.toLowerCase();
        const rows = document.getElementById('data-table-body').getElementsByTagName('tr');

        for (let row of rows) {
            const cells = row.getElementsByTagName('td');
            const email = cells[0].innerText.toLowerCase();
            const auxName = cells[1].innerText.toLowerCase();

            const auxMatch = auxFilter === 'all' ||
                            (auxFilter === 'offline' && auxName === 'offline') ||
                            (auxFilter === 'online' && auxName !== 'offline') ||
                            auxName.includes(auxFilter);

            const searchMatch = email.includes(searchInput);

            row.style.display = auxMatch && searchMatch ? '' : 'none';
        }
    }

    // Function to update counts
    function updateCounts(data) {
        const counts = { Offline: 0, Online: 0 };
        data.forEach(row => {
            if (row.AuxName === 'Offline') {
                counts.Offline++;
            } else {
                counts.Online++;
            }
        });

        document.getElementById('count-Offline').innerText = counts.Offline;
        document.getElementById('count-Online').innerText = counts.Online;
    }

    // Function to set user offline
    function setOffline(email) {
        if (confirm('Are you sure you want to set this user as Offline?')) {
            const xhr = new XMLHttpRequest();
            xhr.open('POST', '', true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            xhr.onload = function() {
                if (this.status === 200 && this.responseText === 'success') {
                    // Show Bootstrap toast notification
                    const toast = new bootstrap.Toast(document.getElementById('offlineToast'));
                    toast.show();

                    fetchData();
                } else {
                    alert('Failed to update user status.');
                }
            };
            xhr.send(`action=set_offline&email=${encodeURIComponent(email)}`);
        }
    }

    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function() {
        fetchData();
        fetchAuxNames();

        // Set up auto-refresh every 5 seconds
        setInterval(fetchData, 5000);
    });
    </script>
</body>
</html>