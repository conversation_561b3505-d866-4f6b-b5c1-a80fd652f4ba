<?php
/**
 * صفحة قاعدة البيانات الذكية - تتكيف مع أي قاعدة بيانات
 * Smart Database Page - Adapts to ANY database structure
 */

session_start();
require_once '../../../config/database.php';
require_once 'core/smart_adapter.php';
require_once 'core/smart_table.php';

// التحقق من الجلسة
if (!isset($_SESSION['username'])) {
    header("Location: ../../../Loginpage.php");
    exit();
}

$userEmail = $_SESSION['username'];
$access = $_SESSION['access_level'] ?? 'Viewer';

// إعدادات قابلة للتخصيص
$tableName = $_GET['table'] ?? 'databasehc'; // يمكن تغيير اسم الجدول من URL
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 50;
$search = $_GET['search'] ?? '';

try {
    // الاتصال بقاعدة البيانات
    $conn = new mysqli($servername, $username, $password, $dbname);
    
    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }
    
    // التحقق من وجود الجدول
    $tableCheck = $conn->query("SHOW TABLES LIKE '$tableName'");
    if (!$tableCheck || $tableCheck->num_rows === 0) {
        throw new Exception("Table '$tableName' does not exist");
    }
    
    // إنشاء النظام الذكي
    $smartTable = new SmartTable($conn, $tableName, $userEmail);
    $adapter = $smartTable->getAdapter();
    
    // معالجة الطلبات
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'delete':
                    if ($access !== 'Viewer' && isset($_POST['id'])) {
                        $id = $_POST['id'];
                        $stmt = $conn->prepare("DELETE FROM $tableName WHERE ID = ?");
                        $stmt->bind_param("i", $id);
                        if ($stmt->execute()) {
                            echo json_encode(['success' => true, 'message' => 'Record deleted successfully']);
                        } else {
                            echo json_encode(['success' => false, 'message' => 'Error deleting record']);
                        }
                        exit;
                    }
                    break;
                    
                case 'export':
                    // تصدير البيانات
                    $exportFilters = [];
                    foreach ($adapter->getFilterableColumns() as $column) {
                        if (isset($_POST[$column]) && !empty($_POST[$column])) {
                            $exportFilters[$column] = is_array($_POST[$column]) ? $_POST[$column] : [$_POST[$column]];
                        }
                    }

                    $data = $smartTable->getData($exportFilters, 1, 10000, $_POST['search'] ?? '');

                    header('Content-Type: text/csv');
                    header('Content-Disposition: attachment;filename="' . $tableName . '_export.csv"');
                    header('Cache-Control: max-age=0');

                    $output = fopen('php://output', 'w');

                    // كتابة رأس الملف
                    $headers = [];
                    foreach ($smartTable->getVisibleColumns() as $column) {
                        $headers[] = $adapter->getColumnDisplayName($column);
                    }
                    fputcsv($output, $headers);

                    // كتابة البيانات
                    foreach ($data as $row) {
                        $rowData = [];
                        foreach ($smartTable->getVisibleColumns() as $column) {
                            $rowData[] = $row[$column] ?? '';
                        }
                        fputcsv($output, $rowData);
                    }

                    fclose($output);
                    exit;
                    break;
            }
        }
    }
    
    // معالجة الفلاتر
    $filters = [];
    foreach ($adapter->getFilterableColumns() as $column) {
        if (isset($_GET[$column]) && !empty($_GET[$column])) {
            $filters[$column] = is_array($_GET[$column]) ? $_GET[$column] : [$_GET[$column]];
        }
    }
    
    // الحصول على البيانات
    $data = $smartTable->getData($filters, $page, $limit, $search);
    $totalRecords = $smartTable->getTotalCount($filters, $search);
    $totalPages = ceil($totalRecords / $limit);
    
} catch (Exception $e) {
    $error = $e->getMessage();
    error_log("Smart Database Error: " . $error);
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Database - <?php echo ucfirst($tableName); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .container-fluid {
            padding: 20px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .card-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 20px;
        }
        
        .table-container {
            max-height: 600px;
            overflow-y: auto;
        }
        
        .filter-group {
            margin-bottom: 15px;
        }
        
        .filter-group label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
            display: block;
        }
        
        .empty-state {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.5;
        }
        
        .smart-badge {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .pagination {
            justify-content: center;
        }
        
        .btn-smart {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            border-radius: 25px;
            padding: 8px 20px;
            transition: all 0.3s ease;
        }
        
        .btn-smart:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <?php if (isset($error)): ?>
            <div class="alert alert-danger" role="alert">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>Error:</strong> <?php echo htmlspecialchars($error); ?>
            </div>
        <?php else: ?>
            
            <!-- Header -->
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h2 class="mb-0">
                                <i class="fas fa-database me-2"></i>
                                Smart Database: <?php echo ucfirst($tableName); ?>
                                <span class="smart-badge">AI-Powered</span>
                            </h2>
                            <p class="mb-0 mt-2 opacity-75">
                                Automatically adapts to any database structure
                            </p>
                        </div>
                        <div class="col-auto">
                            <span class="badge bg-light text-dark fs-6">
                                <?php echo number_format($totalRecords); ?> records
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Smart Filters</h5>
                </div>
                <div class="card-body">
                    <form method="GET" id="filterForm">
                        <input type="hidden" name="table" value="<?php echo htmlspecialchars($tableName); ?>">
                        
                        <div class="row">
                            <!-- Search -->
                            <div class="col-md-4 mb-3">
                                <label for="search">Search</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       value="<?php echo htmlspecialchars($search); ?>" 
                                       placeholder="Search in all columns...">
                            </div>
                            
                            <!-- Dynamic Filters -->
                            <?php
                            $filterableColumns = $adapter->getFilterableColumns();
                            $displayedFilters = 0;
                            foreach ($filterableColumns as $column):
                                if ($displayedFilters >= 6) break; // حد أقصى 6 فلاتر في الصف الأول
                                $options = $adapter->getFilterOptions($column, 50);
                                if (!empty($options)):
                                    $displayedFilters++;
                            ?>
                                <div class="col-md-2 mb-3">
                                    <label for="<?php echo $column; ?>"><?php echo $adapter->getColumnDisplayName($column); ?></label>
                                    <select name="<?php echo $column; ?>[]" id="<?php echo $column; ?>" class="form-control" multiple>
                                        <?php foreach ($options as $option): ?>
                                            <option value="<?php echo htmlspecialchars($option); ?>"
                                                <?php echo (isset($filters[$column]) && in_array($option, $filters[$column])) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($option); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            <?php 
                                endif;
                            endforeach; 
                            ?>
                        </div>
                        
                        <div class="row">
                            <div class="col">
                                <button type="submit" class="btn btn-smart">
                                    <i class="fas fa-search me-2"></i>Apply Filters
                                </button>
                                <a href="?table=<?php echo htmlspecialchars($tableName); ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-2"></i>Clear
                                </a>
                                
                                <?php if ($access !== 'Viewer'): ?>
                                    <button type="submit" name="action" value="export" class="btn btn-outline-success">
                                        <i class="fas fa-download me-2"></i>Export CSV
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Data Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>Data Table
                        <small class="text-light">(Page <?php echo $page; ?> of <?php echo $totalPages; ?>)</small>
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-container">
                        <?php echo $smartTable->generateTable($data, $access !== 'Viewer', 'editdatabase.php', $access === 'Admin'); ?>
                    </div>
                </div>
            </div>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <nav aria-label="Page navigation">
                    <ul class="pagination">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?table=<?php echo $tableName; ?>&page=<?php echo $page-1; ?>&search=<?php echo urlencode($search); ?>">Previous</a>
                            </li>
                        <?php endif; ?>
                        
                        <?php for ($i = max(1, $page-2); $i <= min($totalPages, $page+2); $i++): ?>
                            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?table=<?php echo $tableName; ?>&page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>"><?php echo $i; ?></a>
                            </li>
                        <?php endfor; ?>
                        
                        <?php if ($page < $totalPages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?table=<?php echo $tableName; ?>&page=<?php echo $page+1; ?>&search=<?php echo urlencode($search); ?>">Next</a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            <?php endif; ?>

        <?php endif; ?>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // Handle delete buttons
            $('.delete-btn').click(function() {
                if (confirm('Are you sure you want to delete this record?')) {
                    const id = $(this).data('id');
                    
                    $.post('', {
                        action: 'delete',
                        id: id
                    }, function(response) {
                        const result = JSON.parse(response);
                        if (result.success) {
                            location.reload();
                        } else {
                            alert('Error: ' + result.message);
                        }
                    });
                }
            });
            
            // Auto-submit form on filter change
            $('select[multiple]').change(function() {
                setTimeout(function() {
                    $('#filterForm').submit();
                }, 500);
            });
        });
    </script>
</body>
</html>
