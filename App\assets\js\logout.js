// Modern Bootstrap Modal Functions
function showModal() {
    // Check if Bootstrap modal exists
    if (document.getElementById('logoutModal')) {
        var logoutModal = new bootstrap.Modal(document.getElementById('logoutModal'), {
            backdrop: false, // Disable default backdrop since we use custom blur
            keyboard: true
        });
        logoutModal.show();
    }
    // Fallback to old modal
    else if (document.getElementById('confirmModal')) {
        document.getElementById("confirmModal").style.display = "block";
    }
}

function closeModal() {
    // Check if Bootstrap modal exists
    if (document.getElementById('logoutModal')) {
        var logoutModal = bootstrap.Modal.getInstance(document.getElementById('logoutModal'));
        if (logoutModal) {
            logoutModal.hide();
        }
    }
    // Fallback to old modal
    else if (document.getElementById('confirmModal')) {
        document.getElementById("confirmModal").style.display = "none";
    }
}

// Alternative function names for compatibility
function hideModal() {
    closeModal();
}

function logout() {
    console.log('Starting logout process...');

    // Close modal first
    closeModal();

    // Show loading state
    const loadingHtml = `
        <div class="d-flex flex-column align-items-center py-3">
            <div class="spinner-border text-primary mb-3" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p>Logging out, please wait...</p>
        </div>
    `;

    // Create temporary modal for loading
    const tempModal = document.createElement('div');
    tempModal.innerHTML = `
        <div class="modal fade show" style="display: block; background: rgba(0,0,0,0.5);" tabindex="-1">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    ${loadingHtml}
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(tempModal);

    // إرسال طلب AJAX لتحديث وقت تسجيل الخروج
    var xhr = new XMLHttpRequest();
    xhr.open("POST", "/App/Logpage/logout.php", true);
    xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
    xhr.setRequestHeader("X-Requested-With", "XMLHttpRequest");
    xhr.timeout = 5000;
    xhr.onreadystatechange = function () {
        if (xhr.readyState === XMLHttpRequest.DONE) {
            console.log('Logout request completed. Status:', xhr.status);
            console.log('Response text:', xhr.responseText);

            // Remove loading modal
            if (tempModal && tempModal.parentNode) {
                tempModal.parentNode.removeChild(tempModal);
            }

            if (xhr.status === 200) {
                try {
                    var response = JSON.parse(xhr.responseText);
                    console.log('Parsed response:', response);

                    if (response.status === 'success') {
                        console.log('Logout successful, redirecting...');
                        // تسجيل الخروج الفوري
                        window.location.href = '/Loginpage';
                    } else {
                        console.error('Logout failed:', response.message);
                        // حتى لو فشل، نعمل redirect للـ login page
                        window.location.href = '/Loginpage.php';
                    }
                } catch (e) {
                    console.error('JSON parse error:', e);
                    console.error('Response text:', xhr.responseText);
                    // تسجيل الخروج حتى لو فشل parsing
                    window.location.href = '/Loginpage.php';
                }
            } else {
                console.error('HTTP Error:', xhr.status);
                // حتى لو فشل، نعمل redirect للـ login page
                window.location.href = '/Loginpage.php';
            }
        }
    };

    // Add timeout handler
    xhr.ontimeout = function() {
        console.error('⏰ Logout request timed out');
        // Remove loading modal
        if (tempModal && tempModal.parentNode) {
            tempModal.parentNode.removeChild(tempModal);
        }
        // Still redirect even if timeout
        window.location.href = '/Loginpage';
    };

    // Add error handler
    xhr.onerror = function() {
        console.error('🌐 Network error during logout');
        // Remove loading modal
        if (tempModal && tempModal.parentNode) {
            tempModal.parentNode.removeChild(tempModal);
        }
        // Still redirect even if network error
        window.location.href = '/Loginpage';
    };

    xhr.send("update_logout_time=1");
}

// Ensure modal events are properly bound when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners for modal buttons
    const logoutModal = document.getElementById('logoutModal');
    if (logoutModal) {
        // Cancel button event
        const cancelBtn = logoutModal.querySelector('.cancel-btn');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', function(e) {
                e.preventDefault();
                closeModal();
            });
        }

        // Logout button event
        const logoutBtn = logoutModal.querySelector('.logout-btn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', function(e) {
                e.preventDefault();
                logout();
            });
        }

        // Close button event
        const closeBtn = logoutModal.querySelector('.btn-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', function(e) {
                e.preventDefault();
                closeModal();
            });
        }
    }
});