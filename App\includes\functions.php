<?php
function getUserData($conn) {
    $email = $_SESSION['username'];
    $sql = "SELECT Image FROM usersprofile WHERE Email = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();
    return ($result->num_rows > 0) ? $result->fetch_assoc() : null;

}

function getNameFromEmail($email) {
    $beforeAt = explode('@', $email)[0];
    if (strpos($beforeAt, '.') !== false) {
        $nameParts = explode('.', $beforeAt);
        return [
            'first' => ucfirst($nameParts[0]),
            'last' => ucfirst($nameParts[1])
        ];
    }
    return [
        'first' => ucfirst($beforeAt),
        'last' => ''
    ];
}

//cards
function getStatistics($conn) {
    // تضمين ملف فلترة الليدر
    include_once $_SERVER['DOCUMENT_ROOT'].'/App/includes/leader_filter.php';

    // الحصول على صلاحية المستخدم الحالي
    $user_permission = $_SESSION['access_level'] ?? 'Viewer';
    $user_email = $_SESSION['username'] ?? '';

    // استخدام الدالة الجديدة للحصول على الإحصائيات مع الفلترة
    return getStatisticsWithLeaderFilter($conn, $user_permission, $user_email);
}

function getActiveHeadcount($conn) {
    // Use smart dynamic function instead of fixed one
    return getSmartActiveHeadcount($conn, 'databasehc');
}
function getRealtimeData($conn, $type = 'online') {
    // تضمين ملف فلترة الليدر
    include_once $_SERVER['DOCUMENT_ROOT'].'/App/includes/leader_filter.php';

    // الحصول على صلاحية المستخدم الحالي
    $user_permission = $_SESSION['access_level'] ?? 'Viewer';
    $user_email = $_SESSION['username'] ?? '';

    // استخدام الدالة الجديدة للحصول على بيانات الوقت الفعلي مع الفلترة
    return getRealtimeDataWithLeaderFilter($conn, $type, $user_permission, $user_email);
}

function getChartData($conn) {
    $data = [];

    // Use last 30 days instead of current month to ensure we have data
    $startDate = date('Y-m-d', strtotime('-30 days'));
    $endDate = date('Y-m-d');

    // الحصول على معلومات المستخدم للفلترة
    $user_permission = $_SESSION['access_level'] ?? 'Viewer';
    $user_email = $_SESSION['username'] ?? '';

    error_log("getChartData: Searching for data from $startDate to $endDate");

    try {
        // جلب بيانات مدة الـ Online من جدول interpretersaux
        $sql = "SELECT
                    DATE(ia.Starttime) AS day,
                    ROUND(SUM(TIME_TO_SEC(ia.Duration)) / 3600, 2) AS online_hours,
                    COUNT(*) AS session_count
                FROM interpretersaux ia
                LEFT JOIN databasehc dh ON ia.Email = dh.FGEmails
                WHERE ia.AuxName = 'Online'
                AND DATE(ia.Starttime) BETWEEN ? AND ?";

        // تطبيق فيلتر الليدر
        $params = [$startDate, $endDate];
        if ($user_permission === 'Leader') {
            // للـ Leader: فيلتر تلقائي بناءً على ليدرهم
            $leader_name = findLeaderNameInDatabase($user_email);
            if ($leader_name) {
                $sql .= " AND dh.Leader = ?";
                $params[] = $leader_name;

                if (isset($_GET['debug']) && $_GET['debug'] == '1') {
                    error_log("getChartData: Applying leader filter for: $leader_name");
                }
            }
        }

        $sql .= " GROUP BY DATE(ia.Starttime) ORDER BY DATE(ia.Starttime)";

        if (isset($_GET['debug']) && $_GET['debug'] == '1') {
            error_log("getChartData: SQL Query: " . $sql);
            error_log("getChartData: Parameters: " . implode(', ', $params));
        }

        $stmt = $conn->prepare($sql);
        if ($stmt) {
            // ربط المعاملات بناءً على عددها
            $param_types = str_repeat('s', count($params));
            $stmt->bind_param($param_types, ...$params);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result && $result->num_rows > 0) {
                if (isset($_GET['debug']) && $_GET['debug'] == '1') {
                    error_log("getChartData: Found {$result->num_rows} days with Online data in interpretersaux");
                }

                while ($row = $result->fetch_assoc()) {
                    $data[] = [
                        'name' => $row['day'],
                        'y' => (float)$row['online_hours']
                    ];

                    if (isset($_GET['debug']) && $_GET['debug'] == '1') {
                        error_log("getChartData: Day {$row['day']}: {$row['online_hours']} hours from {$row['session_count']} sessions");
                    }
                }
            } else {
                if (isset($_GET['debug']) && $_GET['debug'] == '1') {
                    error_log("getChartData: No Online records found in interpretersaux for current month");

                    // تحقق من وجود بيانات عامة في الجدول مع فيلتر الليدر
                    $check_sql = "SELECT COUNT(*) as total,
                                         COUNT(CASE WHEN ia.AuxName = 'Online' THEN 1 END) as online_count,
                                         MIN(DATE(ia.Starttime)) as min_date,
                                         MAX(DATE(ia.Starttime)) as max_date
                                  FROM interpretersaux ia
                                  LEFT JOIN databasehc dh ON ia.Email = dh.FGEmails
                                  WHERE 1=1";

                    $check_params = [];
                    if ($user_permission === 'Leader') {
                        $leader_name = findLeaderNameInDatabase($user_email);
                        if ($leader_name) {
                            $check_sql .= " AND dh.Leader = ?";
                            $check_params[] = $leader_name;
                        }
                    }

                    if (!empty($check_params)) {
                        $check_stmt = $conn->prepare($check_sql);
                        $check_stmt->bind_param(str_repeat('s', count($check_params)), ...$check_params);
                        $check_stmt->execute();
                        $check_result = $check_stmt->get_result();
                        $check_stmt->close();
                    } else {
                        $check_result = $conn->query($check_sql);
                    }

                    if ($check_result) {
                        $check_data = $check_result->fetch_assoc();
                        error_log("getChartData: Table stats (with leader filter) - Total: {$check_data['total']}, Online: {$check_data['online_count']}, Date range: {$check_data['min_date']} to {$check_data['max_date']}");
                    }

                    // فحص إضافي للشهر الحالي مع فيلتر الليدر
                    $month_check_sql = "SELECT COUNT(*) as current_month_total,
                                               COUNT(CASE WHEN ia.AuxName = 'Online' THEN 1 END) as current_month_online
                                        FROM interpretersaux ia
                                        LEFT JOIN databasehc dh ON ia.Email = dh.FGEmails
                                        WHERE DATE(ia.Starttime) BETWEEN ? AND ?";

                    $month_params = [$firstDayOfMonth, $lastDayOfMonth];
                    if ($user_permission === 'Leader') {
                        $leader_name = findLeaderNameInDatabase($user_email);
                        if ($leader_name) {
                            $month_check_sql .= " AND dh.Leader = ?";
                            $month_params[] = $leader_name;
                        }
                    }

                    $month_stmt = $conn->prepare($month_check_sql);
                    if ($month_stmt) {
                        $month_stmt->bind_param(str_repeat('s', count($month_params)), ...$month_params);
                        $month_stmt->execute();
                        $month_result = $month_stmt->get_result();
                        if ($month_result) {
                            $month_data = $month_result->fetch_assoc();
                            error_log("getChartData: Current month stats (with leader filter) - Total: {$month_data['current_month_total']}, Online: {$month_data['current_month_online']}");
                        }
                        $month_stmt->close();
                    }
                }
            }
            $stmt->close();
        }

        // إذا لم توجد بيانات في interpretersaux، أرجع مصفوفة فارغة مع رسالة
        if (empty($data)) {
            if (isset($_GET['debug']) && $_GET['debug'] == '1') {
                error_log("getChartData: No Online data found in interpretersaux for current month");
            }

            // إرجاع بيانات فارغة مع رسالة توضيحية
            $data = [
                [
                    'name' => 'No Data',
                    'y' => 0,
                    'message' => 'No online hours data available for current month'
                ]
            ];
        }

    } catch (Exception $e) {
        error_log("Error in getChartData: " . $e->getMessage());

        // في حالة الخطأ، أرجع رسالة خطأ
        $data = [
            [
                'name' => 'Error',
                'y' => 0,
                'message' => 'Error loading chart data: ' . $e->getMessage()
            ]
        ];

        if (isset($_GET['debug']) && $_GET['debug'] == '1') {
            error_log("getChartData: Error occurred, returning error message instead of sample data");
        }
    }

    return json_encode($data);
}

// إضافة دالة renderHeadcountTable من ComponentLeaderTable.php
function renderHeadcountTable($conn, $tableName, $statusColumn, $statusValue) {
    // استعلام للحصول على القيم الفريدة من Leader
    $sql = "SELECT DISTINCT leader FROM $tableName WHERE $statusColumn = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $statusValue);
    $stmt->execute();
    $leaderResult = $stmt->get_result();

    // استعلام للحصول على القيم الفريدة من Module (باستثناء Hybrid Module)
    $moduleSql = "SELECT Module, leader, COUNT(*) as count FROM $tableName WHERE $statusColumn = ? AND Module != 'Hybrid Module' GROUP BY Module, leader";
    $stmt = $conn->prepare($moduleSql);
    $stmt->bind_param("s", $statusValue);
    $stmt->execute();
    $moduleResult = $stmt->get_result();

    // تخزين القيم الفريدة من Module
    $modules = [];
    $leaders = []; // تخزين القادة بشكل منفصل لتفادي التكرار
    while ($row = $moduleResult->fetch_assoc()) {
        $modules[$row['leader']][$row['Module']] = $row['count'];
        if (!in_array($row['leader'], $leaders)) {
            $leaders[] = $row['leader'];
        }
    }

    // استعلام للحصول على Hybrid Module بشكل منفصل
    $hybridModuleSql = "SELECT leader, COUNT(*) as count FROM $tableName WHERE $statusColumn = ? AND Module = 'Hybrid Module' GROUP BY leader";
    $stmt = $conn->prepare($hybridModuleSql);
    $stmt->bind_param("s", $statusValue);
    $stmt->execute();
    $hybridModuleResult = $stmt->get_result();

    // تخزين القيم الخاصة بـ Hybrid Module
    $hybridModules = [];
    while ($row = $hybridModuleResult->fetch_assoc()) {
        $hybridModules[$row['leader']] = $row['count'];
    }

    // طباعة الجدول
    ?>
    <table class="leader-table">
        <thead>
            <tr>
                <th colspan="100%" style="text-align:center; font-size: 18px; padding: 10px; font-weight: bold;">Headcount Per Team Leader</th>
            </tr>
            <tr>
                <th>Leader</th> <!-- العمود الأول للـ Leader -->
                <?php
                // إضافة الأعمدة الديناميكية استنادًا إلى القيم الفريدة من Module
                $printedModules = []; // تعريف المتغير هنا
                foreach ($modules as $leader => $modulesForLeader) {
                    foreach ($modulesForLeader as $module => $count) {
                        // طباعة الأعمدة فقط إذا لم تتم طباعتها من قبل
                        if (!isset($printedModules[$module])) {
                            echo "<th>" . htmlspecialchars($module) . "</th>"; // طباعة أسماء الأعمدة (Modules)
                            $printedModules[$module] = true;
                        }
                    }
                }
                ?>
                <th>Hybrid Module</th> <!-- عمود Hybrid Module -->
                <th>Total</th> <!-- عمود Total -->
            </tr>
        </thead>
        <tbody>
            <?php
            // عرض البيانات
            if (count($leaders) > 0) {
                foreach ($leaders as $leader) {
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($leader) . "</td>"; // عرض الـ Leader

                    // حساب الإجمالي للموديلات
                    $total = 0; // متغير لحساب الإجمالي لكل صف
                    $hybridModuleTotal = isset($hybridModules[$leader]) ? $hybridModules[$leader] : 0; // الحصول على قيمة Hybrid Module بشكل منفصل

                    // عرض القيم لكل موديول
                    foreach ($printedModules as $module => $dummy) {
                        $count = isset($modules[$leader][$module]) ? $modules[$leader][$module] : 0;
                        echo "<td>" . $count . "</td>"; // عرض العد لكل موديول
                        $total += $count; // إضافة العد إلى الإجمالي
                    }

                    // عرض Hybrid Module
                    echo "<td>" . $hybridModuleTotal . "</td>"; // عرض إجمالي Hybrid Module

                    // حساب الإجمالي الكلي بما في ذلك Hybrid Module
                    $total += $hybridModuleTotal; // إضافة قيمة Hybrid Module إلى الإجمالي الكلي

                    // عرض عمود Total
                    echo "<td>" . $total . "</td>"; // عرض الإجمالي الكلي
                    echo "</tr>";
                }
            } else {
                echo "<tr><td colspan='100%'>No data found</td></tr>";
            }
            ?>
        </tbody>
    </table>
    <?php
}

// ==================== DYNAMIC TABLE FUNCTIONS ====================

/**
 * Get table structure dynamically
 * دالة للحصول على بنية الجدول بشكل ديناميكي
 */
function getTableStructure($conn, $tableName) {
    $structure = [];
    $result = $conn->query("DESCRIBE $tableName");

    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $structure[] = [
                'field' => $row['Field'],
                'type' => $row['Type'],
                'null' => $row['Null'],
                'key' => $row['Key'],
                'default' => $row['Default']
            ];
        }
    }

    return $structure;
}

/**
 * Smart column detection for status/state columns
 * كشف ذكي لأعمدة الحالة
 */
function findStatusColumn($conn, $tableName) {
    $possibleColumns = ['status', 'Status', 'state', 'State', 'condition', 'Condition'];

    foreach ($possibleColumns as $col) {
        $result = $conn->query("SELECT COUNT(*) as count FROM $tableName WHERE $col IS NOT NULL LIMIT 1");
        if ($result && !$conn->error) {
            return $col;
        }
    }

    return null;
}

/**
 * Get distinct values from a column
 * الحصول على القيم المميزة من عمود
 */
function getDistinctValues($conn, $tableName, $columnName) {
    $values = [];
    $result = $conn->query("SELECT DISTINCT $columnName as value, COUNT(*) as count FROM $tableName GROUP BY $columnName");

    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $values[] = $row;
        }
    }

    return $values;
}

/**
 * Smart Active Headcount - adapts to any table structure
 * جدول Active Headcount ذكي يتكيف مع أي بنية جدول
 */
function getSmartActiveHeadcount($conn, $tableName = 'databasehc') {
    $data = [];
    $total = 0;

    try {
        // 1. Find status column dynamically
        $statusColumn = findStatusColumn($conn, $tableName);
        if (!$statusColumn) {
            error_log("No status column found in $tableName");
            return ['data' => [], 'total' => 0, 'error' => 'No status column found'];
        }

        // 2. Get distinct status values
        $statusValues = getDistinctValues($conn, $tableName, $statusColumn);
        error_log("Found status values: " . json_encode($statusValues));

        // 3. Find the "active" status (look for common patterns)
        $activeStatus = null;
        $activePatterns = ['production', 'active', 'in production', 'working', 'online'];

        foreach ($statusValues as $status) {
            $statusLower = strtolower($status['value']);
            foreach ($activePatterns as $pattern) {
                if (strpos($statusLower, $pattern) !== false) {
                    $activeStatus = $status['value'];
                    break 2;
                }
            }
        }

        // If no pattern match, use the status with most records
        if (!$activeStatus && !empty($statusValues)) {
            $maxCount = 0;
            foreach ($statusValues as $status) {
                if ($status['count'] > $maxCount) {
                    $maxCount = $status['count'];
                    $activeStatus = $status['value'];
                }
            }
        }

        error_log("Selected active status: $activeStatus");

        if (!$activeStatus) {
            return ['data' => [], 'total' => 0, 'error' => 'No active status found'];
        }

        // 4. Check if Module column exists
        $moduleColumn = null;
        $possibleModuleColumns = ['Module', 'module', 'Department', 'department', 'Team', 'team'];

        foreach ($possibleModuleColumns as $col) {
            $result = $conn->query("SELECT COUNT(*) as count FROM $tableName WHERE $col IS NOT NULL LIMIT 1");
            if ($result && !$conn->error) {
                $moduleColumn = $col;
                break;
            }
        }

        if (!$moduleColumn) {
            // If no module column, just count total
            $sql = "SELECT 'Total' as Module, COUNT(*) as count FROM $tableName WHERE $statusColumn = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("s", $activeStatus);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result && $result->num_rows > 0) {
                $row = $result->fetch_assoc();
                $data[] = $row;
                $total = $row['count'];
            }
        } else {
            // Group by module
            $sql = "SELECT $moduleColumn as Module, COUNT(*) as count FROM $tableName WHERE $statusColumn = ? GROUP BY $moduleColumn ORDER BY $moduleColumn";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("s", $activeStatus);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result && $result->num_rows > 0) {
                while ($row = $result->fetch_assoc()) {
                    $data[] = $row;
                    $total += $row['count'];
                }
            }
        }

        error_log("Smart Active Headcount - Found $total total records");

    } catch (Exception $e) {
        error_log("Error in getSmartActiveHeadcount: " . $e->getMessage());
        return ['data' => [], 'total' => 0, 'error' => $e->getMessage()];
    }

    return [
        'data' => $data,
        'total' => $total,
        'status_column' => $statusColumn,
        'active_status' => $activeStatus,
        'module_column' => $moduleColumn
    ];
}

/**
 * Smart Leader Headcount Table - completely dynamic
 * جدول ليدر ذكي ومرن تماماً
 */
function renderSmartHeadcountTable($conn, $tableName = 'databasehc') {
    try {
        // 1. Get table structure
        $structure = getTableStructure($conn, $tableName);
        $columns = array_column($structure, 'field');

        // 2. Find important columns dynamically
        $statusColumn = findStatusColumn($conn, $tableName);
        $leaderColumn = null;
        $moduleColumn = null;

        // Find leader column
        $possibleLeaderColumns = ['Leader', 'leader', 'Manager', 'manager', 'Supervisor', 'supervisor'];
        foreach ($possibleLeaderColumns as $col) {
            if (in_array($col, $columns)) {
                $leaderColumn = $col;
                break;
            }
        }

        // Find module column
        $possibleModuleColumns = ['Module', 'module', 'Department', 'department', 'Team', 'team'];
        foreach ($possibleModuleColumns as $col) {
            if (in_array($col, $columns)) {
                $moduleColumn = $col;
                break;
            }
        }

        if (!$statusColumn || !$leaderColumn) {
            echo "<p style='color: red;'>Required columns not found. Status: $statusColumn, Leader: $leaderColumn</p>";
            return;
        }

        // 3. Get active status
        $statusValues = getDistinctValues($conn, $tableName, $statusColumn);
        $activeStatus = null;
        $activePatterns = ['production', 'active', 'in production', 'working', 'online'];

        foreach ($statusValues as $status) {
            $statusLower = strtolower($status['value']);
            foreach ($activePatterns as $pattern) {
                if (strpos($statusLower, $pattern) !== false) {
                    $activeStatus = $status['value'];
                    break 2;
                }
            }
        }

        if (!$activeStatus && !empty($statusValues)) {
            $maxCount = 0;
            foreach ($statusValues as $status) {
                if ($status['count'] > $maxCount) {
                    $maxCount = $status['count'];
                    $activeStatus = $status['value'];
                }
            }
        }

        // 4. Get leaders
        $leaders = getDistinctValues($conn, $tableName, $leaderColumn);

        // 5. Get modules (if exists)
        $modules = [];
        if ($moduleColumn) {
            $modules = getDistinctValues($conn, $tableName, $moduleColumn);
        }

        // 6. Build dynamic query and get data
        if ($moduleColumn) {
            // With modules
            $sql = "SELECT $leaderColumn as leader, $moduleColumn as module, COUNT(*) as count
                    FROM $tableName
                    WHERE $statusColumn = ?
                    GROUP BY $leaderColumn, $moduleColumn
                    ORDER BY $leaderColumn, $moduleColumn";
        } else {
            // Without modules, just count by leader
            $sql = "SELECT $leaderColumn as leader, COUNT(*) as count
                    FROM $tableName
                    WHERE $statusColumn = ?
                    GROUP BY $leaderColumn
                    ORDER BY $leaderColumn";
        }

        $stmt = $conn->prepare($sql);
        $stmt->bind_param("s", $activeStatus);
        $stmt->execute();
        $result = $stmt->get_result();

        // 7. Organize data
        $data = [];
        $moduleList = [];

        while ($row = $result->fetch_assoc()) {
            $leader = $row['leader'];
            if ($moduleColumn && isset($row['module'])) {
                $module = $row['module'];
                $data[$leader][$module] = $row['count'];
                if (!in_array($module, $moduleList)) {
                    $moduleList[] = $module;
                }
            } else {
                $data[$leader]['total'] = $row['count'];
            }
        }

        // 8. Render table
        echo "<table class='leader-table'>";
        echo "<thead>";
        echo "<tr>";
        echo "<th colspan='" . (count($moduleList) + 2) . "' style='text-align:center; font-size: 18px; padding: 10px; font-weight: bold;'>";
        echo "Headcount Per Team Leader (Dynamic)";
        echo "</th>";
        echo "</tr>";
        echo "<tr>";
        echo "<th>Leader</th>";

        if ($moduleColumn && !empty($moduleList)) {
            foreach ($moduleList as $module) {
                echo "<th>" . htmlspecialchars($module) . "</th>";
            }
        }

        echo "<th>Total</th>";
        echo "</tr>";
        echo "</thead>";
        echo "<tbody>";

        if (!empty($data)) {
            foreach ($data as $leader => $leaderData) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($leader) . "</td>";

                $total = 0;

                if ($moduleColumn && !empty($moduleList)) {
                    foreach ($moduleList as $module) {
                        $count = isset($leaderData[$module]) ? $leaderData[$module] : 0;
                        echo "<td>$count</td>";
                        $total += $count;
                    }
                } else {
                    $total = isset($leaderData['total']) ? $leaderData['total'] : 0;
                }

                echo "<td><strong>$total</strong></td>";
                echo "</tr>";
            }
        } else {
            $colspan = $moduleColumn ? count($moduleList) + 2 : 2;
            echo "<tr><td colspan='$colspan'>No data found</td></tr>";
        }

        echo "</tbody>";
        echo "</table>";

        // Debug info
        echo "<div style='margin-top: 10px; font-size: 12px; color: #666;'>";
        echo "Status Column: $statusColumn | Active Status: $activeStatus | ";
        echo "Leader Column: $leaderColumn | Module Column: " . ($moduleColumn ?: 'None');
        echo "</div>";

    } catch (Exception $e) {
        echo "<p style='color: red;'>Error rendering smart table: " . $e->getMessage() . "</p>";
        error_log("Error in renderSmartHeadcountTable: " . $e->getMessage());
    }
}
