<?php
/**
 * إصلاح نهائي لمشاكل التواريخ في قاعدة البيانات
 * Final fix for date issues in database
 */

require_once '../../../config/database.php';

echo "<h1>🔧 إصلاح نهائي لمشاكل التواريخ</h1>";

try {
    // Connect to database
    $conn = new mysqli($servername, $username, $password, $dbname);
    
    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }
    
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<p>✅ متصل بقاعدة البيانات بنجاح</p>";
    echo "</div>";
    
    // Step 1: Find all date columns
    echo "<h2>الخطوة 1: البحث عن أعمدة التاريخ</h2>";
    
    $dateColumns = [];
    $result = $conn->query("DESCRIBE databasehc");
    
    while ($row = $result->fetch_assoc()) {
        if (strpos(strtolower($row['Type']), 'date') !== false) {
            $dateColumns[] = $row['Field'];
        }
    }
    
    echo "<p>تم العثور على " . count($dateColumns) . " عمود تاريخ: " . implode(', ', $dateColumns) . "</p>";
    
    // Step 2: Check for problematic values
    echo "<h2>الخطوة 2: فحص القيم المشكلة</h2>";
    
    $totalProblems = 0;
    $problemDetails = [];
    
    foreach ($dateColumns as $column) {
        echo "<h3>فحص عمود: $column</h3>";
        
        // Check for different types of problematic values
        $problems = [
            'empty_string' => "SELECT COUNT(*) as count FROM databasehc WHERE `$column` = ''",
            'zero_date' => "SELECT COUNT(*) as count FROM databasehc WHERE `$column` = '0000-00-00'",
            'invalid_date' => "SELECT COUNT(*) as count FROM databasehc WHERE `$column` IS NOT NULL AND `$column` != '' AND `$column` != '0000-00-00' AND STR_TO_DATE(`$column`, '%Y-%m-%d') IS NULL"
        ];
        
        $columnProblems = 0;
        foreach ($problems as $type => $sql) {
            try {
                $checkResult = $conn->query($sql);
                if ($checkResult) {
                    $row = $checkResult->fetch_assoc();
                    $count = $row['count'];
                    if ($count > 0) {
                        echo "<p style='color: orange;'>⚠ $type: $count قيمة</p>";
                        $columnProblems += $count;
                        $problemDetails[$column][$type] = $count;
                    }
                }
            } catch (Exception $e) {
                echo "<p style='color: red;'>خطأ في فحص $type: " . $e->getMessage() . "</p>";
            }
        }
        
        if ($columnProblems == 0) {
            echo "<p style='color: green;'>✅ لا توجد مشاكل في $column</p>";
        } else {
            $totalProblems += $columnProblems;
        }
    }
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h3>📊 ملخص المشاكل</h3>";
    echo "<p>إجمالي المشاكل المكتشفة: <strong>$totalProblems</strong></p>";
    echo "</div>";
    
    // Step 3: Fix the problems
    if ($totalProblems > 0) {
        echo "<h2>الخطوة 3: إصلاح المشاكل</h2>";
        
        $fixedCount = 0;
        
        foreach ($dateColumns as $column) {
            if (isset($problemDetails[$column])) {
                echo "<h3>إصلاح عمود: $column</h3>";
                
                try {
                    // Fix all problematic date values by setting them to NULL
                    $fixSql = "UPDATE databasehc SET `$column` = NULL WHERE `$column` = '' OR `$column` = '0000-00-00' OR (`$column` IS NOT NULL AND `$column` != '' AND STR_TO_DATE(`$column`, '%Y-%m-%d') IS NULL)";
                    
                    if ($conn->query($fixSql)) {
                        $affectedRows = $conn->affected_rows;
                        echo "<p style='color: green;'>✅ تم إصلاح $affectedRows قيمة في $column</p>";
                        $fixedCount += $affectedRows;
                    } else {
                        echo "<p style='color: red;'>❌ فشل إصلاح $column: " . $conn->error . "</p>";
                    }
                    
                } catch (Exception $e) {
                    echo "<p style='color: red;'>❌ خطأ في إصلاح $column: " . $e->getMessage() . "</p>";
                }
            }
        }
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "<h3>✅ تم الإصلاح</h3>";
        echo "<p>تم إصلاح <strong>$fixedCount</strong> قيمة مشكلة</p>";
        echo "</div>";
    }
    
    // Step 4: Verify the fix
    echo "<h2>الخطوة 4: التحقق من الإصلاح</h2>";
    
    $remainingProblems = 0;
    foreach ($dateColumns as $column) {
        $verifySql = "SELECT COUNT(*) as count FROM databasehc WHERE `$column` = '' OR `$column` = '0000-00-00'";
        $verifyResult = $conn->query($verifySql);
        
        if ($verifyResult) {
            $row = $verifyResult->fetch_assoc();
            $count = $row['count'];
            
            if ($count == 0) {
                echo "<p style='color: green;'>✅ $column نظيف</p>";
            } else {
                echo "<p style='color: red;'>❌ $column لا يزال يحتوي على $count مشكلة</p>";
                $remainingProblems += $count;
            }
        }
    }
    
    // Step 5: Test the problematic function
    echo "<h2>الخطوة 5: اختبار الدالة المشكلة</h2>";
    
    try {
        // Include the fixed function
        require_once 'Database.php';
        
        // Get available columns
        $tableStructure = $conn->query("DESCRIBE databasehc");
        $availableColumns = [];
        while ($row = $tableStructure->fetch_assoc()) {
            $availableColumns[] = $row['Field'];
        }
        
        // Test each date column
        foreach ($dateColumns as $column) {
            echo "<p>اختبار $column: ";
            try {
                $result = getFilterOptionsIfExists($conn, $column, $availableColumns);
                if ($result) {
                    $count = $result->num_rows;
                    echo "<span style='color: green;'>✅ نجح ($count قيمة)</span>";
                } else {
                    echo "<span style='color: orange;'>⚠ فارغ</span>";
                }
            } catch (Exception $e) {
                echo "<span style='color: red;'>❌ خطأ: " . $e->getMessage() . "</span>";
            }
            echo "</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>خطأ في الاختبار: " . $e->getMessage() . "</p>";
    }
    
    // Final summary
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: center;'>";
    echo "<h2>📋 الملخص النهائي</h2>";
    
    if ($remainingProblems == 0) {
        echo "<div style='color: #28a745; font-size: 24px; margin: 10px 0;'>🎉 تم الإصلاح بنجاح!</div>";
        echo "<p>جميع مشاكل التواريخ تم حلها. يمكنك الآن استخدام النظام بدون أخطاء.</p>";
        echo "<p><a href='Database.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>فتح قاعدة البيانات</a></p>";
    } else {
        echo "<div style='color: #dc3545; font-size: 24px; margin: 10px 0;'>⚠️ لا تزال هناك مشاكل</div>";
        echo "<p>لا تزال هناك $remainingProblems مشكلة. قد تحتاج لإصلاح يدوي.</p>";
    }
    echo "</div>";
    
    $conn->close();
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; color: #721c24;'>";
    echo "<h3>❌ خطأ عام</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
}

h1 {
    color: white;
    text-align: center;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

h2, h3 {
    color: #2c3e50;
}

div {
    margin: 10px 0;
}

p {
    margin: 8px 0;
}
</style>
