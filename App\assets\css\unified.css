@import url('root-variables.css');

/* ==================== ADMIN UNIFIED STYLES ==================== */
/* ملف CSS موحد لجميع صفحات الإدارة */

/* ==================== BASE STYLES ==================== */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', sans-serif;
}

body {
    background-color: var(--light-color);
    color: var(--dark-color);
    display: flex;
    min-height: 100vh;
    transition: var(--transition);
    font-size: 14px;
    line-height: 1.6;
}

/* ==================== COMMON COMPONENTS ==================== */
/* تم نقل تنسيقات الـ Sidebar إلى ملف منفصل: assets/css/sidebar.css */

/* Main Content - موحد لجميع الصفحات */
.main-content {
    flex: 1;
    margin-left: 250px;
    transition: var(--transition);
    padding: 20px;
    min-height: 100vh;
}

.sidebar.collapsed ~ .main-content {
    margin-left: 70px;
}

/* Page Header - موحد لجميع الصفحات */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    animation: fadeIn 0.5s ease;
}

/* Welcome Section - موحد لجميع الصفحات */
.welcome-section {
    display: flex;
    align-items: center;
    gap: 15px;
}

.welcome-message h1 {
    font-size: 24px;
    font-weight: 600;
    color: var(--primary-dark);
    margin: 0;
}

.welcome-message p {
    font-size: 14px;
    color: var(--gray-color);
    margin: 5px 0 0 0;
}

/* Profile Picture - موحد لجميع الصفحات */
.profile-pic {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--primary-light);
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.profile-pic:hover {
    transform: scale(1.05);
    border-color: var(--primary-color);
    box-shadow: 0 6px 20px rgba(208, 164, 208, 0.2);
}

.default-avatar {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(145deg, #f0f0f0, #ffffff);
    border-radius: 50%;
    color: var(--primary-dark);
    width: 50px;
    height: 50px;
    border: 3px solid var(--primary-light);
    overflow: hidden;
    position: relative;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.default-avatar i {
    font-size: 20px;
}

.default-avatar:hover {
    transform: scale(1.05);
    border-color: var(--primary-color);
    box-shadow: 0 6px 20px rgba(208, 164, 208, 0.2);
}

/* Common Button Styles - موحد لجميع الصفحات */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 500;
    transition: var(--transition);
    font-size: 12px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.btn-primary {
    background-color: var(--primary-dark);
    color: var(--white);
}

.btn-primary:hover {
    background-color: var(--primary-color);
    transform: translateY(-2px);
    color: var(--white);
    text-decoration: none;
}

.btn-secondary {
    background-color: var(--gray-color);
    color: var(--white);
}

.btn-secondary:hover {
    background-color: var(--dark-color);
    color: var(--white);
}

.btn-success {
    background-color: var(--success-color);
    color: var(--white);
}

.btn-danger {
    background-color: var(--danger-color);
    color: var(--white);
}

/* Common Form Elements - موحد لجميع الصفحات */
.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: var(--dark-color);
    font-size: 12px;
}

.form-control {
    width: 100%;
    padding: 8px 10px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 12px;
    transition: var(--transition);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(208, 164, 208, 0.1);
}

/* ==================== UNIFIED TABLE SYSTEM ==================== */
/* نظام جداول موحد لجميع صفحات الإدارة */

/* Base Table Container */
.table-container {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    margin-bottom: 30px;
    border: 1px solid var(--border-color);
}

.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

/* Unified Table Base Styles */
.unified-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 12px;
    margin-bottom: 0;
    background-color: var(--white);
    line-height: 1.4;
}

/* Table Header Unified */
.unified-table thead {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    color: var(--white);
}

.unified-table th {
    padding: 8px 10px;
    text-align: left;
    font-weight: 600;
    font-size: 11px;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    border-bottom: 1px solid var(--primary-light);
    position: sticky;
    top: 0;
    z-index: 10;
    transition: var(--transition);
    line-height: 1.3;
}

.unified-table th:hover {
    background-color: var(--primary-darker);
}

/* Table Body Unified */
.unified-table td {
    padding: 8px 10px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
    transition: var(--transition);
    font-size: 11px;
    line-height: 1.3;
}

.unified-table tbody tr:nth-child(even) {
    background-color: rgba(208, 164, 208, 0.03);
}

.unified-table tbody tr:hover {
    background-color: rgba(208, 164, 208, 0.06);
    transform: translateY(-0.5px);
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.08);
}

/* Table Icons in Headers */
.unified-table th i {
    margin-right: 8px;
    opacity: 0.9;
    font-size: 12px;
}

/* Compact Table Variant */
.unified-table.table-compact th,
.unified-table.table-compact td {
    padding: 8px 12px;
    font-size: 13px;
}

/* Small Table Variant */
.unified-table.table-sm th,
.unified-table.table-sm td {
    padding: 6px 10px;
    font-size: 12px;
}

/* Legacy Support - maintain backward compatibility */
table:not(.unified-table) {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

table:not(.unified-table) thead {
    background-color: var(--primary-dark);
    color: var(--white);
}

table:not(.unified-table) th,
table:not(.unified-table) td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

table:not(.unified-table) th {
    font-weight: 600;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

table:not(.unified-table) tbody tr:hover {
    background-color: var(--light-color);
}

/* ==================== TABLE COMPONENTS ==================== */
/* مكونات الجداول الموحدة */

/* Status Badges - موحد لجميع الصفحات */
.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-block;
    text-align: center;
    min-width: 70px;
    transition: var(--transition);
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-approved {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-rejected {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-active {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.status-inactive {
    background-color: #f8f9fa;
    color: #6c757d;
    border: 1px solid #dee2e6;
}

/* Table Action Buttons */
.table-actions {
    display: flex;
    gap: 5px;
    justify-content: center;
    align-items: center;
}

.btn-table-action {
    width: 26px;
    height: 26px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    cursor: pointer;
    transition: var(--transition);
    font-size: 10px;
    text-decoration: none;
    color: var(--white);
}

.btn-table-action:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.btn-table-action.btn-edit {
    background-color: #007bff;
}

.btn-table-action.btn-edit:hover {
    background-color: #0056b3;
}

.btn-table-action.btn-delete {
    background-color: var(--danger-color);
}

.btn-table-action.btn-delete:hover {
    background-color: #da190b;
}

.btn-table-action.btn-view {
    background-color: #28a745;
}

.btn-table-action.btn-view:hover {
    background-color: #1e7e34;
}

.btn-table-action.btn-archive {
    background-color: #6c757d;
}

.btn-table-action.btn-archive:hover {
    background-color: #545b62;
}

/* Profile Images in Tables */
.table-profile-img {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    object-fit: cover;
    border: 1px solid var(--primary-light);
    transition: var(--transition);
}

.table-profile-img:hover {
    transform: scale(1.05);
    border-color: var(--primary-color);
}

.table-profile-placeholder {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-light), var(--primary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 14px;
    font-weight: 600;
}

/* Table Input Fields */
.table-input {
    width: 100%;
    padding: 6px 10px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 12px;
    transition: var(--transition);
    background-color: var(--white);
}

.table-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(208, 164, 208, 0.2);
}

/* Table Filters */
.table-filters {
    background: var(--white);
    padding: 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-group label {
    font-size: 12px;
    font-weight: 600;
    color: var(--primary-dark);
    margin-bottom: 5px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.filter-group select,
.filter-group input {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 13px;
    transition: var(--transition);
}

.filter-group select:focus,
.filter-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(208, 164, 208, 0.2);
}

/* DataTables Integration */
.unified-table.dataTable {
    border-collapse: collapse !important;
}

.unified-table.dataTable thead th {
    border-bottom: 2px solid var(--primary-light) !important;
}

.unified-table.dataTable tbody td {
    border-top: none !important;
}

/* DataTables Wrapper Styling */
.dataTables_wrapper {
    padding: 0;
}

.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter {
    margin-bottom: 15px;
}

.dataTables_wrapper .dataTables_length select {
    padding: 5px 8px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    margin: 0 5px;
}

.dataTables_wrapper .dataTables_filter input {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    margin-left: 5px;
}

.dataTables_wrapper .dataTables_info {
    color: var(--gray-color);
    font-size: 13px;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    padding: 6px 12px;
    margin: 0 2px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: var(--white);
    color: var(--dark-color);
    text-decoration: none;
    transition: var(--transition);
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background: var(--primary-light);
    border-color: var(--primary-color);
    color: var(--primary-dark);
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
}

/* Responsive Table Adjustments */
@media (max-width: 768px) {
    .unified-table {
        font-size: 12px;
    }

    .unified-table th,
    .unified-table td {
        padding: 8px 6px;
    }

    .table-filters {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .table-actions {
        flex-direction: column;
        gap: 3px;
    }

    .btn-table-action {
        width: 24px;
        height: 24px;
        font-size: 9px;
    }
}



.status-approved {
    background-color: var(--success-light);
    color: var(--success-color);
}

.status-rejected {
    background-color: var(--danger-light);
    color: var(--danger-color);
}

/* Action Buttons - موحد لجميع الصفحات */
.action-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
}

/* Action Buttons Group - لتقريب الأزرار من بعض */
.action-buttons-group {
    display: flex;
    gap: 8px;
    align-items: center;
    flex-wrap: wrap;
}

.action-buttons-group .btn {
    margin-right: 0;
    margin-bottom: 0;
}

.action-buttons-group form {
    margin: 0;
}

.btn-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    cursor: pointer;
    transition: var(--transition);
    font-size: 12px;
}

.btn-approved {
    background-color: var(--success-color);
    color: var(--white);
}

.btn-approved:hover {
    background-color: #45a049;
    transform: scale(1.1);
}

.btn-rejected {
    background-color: var(--danger-color);
    color: var(--white);
}

.btn-rejected:hover {
    background-color: #da190b;
    transform: scale(1.1);
}

/* Cards - موحد لجميع الصفحات */
.data-card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 30px;
    overflow: hidden;
}

.card-header {
    background-color: var(--primary-light);
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
}

.card-header h5 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--primary-dark);
}

.card-body {
    padding: 20px;
}

/* Mobile Menu Button - تم نقله إلى sidebar.css */

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideUp {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

/* ==================== RESPONSIVE DESIGN ==================== */

@media (max-width: 992px) {
    .main-content {
        margin-left: 0;
    }

    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .welcome-section {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}

@media (max-width: 768px) {
    .main-content {
        padding: 15px;
    }

    .welcome-message h1 {
        font-size: 20px;
    }

    .welcome-message p {
        font-size: 13px;
    }

    .profile-pic,
    .default-avatar {
        width: 40px;
        height: 40px;
    }

    .default-avatar i {
        font-size: 16px;
    }

    table th,
    table td {
        padding: 6px;
        font-size: 11px;
    }

    .btn {
        padding: 6px 12px;
        font-size: 11px;
    }

    .action-buttons {
        flex-direction: column;
        gap: 4px;
    }
}

@media (max-width: 480px) {
    .main-content {
        padding: 10px;
    }

    .welcome-message h1 {
        font-size: 18px;
    }

    .profile-pic,
    .default-avatar {
        width: 35px;
        height: 35px;
    }

    .default-avatar i {
        font-size: 14px;
    }

    /* Sidebar styles moved to sidebar.css */
}

/* ==================== SPECIFIC PAGE STYLES ==================== */

/* Admin Home Page Specific */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    animation: slideUp 0.5s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 100%;
    background: var(--primary-color);
}

.stat-card h3 {
    font-size: 14px;
    color: var(--gray-color);
    margin-bottom: 10px;
    font-weight: 500;
}

.stat-card p {
    font-size: 28px;
    font-weight: 600;
    color: var(--primary-dark);
    margin: 0;
}

/* Profile Page Specific */
.profile-container {
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: 20px;
    max-width: 1200px;
}

.profile-card {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--shadow);
    text-align: center;
    height: fit-content;
}

/* Profile Overview Section */
.profile-overview {
    padding: 0;
}

.profile-overview .welcome-section {
    background: linear-gradient(135deg, var(--primary-light), var(--primary-lighter));
    border-radius: var(--border-radius);
    padding: 18px;
    margin-bottom: 20px;
    text-align: center;
    border-left: 4px solid var(--primary-color);
}

.profile-overview .welcome-title {
    font-size: 22px;
    font-weight: 700;
    color: var(--primary-dark);
    margin-bottom: 8px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.profile-overview .welcome-message {
    font-size: 14px;
    color: var(--gray-color);
    margin: 0;
    font-weight: 500;
}

/* Profile Details Grid */
.profile-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 15px;
    margin-top: 0;
}

.detail-card {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 15px;
    box-shadow: var(--shadow);
    border-left: 3px solid var(--primary-color);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.detail-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, var(--primary-light), transparent);
    border-radius: 0 0 0 45px;
    opacity: 0.3;
}

.detail-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.12);
    border-left-color: var(--primary-dark);
}

.detail-card-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.detail-card-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    font-size: 14px;
    box-shadow: 0 3px 8px rgba(208, 164, 208, 0.3);
}

.detail-label {
    font-weight: 600;
    color: var(--primary-dark);
    font-size: 12px;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 0.4px;
}

.detail-value {
    font-size: 14px;
    font-weight: 500;
    color: var(--dark-color);
    margin: 0;
    word-break: break-word;
    line-height: 1.3;
}

/* Access Badge Styles */
.access-badge {
    display: inline-block;
    padding: 4px 10px;
    border-radius: 16px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.access-admin {
    background: linear-gradient(135deg, var(--danger-color), #e53e3e);
    color: var(--white);
}

.access-leader {
    background: linear-gradient(135deg, var(--warning-color), #f6ad55);
    color: var(--dark-color);
}

.access-member {
    background: linear-gradient(135deg, var(--success-color), #68d391);
    color: var(--white);
}

/* Employee Code Styles */
.employee-code {
    background: var(--light-color);
    padding: 8px 12px;
    border-radius: 6px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    font-weight: 600;
    color: var(--primary-dark);
    border: 2px solid var(--primary-light);
    letter-spacing: 1px;
}

/* Chart Styles */
.chart-container {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--shadow);
    margin-bottom: 20px;
}

.chart-wrapper {
    min-height: 350px;
    position: relative;
}

.chart-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 350px;
    color: var(--gray-color);
    font-family: 'Poppins', sans-serif;
}

.chart-loading i {
    font-size: 32px;
    margin-bottom: 15px;
    color: var(--primary-color);
}

.chart-loading p {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
}

.section-description {
    color: var(--gray-color);
    font-size: 14px;
    margin: 5px 0 0 0;
    font-weight: 400;
}

.profile-overview .welcome-section {
    background: linear-gradient(135deg, var(--primary-light), var(--primary-lighter));
    border-radius: var(--border-radius);
    padding: 25px;
    margin-bottom: 30px;
    text-align: center;
    border-left: 5px solid var(--primary-color);
}



.detail-card {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--shadow);
    border-left: 4px solid var(--primary-color);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.detail-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-light), transparent);
    border-radius: 0 0 0 60px;
    opacity: 0.3;
}

.detail-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-left-color: var(--primary-dark);
}

.detail-card-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.detail-card-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 16px;
    box-shadow: 0 4px 10px rgba(208, 164, 208, 0.3);
}

.detail-label {
    font-weight: 600;
    color: var(--primary-dark);
    font-size: 14px;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.detail-value {
    font-size: 16px;
    font-weight: 500;
    color: var(--dark-color);
    margin: 0;
    word-break: break-word;
    line-height: 1.4;
}

.profile-img {
    width: 90px;
    height: 90px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--primary-light);
    margin-bottom: 15px;
}

.profile-name {
    font-size: 16px;
    font-weight: 600;
    color: var(--primary-dark);
    margin-bottom: 4px;
}

.profile-role {
    color: var(--gray-color);
    font-size: 12px;
    margin-bottom: 15px;
}

.social-links {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-bottom: 15px;
}

.social-links a {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--primary-light);
    color: var(--primary-dark);
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: var(--transition);
    font-size: 14px;
}

.social-links a:hover {
    background-color: var(--primary-color);
    color: var(--white);
    transform: translateY(-1px);
}

.profile-info {
    margin-top: 20px;
}

.info-item {
    display: flex;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid var(--border-color);
}

.info-item:last-child {
    border-bottom: none;
}

.info-item i {
    width: 16px;
    color: var(--primary-color);
    margin-right: 12px;
    font-size: 12px;
}

.info-content {
    flex: 1;
}

.info-label {
    font-size: 10px;
    color: var(--gray-color);
    text-transform: uppercase;
    letter-spacing: 0.4px;
}

.info-value {
    font-size: 12px;
    color: var(--dark-color);
    font-weight: 500;
}

.profile-content {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.profile-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
}

.tab-btn {
    flex: 1;
    padding: 12px 16px;
    background: transparent;
    border: none;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    color: var(--gray-color);
    transition: var(--transition);
}

.tab-btn.active,
.tab-btn:hover {
    background-color: var(--primary-light);
    color: var(--primary-dark);
}

.tab-content {
    display: none;
    padding: 18px;
}

.tab-content.active {
    display: block;
}

.tab-content h3 {
    color: var(--primary-dark);
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 600;
}

/* Employee Code Styles */
.employee-code {
    background-color: var(--primary-light);
    color: var(--primary-dark);
    padding: 4px 8px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    font-weight: 600;
    letter-spacing: 1px;
}

/* Filters Container */
.filters-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    padding: 20px;
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 20px;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-group label {
    font-size: 12px;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 5px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.filter-group select,
.filter-group input {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 14px;
    transition: var(--transition);
}

.filter-group select:focus,
.filter-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(208, 164, 208, 0.1);
}

/* Export Form */
.export-form {
    display: flex;
    align-items: end;
    gap: 15px;
    padding: 20px;
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 20px;
}

.export-form label {
    font-size: 12px;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 5px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.export-form input {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 14px;
}

.export-btn {
    background-color: var(--success-color);
    color: var(--white);
    border: none;
    padding: 10px 20px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
}

.export-btn:hover {
    background-color: #45a049;
    transform: translateY(-2px);
}

/* Create Leaves Button */
.create-leaves-btn {
    background-color: var(--primary-color);
    color: var(--white);
    padding: 12px 24px;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: var(--transition);
}

.create-leaves-btn:hover {
    background-color: var(--primary-dark);
    color: var(--white);
    text-decoration: none;
    transform: translateY(-2px);
}

/* No Data Message */
.no-data-message {
    text-align: center;
    padding: 40px 20px;
    color: var(--gray-color);
    font-size: 16px;
}

.no-data-message i {
    font-size: 48px;
    margin-bottom: 15px;
    color: var(--primary-light);
}

/* Responsive adjustments for specific components */
@media (max-width: 992px) {
    .profile-container {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .stats-container {
        grid-template-columns: repeat(2, 1fr);
    }

    .filters-container {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }

    .export-form {
        flex-direction: column;
        align-items: stretch;
    }

    /* Profile Overview Responsive */
    .profile-details {
        grid-template-columns: 1fr;
    }

    .profile-overview .welcome-title {
        font-size: 20px;
    }
}

@media (max-width: 768px) {
    .stats-container {
        grid-template-columns: 1fr;
    }

    .profile-tabs {
        flex-direction: column;
    }

    .tab-btn {
        text-align: left;
    }

    .filters-container {
        grid-template-columns: 1fr;
    }

    .action-buttons {
        justify-content: center;
    }

    .action-buttons-group {
        flex-direction: column;
        gap: 10px;
        width: 100%;
    }

    .action-buttons-group .btn {
        width: 100%;
        justify-content: center;
    }

    /* Profile Overview Mobile */
    .profile-overview .welcome-section {
        padding: 20px;
        margin-bottom: 20px;
    }

    .profile-overview .welcome-title {
        font-size: 18px;
    }

    .profile-overview .welcome-message {
        font-size: 12px;
    }

    .detail-card {
        padding: 15px;
    }

    .detail-card-icon {
        width: 28px;
        height: 28px;
        font-size: 12px;
        margin-right: 10px;
    }

    .detail-label {
        font-size: 11px;
    }

    .detail-value {
        font-size: 13px;
    }
}
