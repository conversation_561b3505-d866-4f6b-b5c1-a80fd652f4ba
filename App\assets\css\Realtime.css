  @import url('root-variables.css');
       
        .rtd-body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .rtd-navbar {
            background-color: var(--rtd-main-color);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .rtd-navbar-brand {
            font-weight: bold;
            display: flex;
            align-items: center;
        }
        
        .rtd-navbar-brand img {
            height: 30px;
            margin-right: 10px;
        }
        
        .rtd-status-counters {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .rtd-counter-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 10px;
        }
        
        .rtd-counter-item {
            padding: 8px;
            border-radius: 5px;
            background-color: #f8f9fa;
            text-align: center;
            font-size: 0.85rem;
        }
        
        .rtd-counter-value {
            font-weight: bold;
            color: var(--rtd-main-color);
            font-size: 1.1rem;
        }
        
        .rtd-main-container {
            margin-top: 20px;
        }
        
        .rtd-card {
            border-radius: 8px;
            border: none;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .rtd-card-header {
            background-color: var(--rtd-main-color);
            color: white;
            border-radius: 8px 8px 0 0 !important;
            font-weight: bold;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 20px;
        }
        
        .rtd-btn-main {
            background-color: var(--rtd-main-color);
            border-color: var(--rtd-main-color);
            color: white;
            font-size: 0.85rem;
            padding: 5px 10px;
        }
        
        .rtd-btn-main:hover {
            background-color: #c094c0;
            border-color: #c094c0;
            color: white;
        }
        
        .rtd-table th {
            background-color: var(--rtd-main-color);
            color: white;
            text-align: center;
            vertical-align: middle;
            font-size: 0.9rem;
            padding: 8px 12px;
        }
        
        .rtd-table td {
            text-align: center;
            vertical-align: middle;
            font-size: 0.85rem;
            padding: 8px 12px;
        }
        
        .rtd-table-responsive {
            border-radius: 8px;
            overflow: hidden;
        }
        
        .rtd-filter-export-container {
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            margin-bottom: 15px;
        }
        
        .rtd-filter-section {
            flex-grow: 1;
            background-color: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-right: 15px;
        }
        
        .rtd-export-container {
            margin-bottom: 15px;
        }
        
        .rtd-form-control, .rtd-form-select {
            font-size: 0.85rem;
            padding: 6px 12px;
        }
        
        /* Status Time Column Specific Styles */
        .rtd-status-time-cell {
            font-weight: bold;
        }
        
        .rtd-highlight-online {
            color: #c93f30;
            background-color: #fbc3cb;
            border-radius: 4px;
            padding: 2px 5px;
            font-weight: bold;
        }
        
        .rtd-highlight-break {
            color: black;
            background-color: yellow;
            border-radius: 4px;
            padding: 2px 5px;
            font-weight: bold;
        }
        
        .rtd-highlight-technical {
            color: white;
            background-color: green;
            border-radius: 4px;
            padding: 2px 5px;
            font-weight: bold;
        }
        
        .rtd-badge-status {
            font-size: 0.75rem;
            padding: 3px 6px;
            border-radius: 4px;
        }
        
        .rtd-nav-buttons {
            display: flex;
            gap: 10px;
        }
        
        .rtd-status-legend {
            background-color: white;
            border-radius: 8px;
            padding: 10px;
            margin-top: 10px;
            font-size: 0.8rem;
        }
        
        .rtd-legend-item {
            display: inline-block;
            margin-right: 15px;
        }
        
        .rtd-legend-color {
            display: inline-block;
            width: 15px;
            height: 15px;
            border-radius: 3px;
            margin-right: 5px;
            vertical-align: middle;
        }

        .rtd-highlight-online {
            background-color: #fbc3cb !important;
            color: #c93f30 !important;
            font-weight: bold;
            border-radius: 4px;
            padding: 2px 5px;
        }

        .rtd-highlight-break {
            background-color: yellow !important;
            color: black !important;
            font-weight: bold;
            border-radius: 4px;
            padding: 2px 5px;
        }

        .rtd-highlight-technical {
            background-color: green !important;
            color: white !important;
            font-weight: bold;
            border-radius: 4px;
            padding: 2px 5px;
        }

        .rtd-status-time-content {
            display: inline-block;
            padding: 2px 5px;
        }

        /* تحسينات للجوال */
        @media (max-width: 768px) {
            .rtd-filter-section .row {
                flex-direction: column;
            }
            
            .rtd-filter-section .col-md-2,
            .rtd-filter-section .col-md-4 {
                width: 100%;
                margin-bottom: 10px;
            }
            
            .rtd-table-responsive {
                border: 1px solid #ddd;
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
            }
            
            .rtd-table {
                font-size: 0.75rem;
            }
            
            .rtd-table th, 
            .rtd-table td {
                padding: 4px 8px;
                white-space: nowrap;
            }
            
            .rtd-counter-grid {
                grid-template-columns: repeat(3, 1fr);
            }
            
            .rtd-navbar-brand {
                font-size: 0.9rem;
            }
            
            .rtd-btn-main {
                font-size: 0.7rem;
                padding: 3px 6px;
            }
            
            .rtd-card-header {
                padding: 8px 12px;
                font-size: 0.9rem;
            }
            
            .rtd-status-legend {
                font-size: 0.7rem;
            }
            
            .rtd-legend-item {
                margin-right: 8px;
                margin-bottom: 5px;
            }
        }

        @media (max-width: 576px) {
            .rtd-counter-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .rtd-table {
                font-size: 0.65rem;
            }
            
            .rtd-badge-status {
                font-size: 0.6rem;
                padding: 2px 4px;
            }
            
            .rtd-status-time-content {
                padding: 1px 3px;
            }
        }

        .rtd-table-hover tbody tr {
            padding: 10px 0;
        }

        .rtd-table-hover tbody tr:hover {
            background-color: rgba(208, 164, 208, 0.1);
        }