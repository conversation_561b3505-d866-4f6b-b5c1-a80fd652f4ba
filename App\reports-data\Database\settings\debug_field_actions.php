<?php
/**
 * Debug Field Actions - Test the auto-map and clean functions
 */

session_start();
include $_SERVER['DOCUMENT_ROOT'].'/config/Databasehost.php';
include '../system/database_config.php';
require_once __DIR__ . '/field_manager.php';

echo "<h1>🔧 Debug Field Actions</h1>";

try {
    // Initialize field manager
    $fieldManager = new FieldManager($conn);
    
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h2>✅ Field Manager Initialized Successfully</h2>";
    echo "</div>";
    
    // Test 1: Get Database Columns
    echo "<h2>📊 Test 1: Database Columns</h2>";
    $dbColumns = $fieldManager->getDatabaseColumns();
    echo "<p>Found <strong>" . count($dbColumns) . "</strong> database columns:</p>";
    echo "<details><summary>Show columns</summary><ul>";
    foreach ($dbColumns as $name => $info) {
        echo "<li><strong>$name</strong> - {$info['type']}</li>";
    }
    echo "</ul></details>";
    
    // Test 2: Get Current Configuration
    echo "<h2>⚙️ Test 2: Current Configuration</h2>";
    $config = $fieldManager->loadConfiguration();
    echo "<p>Configuration loaded with <strong>" . count($config) . "</strong> sections:</p>";
    echo "<details><summary>Show sections</summary><ul>";
    foreach ($config as $sectionName => $section) {
        $fieldCount = isset($section['fields']) ? count($section['fields']) : 0;
        echo "<li><strong>$sectionName</strong> - $fieldCount fields</li>";
    }
    echo "</ul></details>";
    
    // Test 3: Get Unmapped Fields
    echo "<h2>🔍 Test 3: Unmapped Fields</h2>";
    $unmappedFields = $fieldManager->getUnmappedFields();
    echo "<p>Found <strong>" . count($unmappedFields) . "</strong> unmapped fields:</p>";
    if (!empty($unmappedFields)) {
        echo "<ul>";
        foreach ($unmappedFields as $name => $info) {
            echo "<li><strong>$name</strong> - {$info['type']}</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: green;'>✅ All fields are mapped!</p>";
    }
    
    // Test 4: Get Orphaned Fields
    echo "<h2>🗑️ Test 4: Orphaned Fields</h2>";
    $orphanedFields = $fieldManager->getOrphanedFields();
    echo "<p>Found <strong>" . count($orphanedFields) . "</strong> orphaned fields:</p>";
    if (!empty($orphanedFields)) {
        echo "<ul>";
        foreach ($orphanedFields as $orphan) {
            echo "<li><strong>{$orphan['field']}</strong> in section '{$orphan['section']}'</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: green;'>✅ No orphaned fields!</p>";
    }
    
    // Test 5: Test Auto-Map Function
    echo "<h2>🪄 Test 5: Auto-Map Function</h2>";
    if (!empty($unmappedFields)) {
        echo "<p>Testing auto-map function...</p>";
        
        // Create backup of current config
        $backupConfig = $config;
        
        try {
            $mapped = $fieldManager->autoMapFields();
            echo "<p style='color: green;'>✅ Auto-mapped <strong>$mapped</strong> fields successfully!</p>";
            
            // Show what was mapped
            $newConfig = $fieldManager->loadConfiguration();
            echo "<h3>Newly mapped fields:</h3>";
            echo "<ul>";
            foreach ($newConfig as $sectionName => $section) {
                if (isset($section['fields'])) {
                    foreach ($section['fields'] as $fieldName => $fieldConfig) {
                        if (!isset($backupConfig[$sectionName]['fields'][$fieldName])) {
                            echo "<li><strong>$fieldName</strong> → $sectionName section</li>";
                        }
                    }
                }
            }
            echo "</ul>";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Error in auto-map: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p style='color: blue;'>ℹ️ No unmapped fields to test with</p>";
    }
    
    // Test 6: Test Clean Function
    echo "<h2>🧹 Test 6: Clean Orphaned Function</h2>";
    if (!empty($orphanedFields)) {
        echo "<p>Testing clean orphaned function...</p>";
        
        try {
            $cleaned = $fieldManager->cleanOrphanedFields();
            echo "<p style='color: green;'>✅ Cleaned <strong>$cleaned</strong> orphaned fields successfully!</p>";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Error in clean: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p style='color: blue;'>ℹ️ No orphaned fields to test with</p>";
    }
    
    // Test 7: Final Statistics
    echo "<h2>📈 Test 7: Final Statistics</h2>";
    $stats = $fieldManager->getFieldStats();
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;'>";
    
    $statLabels = [
        'total_db_columns' => 'DB Columns',
        'configured_fields' => 'Configured Fields',
        'enabled_fields' => 'Enabled Fields',
        'sections' => 'Sections',
        'unmapped_fields' => 'Unmapped Fields',
        'orphaned_fields' => 'Orphaned Fields'
    ];
    
    foreach ($statLabels as $key => $label) {
        $value = $stats[$key];
        $color = ($key === 'unmapped_fields' || $key === 'orphaned_fields') && $value > 0 ? '#dc3545' : '#28a745';
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center; border-left: 4px solid $color;'>";
        echo "<h3 style='margin: 0; color: $color;'>$value</h3>";
        echo "<p style='margin: 5px 0 0 0; color: #6c757d;'>$label</p>";
        echo "</div>";
    }
    echo "</div>";
    
    // Test 8: Manual Action Buttons
    echo "<h2>🎮 Test 8: Manual Action Buttons</h2>";
    echo "<div style='margin: 20px 0;'>";
    
    if ($stats['unmapped_fields'] > 0) {
        echo "<form method='POST' style='display: inline-block; margin-right: 10px;'>";
        echo "<input type='hidden' name='action' value='auto_map'>";
        echo "<button type='submit' style='background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;'>";
        echo "🪄 Auto-Map {$stats['unmapped_fields']} Fields";
        echo "</button>";
        echo "</form>";
    }
    
    if ($stats['orphaned_fields'] > 0) {
        echo "<form method='POST' style='display: inline-block;' onsubmit='return confirm(\"Are you sure?\");'>";
        echo "<input type='hidden' name='action' value='clean_orphaned'>";
        echo "<button type='submit' style='background: #ffc107; color: black; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;'>";
        echo "🧹 Clean {$stats['orphaned_fields']} Orphaned Fields";
        echo "</button>";
        echo "</form>";
    }
    
    if ($stats['unmapped_fields'] === 0 && $stats['orphaned_fields'] === 0) {
        echo "<p style='color: green; font-size: 18px;'>🎉 All fields are properly configured!</p>";
    }
    
    echo "</div>";
    
    // Handle POST actions
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h3>🔄 Processing Action: " . htmlspecialchars($_POST['action']) . "</h3>";
        
        switch ($_POST['action']) {
            case 'auto_map':
                try {
                    $mapped = $fieldManager->autoMapFields();
                    echo "<p style='color: green;'>✅ Successfully auto-mapped $mapped fields!</p>";
                    echo "<script>setTimeout(() => location.reload(), 2000);</script>";
                } catch (Exception $e) {
                    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
                }
                break;
                
            case 'clean_orphaned':
                try {
                    $cleaned = $fieldManager->cleanOrphanedFields();
                    echo "<p style='color: green;'>✅ Successfully cleaned $cleaned orphaned fields!</p>";
                    echo "<script>setTimeout(() => location.reload(), 2000);</script>";
                } catch (Exception $e) {
                    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
                }
                break;
        }
        echo "</div>";
    }
    
    echo "<div style='margin: 30px 0; text-align: center;'>";
    echo "<a href='field_diagnostics.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>";
    echo "🔙 Back to Field Diagnostics";
    echo "</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; color: #721c24;'>";
    echo "<h3>❌ Error</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
    echo "</div>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background: #f8f9fa;
}

h1, h2, h3 {
    color: #2c3e50;
}

details {
    margin: 10px 0;
    padding: 10px;
    background: white;
    border-radius: 5px;
    border: 1px solid #dee2e6;
}

summary {
    cursor: pointer;
    font-weight: bold;
    color: #007bff;
}

ul {
    margin: 10px 0;
}

li {
    margin: 5px 0;
}
</style>
