<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Export Button Example</title>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    
    <!-- Bootstrap (Optional) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Unified Tables CSS -->
    <link rel="stylesheet" href="../css/root-variables.css">
    <link rel="stylesheet" href="../css/unified-tables.css">
    
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f8f9fa;
            padding: 20px;
            margin: 0;
        }
        .example-section {
            margin-bottom: 40px;
        }
        .example-title {
            color: #6c5ce7;
            margin-bottom: 20px;
            border-bottom: 2px solid #6c5ce7;
            padding-bottom: 10px;
        }
        .demo-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #2196f3;
            margin: 15px 0;
        }
        .demo-info h6 {
            color: #1976d2;
            margin-bottom: 10px;
        }
        .code-example {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #6c5ce7;
            margin: 15px 0;
        }
        .code-example pre {
            margin: 0;
            font-size: 13px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <h1 class="text-center mb-5">📤 تحسين زر Export Users</h1>
        
        <!-- Problem Description -->
        <div class="example-section">
            <h2 class="example-title">🔍 المشاكل التي تم حلها</h2>
            
            <div class="demo-info">
                <h6>المشاكل الأصلية:</h6>
                <ul>
                    <li>زر Export لا يعمل بشكل صحيح</li>
                    <li>تصميم الزر لا يتناسب مع باقي الصفحة</li>
                    <li>لا يوجد feedback للمستخدم أثناء التصدير</li>
                    <li>مشاكل في تنسيق ملف CSV المُصدر</li>
                    <li>عدم وجود تأكيد قبل التصدير</li>
                </ul>
            </div>
            
            <div class="demo-info">
                <h6>الحلول المطبقة:</h6>
                <ul>
                    <li>إصلاح كود PHP للتصدير</li>
                    <li>تحسين تصميم الزر ليتناسب مع الصفحة</li>
                    <li>إضافة loading state أثناء التصدير</li>
                    <li>تحسين تنظيف البيانات في CSV</li>
                    <li>إضافة تأكيد وأمان إضافي</li>
                </ul>
            </div>
        </div>

        <!-- New Export Button Demo -->
        <div class="example-section">
            <h2 class="example-title">✨ الزر المحسن الجديد</h2>
            
            <div class="demo-info">
                <h6>جرب الزر الجديد:</h6>
                <p>لاحظ التصميم المحسن والتأثيرات البصرية الجذابة.</p>
            </div>
            
            <div class="action-buttons">
                <a href="#" class="btn btn-primary">
                    <i class="fas fa-user-plus"></i> Add User
                </a>
                <a href="#" class="btn btn-primary">
                    <i class="fas fa-calendar-plus"></i> Add Schedule
                </a>
                <form method="POST" action="#" class="export-form" style="display: inline-block;">
                    <button type="button" class="btn btn-success export-btn" onclick="demoExport()">
                        <i class="fas fa-file-export"></i> Export Users
                    </button>
                </form>
            </div>
        </div>

        <!-- Features Showcase -->
        <div class="example-section">
            <h2 class="example-title">🎨 مميزات التصميم الجديد</h2>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="data-card">
                        <div class="card-header">
                            <h5><i class="fas fa-palette me-2"></i> Visual Features</h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    <strong>Gradient Background:</strong> تدرج لوني أخضر جذاب
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    <strong>Hover Effects:</strong> تأثيرات تفاعلية عند التمرير
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    <strong>Shine Animation:</strong> تأثير لمعان يمر عبر الزر
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    <strong>Loading State:</strong> حالة تحميل مع spinner
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    <strong>Icon Integration:</strong> أيقونة مناسبة للتصدير
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="data-card">
                        <div class="card-header">
                            <h5><i class="fas fa-cogs me-2"></i> Technical Features</h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-check text-primary me-2"></i>
                                    <strong>CSRF Protection:</strong> حماية من هجمات CSRF
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-primary me-2"></i>
                                    <strong>Data Cleaning:</strong> تنظيف البيانات قبل التصدير
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-primary me-2"></i>
                                    <strong>UTF-8 Support:</strong> دعم كامل للنصوص العربية
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-primary me-2"></i>
                                    <strong>Error Handling:</strong> معالجة محسنة للأخطاء
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-primary me-2"></i>
                                    <strong>Security Logging:</strong> تسجيل العمليات الأمنية
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Button States Demo -->
        <div class="example-section">
            <h2 class="example-title">🔄 حالات الزر المختلفة</h2>
            
            <div class="row">
                <div class="col-md-3">
                    <div class="text-center mb-3">
                        <h6>Normal State</h6>
                        <button class="btn btn-success export-btn">
                            <i class="fas fa-file-export"></i> Export Users
                        </button>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="text-center mb-3">
                        <h6>Loading State</h6>
                        <button class="btn btn-success export-btn" disabled>
                            <i class="fas fa-spinner fa-spin"></i> Exporting...
                        </button>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="text-center mb-3">
                        <h6>Disabled State</h6>
                        <button class="btn btn-success export-btn" disabled>
                            <i class="fas fa-file-export"></i> Export Users
                        </button>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="text-center mb-3">
                        <h6>Mobile View</h6>
                        <button class="btn btn-success export-btn" style="width: 100%; font-size: 13px;">
                            <i class="fas fa-file-export"></i> Export Users
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Code Implementation -->
        <div class="example-section">
            <h2 class="example-title">💻 التطبيق التقني</h2>
            
            <div class="data-card">
                <div class="card-header">
                    <h5><i class="fas fa-code me-2"></i> HTML Structure</h5>
                </div>
                <div class="card-body">
                    <div class="code-example">
                        <strong>HTML:</strong>
                        <pre><code>&lt;form method="POST" action="" class="export-form" style="display: inline-block;"&gt;
    &lt;input type="hidden" name="csrf_token" value="&lt;?php echo htmlspecialchars($_SESSION['csrf_token']); ?&gt;"&gt;
    &lt;button type="submit" name="export" class="btn btn-success export-btn" onclick="return confirmExport()"&gt;
        &lt;i class="fas fa-file-export"&gt;&lt;/i&gt; Export Users
    &lt;/button&gt;
&lt;/form&gt;</code></pre>
                    </div>
                    
                    <div class="code-example">
                        <strong>JavaScript:</strong>
                        <pre><code>function confirmExport() {
    const exportBtn = document.querySelector('.export-btn');
    const originalText = exportBtn.innerHTML;
    
    if (confirm('Are you sure you want to export user data?')) {
        exportBtn.innerHTML = '&lt;i class="fas fa-spinner fa-spin"&gt;&lt;/i&gt; Exporting...';
        exportBtn.disabled = true;
        
        setTimeout(() => {
            exportBtn.innerHTML = originalText;
            exportBtn.disabled = false;
        }, 5000);
        
        return true;
    }
    return false;
}</code></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- CSS Styles -->
        <div class="example-section">
            <h2 class="example-title">🎨 CSS Styles</h2>
            
            <div class="data-card">
                <div class="card-header">
                    <h5><i class="fas fa-paint-brush me-2"></i> Key CSS Rules</h5>
                </div>
                <div class="card-body">
                    <div class="code-example">
                        <strong>Button Base Styles:</strong>
                        <pre><code>.export-btn {
    background: linear-gradient(135deg, #28a745, #20c997);
    border: none;
    color: var(--white);
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.2);
}</code></pre>
                    </div>
                    
                    <div class="code-example">
                        <strong>Hover Effects:</strong>
                        <pre><code>.export-btn:hover {
    background: linear-gradient(135deg, #218838, #1ea085);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.export-btn::before {
    content: '';
    position: absolute;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}</code></pre>
                    </div>
                    
                    <div class="code-example">
                        <strong>Loading Animation:</strong>
                        <pre><code>.export-btn .fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}</code></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- PHP Export Code -->
        <div class="example-section">
            <h2 class="example-title">🐘 PHP Export Code</h2>
            
            <div class="data-card">
                <div class="card-header">
                    <h5><i class="fas fa-server me-2"></i> Backend Implementation</h5>
                </div>
                <div class="card-body">
                    <div class="code-example">
                        <strong>Security & Headers:</strong>
                        <pre><code>// CSRF Protection
if (!hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
    throw new Exception("Invalid security token");
}

// Set secure headers
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename="users_export_' . date('Y-m-d_H-i-s') . '.csv"');
header('Cache-Control: no-cache, no-store, must-revalidate');</code></pre>
                    </div>
                    
                    <div class="code-example">
                        <strong>Data Cleaning:</strong>
                        <pre><code>while ($row = $export_result->fetch_assoc()) {
    $cleanRow = [];
    foreach ($row as $key => $value) {
        // Clean data from harmful content
        $cleanValue = htmlspecialchars($value ?? '', ENT_QUOTES, 'UTF-8');
        // Remove control characters
        $cleanValue = preg_replace('/[\x00-\x1F\x7F]/', '', $cleanValue);
        $cleanRow[$key] = $cleanValue;
    }
    
    fputcsv($output, $cleanRow);
    $recordCount++;
}</code></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Testing Instructions -->
        <div class="example-section">
            <h2 class="example-title">🧪 تعليمات الاختبار</h2>
            
            <div class="demo-info">
                <h6>للتأكد من عمل الزر بشكل صحيح:</h6>
                <ol>
                    <li><strong>اختبر التصميم:</strong> تأكد من ظهور الزر بالتصميم الجديد</li>
                    <li><strong>اختبر التفاعل:</strong> مرر الماوس فوق الزر ولاحظ التأثيرات</li>
                    <li><strong>اختبر التأكيد:</strong> اضغط على الزر وتأكد من ظهور رسالة التأكيد</li>
                    <li><strong>اختبر التصدير:</strong> أكد التصدير وتأكد من تحميل ملف CSV</li>
                    <li><strong>اختبر المحتوى:</strong> افتح ملف CSV وتأكد من صحة البيانات</li>
                    <li><strong>اختبر الأمان:</strong> تأكد من عدم إمكانية التصدير بدون صلاحيات</li>
                </ol>
            </div>
        </div>

        <!-- Before vs After -->
        <div class="example-section">
            <h2 class="example-title">📊 مقارنة قبل وبعد</h2>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="data-card">
                        <div class="card-header" style="background: linear-gradient(135deg, #f44336, #d32f2f);">
                            <h5><i class="fas fa-times me-2"></i> قبل التحسين</h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-times text-danger me-2"></i>
                                    زر بتصميم عادي وغير جذاب
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-times text-danger me-2"></i>
                                    لا يعمل بشكل صحيح
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-times text-danger me-2"></i>
                                    لا يوجد feedback للمستخدم
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-times text-danger me-2"></i>
                                    مشاكل في ملف CSV
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-times text-danger me-2"></i>
                                    عدم تناسق مع باقي الصفحة
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="data-card">
                        <div class="card-header" style="background: linear-gradient(135deg, #4caf50, #388e3c);">
                            <h5><i class="fas fa-check me-2"></i> بعد التحسين</h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    تصميم عصري وجذاب
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    يعمل بشكل مثالي
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    loading state وتأكيد
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    ملف CSV نظيف ومنسق
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    متناسق مع تصميم الصفحة
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function demoExport() {
            const exportBtn = event.target.closest('.export-btn');
            const originalText = exportBtn.innerHTML;
            
            if (confirm('This is a demo. Are you sure you want to simulate export?')) {
                exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Exporting...';
                exportBtn.disabled = true;
                
                setTimeout(() => {
                    exportBtn.innerHTML = originalText;
                    exportBtn.disabled = false;
                    alert('Demo export completed! In real implementation, a CSV file would be downloaded.');
                }, 3000);
            }
        }
    </script>

    <!-- Bootstrap JS (Optional) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
