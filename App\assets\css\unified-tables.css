/* ==================== UNIFIED TABLES SYSTEM ==================== */
/* نظام الجداول الموحد لجميع صفحات الإدارة */
/* Created for better maintainability and consistency */

@import url('root-variables.css');

/* ==================== PREVENT PAGE HORIZONTAL SCROLL ==================== */
/* منع التمرير الأفقي للصفحة كاملة */

/* Ensure body and html don't have horizontal scroll */
html {
    overflow-x: hidden;
    max-width: 100vw;
}

body {
    overflow-x: hidden;
    max-width: 100vw;
    box-sizing: border-box;
}

/* Ensure all containers respect viewport width */
.container,
.container-fluid,
.main-content,
.content-wrapper {
    max-width: 100%;
    overflow-x: hidden;
    box-sizing: border-box;
}

/* Ensure data cards don't exceed container width */
.data-card,
.unified-table-container {
    max-width: 100%;
    overflow: hidden;
    box-sizing: border-box;
}

/* ==================== BASE TABLE STYLES ==================== */

/* Data Card Container - Modern Card Design */
.data-card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    margin-bottom: 20px;
    border: 1px solid var(--border-color);
    animation: fadeIn 0.5s ease;
    transition: var(--transition);
}

.data-card:hover {
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
}

/* Card Header */
.data-card .card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--white);
    padding: 12px 16px;
    border-bottom: 1px solid var(--primary-light);
    position: relative;
    overflow: hidden;
}

.data-card .card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s;
}

.data-card:hover .card-header::before {
    transform: translateX(100%);
}

.data-card .card-header h5 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    position: relative;
    z-index: 1;
    line-height: 1.3;
}

.data-card .card-header h5 i {
    margin-right: 6px;
    font-size: 14px;
    opacity: 0.9;
}

/* Card Body */
.data-card .card-body {
    padding: 15px;
}

.data-card .card-body.p-0 {
    padding: 0;
}

/* Card Footer */
.data-card .card-footer {
    background: var(--light-color);
    padding: 15px 20px;
    border-top: 1px solid var(--border-color);
    color: var(--gray-color);
    font-size: 14px;
}

/* Legacy Table Container Support */
.unified-table-container {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    margin-bottom: 30px;
    border: 1px solid var(--border-color);
    animation: fadeIn 0.5s ease;
}

.unified-table-responsive {
    overflow-x: auto;
    overflow-y: hidden;
    -webkit-overflow-scrolling: touch;
    max-width: 100%;
    width: 100%;
    position: relative;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    /* Ensure container doesn't exceed parent width */
    box-sizing: border-box;
}

/* Custom Scrollbar for Table Container */
.unified-table-responsive::-webkit-scrollbar {
    height: 8px;
    background-color: var(--light-color);
}

.unified-table-responsive::-webkit-scrollbar-thumb {
    background: linear-gradient(90deg, var(--primary-light), var(--primary-color));
    border-radius: 4px;
    transition: var(--transition);
}

.unified-table-responsive::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
}

.unified-table-responsive::-webkit-scrollbar-track {
    background-color: var(--light-color);
    border-radius: 4px;
}

/* Scroll Indicators - DISABLED */
.unified-table-responsive::before,
.unified-table-responsive::after {
    display: none;
}

/* Table Scroll Navigation - DISABLED */
.table-scroll-nav {
    display: none;
}

.scroll-btn {
    display: none;
}

/* Scroll Hint Text - DISABLED */
.scroll-hint {
    display: none !important;
}

.scroll-hint.show {
    display: none !important;
}

/* Base Table */
.unified-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 12px;
    margin-bottom: 0;
    background-color: var(--white);
    /* Set minimum width based on content, not fixed */
    min-width: max-content;
    /* Ensure table fits within container */
    table-layout: auto;
    line-height: 1.4;
}

/* Table columns responsive behavior */
.unified-table th,
.unified-table td {
    /* Allow columns to shrink but maintain readability */
    min-width: 70px;
    max-width: 180px;
    word-wrap: break-word;
    word-break: break-word;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Specific column width adjustments */
.unified-table th:first-child,
.unified-table td:first-child {
    min-width: 50px; /* ID/Code columns */
    max-width: 80px;
}

.unified-table th:last-child,
.unified-table td:last-child {
    min-width: 100px; /* Actions column */
    max-width: 120px;
    text-align: center;
}

/* Email columns */
.unified-table th:nth-child(2),
.unified-table td:nth-child(2) {
    min-width: 130px;
    max-width: 200px;
}

/* Allow text wrapping for certain columns */
.unified-table .allow-wrap {
    white-space: normal;
    word-wrap: break-word;
    max-width: none;
}

/* ==================== TABLE HEADER ==================== */

.unified-table thead {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    color: var(--white);
}

.unified-table th {
    padding: 8px 10px;
    text-align: left;
    font-weight: 600;
    font-size: 11px;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    border-bottom: 1px solid var(--primary-light);
    position: sticky;
    top: 0;
    z-index: 10;
    transition: var(--transition);
    white-space: nowrap;
    line-height: 1.3;
}

.unified-table th:hover {
    background-color: var(--primary-darker);
}

.unified-table th i {
    margin-right: 6px;
    opacity: 0.9;
    font-size: 10px;
}

/* ==================== TABLE BODY ==================== */

.unified-table td {
    padding: 8px 10px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
    transition: var(--transition);
    font-size: 11px;
    line-height: 1.3;
}

.unified-table tbody tr:nth-child(even) {
    background-color: rgba(208, 164, 208, 0.03);
}

.unified-table tbody tr:hover {
    background-color: rgba(208, 164, 208, 0.06);
    transform: translateY(-0.5px);
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.08);
}

.unified-table tbody tr:last-child td {
    border-bottom: none;
}

/* ==================== TABLE VARIANTS ==================== */

/* Compact Table */
.unified-table.table-compact th,
.unified-table.table-compact td {
    padding: 6px 8px;
    font-size: 10px;
}

/* Small Table */
.unified-table.table-sm th,
.unified-table.table-sm td {
    padding: 4px 6px;
    font-size: 9px;
}

/* Striped Table */
.unified-table.table-striped tbody tr:nth-child(odd) {
    background-color: rgba(208, 164, 208, 0.02);
}

/* Bordered Table */
.unified-table.table-bordered th,
.unified-table.table-bordered td {
    border: 1px solid var(--border-color);
}

/* ==================== STATUS BADGES ==================== */

.status-badge {
    padding: 3px 8px;
    border-radius: 16px;
    font-size: 9px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    display: inline-block;
    text-align: center;
    min-width: 60px;
    transition: var(--transition);
    border: 1px solid transparent;
    line-height: 1.2;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
    border-color: #ffeaa7;
}

.status-approved {
    background-color: #d4edda;
    color: #155724;
    border-color: #c3e6cb;
}

.status-rejected {
    background-color: #f8d7da;
    color: #721c24;
    border-color: #f5c6cb;
}

.status-active {
    background-color: #d1ecf1;
    color: #0c5460;
    border-color: #bee5eb;
}

.status-inactive {
    background-color: #f8f9fa;
    color: #6c757d;
    border-color: #dee2e6;
}

.status-processing {
    background-color: #e2e3e5;
    color: #383d41;
    border-color: #d6d8db;
}

/* ==================== ACTION BUTTONS ==================== */

.table-actions {
    display: flex;
    gap: 3px;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
}

.btn-table-action {
    width: 26px;
    height: 26px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    cursor: pointer;
    transition: var(--transition);
    font-size: 10px;
    text-decoration: none;
    color: var(--white);
    position: relative;
    overflow: hidden;
}

.btn-table-action:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    color: var(--white);
    text-decoration: none;
}

.btn-table-action::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
}

.btn-table-action:hover::before {
    width: 100%;
    height: 100%;
}

/* Button Colors */
.btn-table-action.btn-edit {
    background-color: #007bff;
}

.btn-table-action.btn-edit:hover {
    background-color: #0056b3;
}

.btn-table-action.btn-delete {
    background-color: var(--danger-color);
}

.btn-table-action.btn-delete:hover {
    background-color: #da190b;
}

.btn-table-action.btn-view {
    background-color: #28a745;
}

.btn-table-action.btn-view:hover {
    background-color: #1e7e34;
}

.btn-table-action.btn-archive {
    background-color: #6c757d;
}

.btn-table-action.btn-archive:hover {
    background-color: #545b62;
}

.btn-table-action.btn-approved {
    background-color: var(--success-color);
}

.btn-table-action.btn-approved:hover {
    background-color: #45a049;
}

.btn-table-action.btn-rejected {
    background-color: var(--danger-color);
}

.btn-table-action.btn-rejected:hover {
    background-color: #da190b;
}

/* ==================== PROFILE IMAGES ==================== */

.table-profile-img {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    object-fit: cover;
    border: 1px solid var(--primary-light);
    transition: var(--transition);
}

.table-profile-img:hover {
    transform: scale(1.05);
    border-color: var(--primary-color);
}

.table-profile-placeholder {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-light), var(--primary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 14px;
    font-weight: 600;
}

/* ==================== INPUT FIELDS ==================== */

.table-input {
    width: 100%;
    padding: 6px 10px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 12px;
    transition: var(--transition);
    background-color: var(--white);
}

.table-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(208, 164, 208, 0.2);
}

/* ==================== TABLE FILTERS ==================== */

.table-filters {
    background: var(--white);
    padding: 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-group label {
    font-size: 12px;
    font-weight: 600;
    color: var(--primary-dark);
    margin-bottom: 5px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.filter-group select,
.filter-group input {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 13px;
    transition: var(--transition);
}

.filter-group select:focus,
.filter-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(208, 164, 208, 0.2);
}

/* ==================== DATATABLES INTEGRATION ==================== */

.unified-table.dataTable {
    border-collapse: collapse !important;
}

.unified-table.dataTable thead th {
    border-bottom: 2px solid var(--primary-light) !important;
}

.unified-table.dataTable tbody td {
    border-top: none !important;
}

/* ==================== UNIFIED PAGINATION SYSTEM ==================== */
/* نظام ترقيم الصفحات الموحد */

.unified-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px 0;
    gap: 5px;
    flex-wrap: wrap;
}

.unified-pagination a,
.unified-pagination span {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    height: 40px;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--white);
    color: var(--dark-color);
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: var(--transition);
    cursor: pointer;
}

.unified-pagination a:hover {
    background: var(--primary-light);
    border-color: var(--primary-color);
    color: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.unified-pagination .active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(208, 164, 208, 0.3);
}

.unified-pagination .disabled {
    background: var(--light-color);
    color: var(--gray-color);
    cursor: not-allowed;
    opacity: 0.6;
}

.unified-pagination .disabled:hover {
    background: var(--light-color);
    border-color: var(--border-color);
    color: var(--gray-color);
    transform: none;
    box-shadow: none;
}

.unified-pagination .dots {
    background: transparent;
    border: none;
    color: var(--gray-color);
    cursor: default;
    font-weight: bold;
}

.unified-pagination .dots:hover {
    background: transparent;
    border: none;
    color: var(--gray-color);
    transform: none;
    box-shadow: none;
}

/* Pagination Info */
.pagination-info {
    text-align: center;
    margin: 10px 0;
    color: var(--gray-color);
    font-size: 14px;
}

.pagination-info strong {
    color: var(--primary-dark);
    font-weight: 600;
}

/* DataTables Wrapper Styling - Hide unwanted elements */
.dataTables_wrapper {
    padding: 0;
}

.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_paginate {
    display: none !important;
}

.dataTables_wrapper .dataTables_filter {
    margin-bottom: 15px;
}

.dataTables_wrapper .dataTables_filter input {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    margin-left: 5px;
    font-size: 14px;
}

.dataTables_wrapper .dataTables_filter label {
    font-weight: 600;
    color: var(--primary-dark);
    font-size: 14px;
}

/* Custom Search Box */
.unified-search {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.unified-search-box {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;
    max-width: 400px;
}

.unified-search-box input {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 14px;
    transition: var(--transition);
}

.unified-search-box input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(208, 164, 208, 0.2);
}

.unified-search-box button {
    padding: 10px 15px;
    background: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition);
    font-size: 14px;
}

.unified-search-box button:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Results per page selector */
.results-per-page {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
    color: var(--dark-color);
}

.results-per-page select {
    padding: 6px 10px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 14px;
    background: var(--white);
    cursor: pointer;
}

.results-per-page select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(208, 164, 208, 0.2);
}

/* ==================== SPECIAL COMPONENTS ==================== */

/* Employee Code Display */
.employee-code {
    background: var(--light-color);
    padding: 3px 6px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 10px;
    font-weight: 600;
    color: var(--primary-dark);
    border: 1px solid var(--primary-light);
    letter-spacing: 0.5px;
    display: inline-block;
    text-transform: uppercase;
    line-height: 1.2;
}

/* Archive Users Page - Smaller Action Buttons */
.reactivate-btn {
    padding: 4px 8px !important;
    font-size: 9px !important;
    line-height: 1.2 !important;
    border-radius: 3px !important;
    min-width: auto !important;
    white-space: nowrap !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 3px !important;
    font-weight: 500 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.3px !important;
}

.reactivate-btn i {
    font-size: 8px !important;
    margin-right: 0 !important;
}

.reactivate-btn:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* Archive Users Page - All buttons smaller */
body:has(.reactivate-btn) .btn:not(.mobile-menu-btn):not(.notification-close) {
    padding: 6px 12px !important;
    font-size: 10px !important;
    line-height: 1.3 !important;
    font-weight: 500 !important;
    border-radius: 4px !important;
    min-height: auto !important;
}

body:has(.reactivate-btn) .btn i {
    font-size: 9px !important;
    margin-right: 4px !important;
}

/* Export button specific styling for Archive Users */
body:has(.reactivate-btn) #exportBtn {
    padding: 8px 14px !important;
    font-size: 11px !important;
}

body:has(.reactivate-btn) #exportBtn i {
    font-size: 10px !important;
}

/* Modal buttons in Archive Users page */
body:has(.reactivate-btn) .modal-btn {
    padding: 6px 12px !important;
    font-size: 10px !important;
    line-height: 1.3 !important;
    border-radius: 3px !important;
}

body:has(.reactivate-btn) .btn-close {
    font-size: 9px !important;
    padding: 4px 8px !important;
}

/* Date Display */
.table-date {
    font-family: 'Courier New', monospace;
    font-size: 10px;
    color: var(--gray-color);
    line-height: 1.2;
}

/* Attachment Links */
.table-attachment {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 10px;
    transition: var(--transition);
}

.table-attachment:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

/* ==================== RESPONSIVE DESIGN ==================== */

@media (max-width: 992px) {
    .data-card .card-header {
        padding: 10px 12px;
    }

    .data-card .card-header h5 {
        font-size: 13px;
    }

    .data-card .card-body {
        padding: 12px;
    }

    .data-card .card-footer {
        padding: 12px 15px;
        font-size: 13px;
    }

    .table-filters {
        grid-template-columns: repeat(2, 1fr);
    }

    .unified-search {
        flex-direction: column;
        align-items: stretch;
    }

    .unified-search-box {
        max-width: none;
    }

    .unified-pagination {
        gap: 3px;
    }

    .unified-pagination a,
    .unified-pagination span {
        min-width: 35px;
        height: 35px;
        padding: 6px 10px;
        font-size: 13px;
    }
}

@media (max-width: 768px) {
    .data-card .card-header {
        padding: 8px 10px;
    }

    .data-card .card-header h5 {
        font-size: 12px;
    }

    .data-card .card-body {
        padding: 10px;
    }

    /* Smaller action buttons on mobile */
    .reactivate-btn {
        padding: 3px 6px !important;
        font-size: 8px !important;
    }

    .reactivate-btn i {
        font-size: 7px !important;
        margin-right: 2px !important;
    }

    /* All buttons smaller on mobile for Archive Users */
    body:has(.reactivate-btn) .btn:not(.mobile-menu-btn):not(.notification-close) {
        padding: 4px 8px !important;
        font-size: 9px !important;
    }

    body:has(.reactivate-btn) .btn i {
        font-size: 8px !important;
    }

    body:has(.reactivate-btn) #exportBtn {
        padding: 6px 10px !important;
        font-size: 10px !important;
    }

    .data-card .card-footer {
        padding: 10px 12px;
        font-size: 12px;
    }

    .unified-table {
        font-size: 11px;
        /* Remove fixed min-width for mobile */
        min-width: auto;
        table-layout: fixed; /* Fixed layout for better control */
    }

    .unified-table th,
    .unified-table td {
        padding: 6px 4px;
        min-width: 60px; /* Smaller minimum for mobile */
        max-width: 120px;
        font-size: 11px;
    }

    /* Adjust specific columns for mobile */
    .unified-table th:first-child,
    .unified-table td:first-child {
        min-width: 40px;
        max-width: 60px;
    }

    .unified-table th:last-child,
    .unified-table td:last-child {
        min-width: 80px;
        max-width: 100px;
    }

    /* Email columns on mobile */
    .unified-table th:nth-child(2),
    .unified-table td:nth-child(2) {
        min-width: 100px;
        max-width: 140px;
    }

    /* Hide less important columns on very small screens */
    .unified-table .hide-mobile {
        display: none;
    }

    .table-filters {
        grid-template-columns: 1fr;
        gap: 10px;
        padding: 15px;
    }

    .table-actions {
        flex-direction: column;
        gap: 2px;
    }

    .btn-table-action {
        width: 24px;
        height: 24px;
        font-size: 9px;
    }

    .status-badge {
        font-size: 10px;
        padding: 3px 8px;
        min-width: 60px;
    }

    .unified-pagination {
        gap: 2px;
    }

    .unified-pagination a,
    .unified-pagination span {
        min-width: 32px;
        height: 32px;
        padding: 4px 8px;
        font-size: 12px;
    }

    .pagination-info {
        font-size: 12px;
    }

    .unified-search {
        margin-bottom: 15px;
    }

    .results-per-page {
        font-size: 12px;
    }

    /* Mobile Scroll Enhancements */
    .unified-table-responsive {
        border-radius: 0;
    }

    .unified-table-responsive::-webkit-scrollbar {
        height: 6px;
    }

    .table-scroll-nav {
        display: none;
    }

    .scroll-btn {
        display: none;
    }

    .scroll-hint {
        display: none !important;
    }

    /* Touch-friendly scrolling */
    .unified-table-responsive {
        scroll-behavior: smooth;
        -webkit-overflow-scrolling: touch;
    }

    /* Scroll hint disabled on mobile */
    .scroll-hint {
        display: none !important;
    }

    .scroll-hint::before {
        display: none;
    }

    .scroll-hint::after {
        display: none;
    }

    .table-profile-img,
    .table-profile-placeholder {
        width: 24px;
        height: 24px;
    }

    .table-profile-placeholder {
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    .unified-table th,
    .unified-table td {
        padding: 5px 3px;
        font-size: 10px;
    }

    .btn-table-action {
        width: 20px;
        height: 20px;
        font-size: 8px;
    }

    /* Extra small buttons for Archive Users on very small screens */
    body:has(.reactivate-btn) .reactivate-btn {
        padding: 2px 4px !important;
        font-size: 7px !important;
    }

    body:has(.reactivate-btn) .btn:not(.mobile-menu-btn):not(.notification-close) {
        padding: 3px 6px !important;
        font-size: 8px !important;
    }

    body:has(.reactivate-btn) #exportBtn {
        padding: 4px 8px !important;
        font-size: 9px !important;
    }

    .table-profile-img,
    .table-profile-placeholder {
        width: 20px;
        height: 20px;
    }

    .table-profile-placeholder {
        font-size: 10px;
    }

    .unified-pagination a,
    .unified-pagination span {
        min-width: 28px;
        height: 28px;
        padding: 2px 6px;
        font-size: 11px;
    }

    .unified-search-box input,
    .unified-search-box button {
        padding: 8px 12px;
        font-size: 12px;
    }
}

/* ==================== ANIMATIONS ==================== */

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Table row animation */
.unified-table tbody tr {
    animation: slideUp 0.3s ease;
}

/* ==================== PRINT STYLES ==================== */

@media print {
    .unified-table-container {
        box-shadow: none;
        border: 1px solid #000;
    }

    .unified-table th {
        background: #f0f0f0 !important;
        color: #000 !important;
    }

    .table-actions,
    .btn-table-action {
        display: none !important;
    }

    .unified-table tbody tr:hover {
        background-color: transparent !important;
        transform: none !important;
        box-shadow: none !important;
    }
}

/* ==================== EXPORT BUTTON STYLES ==================== */
/* تنسيقات زر التصدير */

.export-form {
    display: inline-block;
    margin: 0;
}

.export-btn {
    background: linear-gradient(135deg, #28a745, #20c997);
    border: none;
    color: var(--white);
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.2);
    position: relative;
    overflow: hidden;
}

.export-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.export-btn:hover {
    background: linear-gradient(135deg, #218838, #1ea085);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
    color: var(--white);
    text-decoration: none;
}

.export-btn:hover::before {
    left: 100%;
}

.export-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.2);
}

.export-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.export-btn:disabled:hover {
    background: #6c757d;
    transform: none;
    box-shadow: none;
}

.export-btn i {
    font-size: 14px;
    transition: var(--transition);
}

.export-btn:hover i {
    transform: scale(1.1);
}

/* Loading spinner animation */
.export-btn .fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Action buttons without container */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-right: 10px;
    margin-bottom: 10px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    text-decoration: none;
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--white);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark), #5a4fcf);
    color: var(--white);
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: var(--white);
}

.btn-success:hover {
    background: linear-gradient(135deg, #218838, #1ea085);
    color: var(--white);
}



/* Responsive adjustments for buttons */
@media (max-width: 768px) {
    .btn {
        display: block;
        width: 100%;
        margin-right: 0;
        margin-bottom: 8px;
        justify-content: center;
        padding: 12px 16px;
        font-size: 13px;
    }

    form[style*="inline-block"] {
        display: block !important;
        width: 100%;
    }
}
