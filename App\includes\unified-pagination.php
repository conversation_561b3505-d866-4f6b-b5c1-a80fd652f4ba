<?php
/**
 * Unified Pagination Component
 * مكون ترقيم الصفحات الموحد
 * 
 * يوفر نظام pagination موحد لجميع صفحات الإدارة
 * مع إمكانية التخصيص والتحكم الكامل
 */

/**
 * Generate unified pagination HTML
 * 
 * @param int $currentPage Current page number
 * @param int $totalPages Total number of pages
 * @param int $totalRecords Total number of records
 * @param int $recordsPerPage Records per page
 * @param string $baseUrl Base URL for pagination links
 * @param array $extraParams Additional URL parameters
 * @return string HTML pagination
 */
function generateUnifiedPagination($currentPage, $totalPages, $totalRecords, $recordsPerPage, $baseUrl = '', $extraParams = []) {
    if ($totalPages <= 1) {
        return '';
    }
    
    $html = '';
    
    // Build query string for extra parameters
    $queryString = '';
    if (!empty($extraParams)) {
        $queryString = '&' . http_build_query($extraParams);
    }
    
    // Calculate display range
    $startRecord = (($currentPage - 1) * $recordsPerPage) + 1;
    $endRecord = min($currentPage * $recordsPerPage, $totalRecords);
    
    // Pagination info
    $html .= '<div class="pagination-info">';
    $html .= sprintf(
        'Showing <strong>%d</strong> to <strong>%d</strong> of <strong>%d</strong> entries',
        $startRecord,
        $endRecord,
        $totalRecords
    );
    $html .= '</div>';
    
    // Pagination navigation
    $html .= '<div class="unified-pagination">';
    
    // Previous button
    if ($currentPage > 1) {
        $prevPage = $currentPage - 1;
        $html .= sprintf(
            '<a href="%s?page=%d%s" title="Previous Page"><i class="fas fa-chevron-left"></i> Previous</a>',
            $baseUrl,
            $prevPage,
            $queryString
        );
    } else {
        $html .= '<span class="disabled"><i class="fas fa-chevron-left"></i> Previous</span>';
    }
    
    // Page numbers
    $startPage = max(1, $currentPage - 2);
    $endPage = min($totalPages, $currentPage + 2);
    
    // First page and dots
    if ($startPage > 1) {
        $html .= sprintf(
            '<a href="%s?page=1%s">1</a>',
            $baseUrl,
            $queryString
        );
        
        if ($startPage > 2) {
            $html .= '<span class="dots">...</span>';
        }
    }
    
    // Page range
    for ($i = $startPage; $i <= $endPage; $i++) {
        if ($i == $currentPage) {
            $html .= sprintf('<span class="active">%d</span>', $i);
        } else {
            $html .= sprintf(
                '<a href="%s?page=%d%s">%d</a>',
                $baseUrl,
                $i,
                $queryString,
                $i
            );
        }
    }
    
    // Last page and dots
    if ($endPage < $totalPages) {
        if ($endPage < $totalPages - 1) {
            $html .= '<span class="dots">...</span>';
        }
        
        $html .= sprintf(
            '<a href="%s?page=%d%s">%d</a>',
            $baseUrl,
            $totalPages,
            $queryString,
            $totalPages
        );
    }
    
    // Next button
    if ($currentPage < $totalPages) {
        $nextPage = $currentPage + 1;
        $html .= sprintf(
            '<a href="%s?page=%d%s" title="Next Page">Next <i class="fas fa-chevron-right"></i></a>',
            $baseUrl,
            $nextPage,
            $queryString
        );
    } else {
        $html .= '<span class="disabled">Next <i class="fas fa-chevron-right"></i></span>';
    }
    
    $html .= '</div>';
    
    return $html;
}

/**
 * Generate unified search box
 * 
 * @param string $currentSearch Current search term
 * @param string $placeholder Search placeholder text
 * @param string $actionUrl Form action URL
 * @param array $hiddenFields Hidden form fields
 * @return string HTML search box
 */
function generateUnifiedSearch($currentSearch = '', $placeholder = 'Search...', $actionUrl = '', $hiddenFields = []) {
    $html = '<div class="unified-search">';
    $html .= '<form method="GET" action="' . htmlspecialchars($actionUrl) . '" class="unified-search-box">';
    
    // Hidden fields
    foreach ($hiddenFields as $name => $value) {
        $html .= sprintf(
            '<input type="hidden" name="%s" value="%s">',
            htmlspecialchars($name),
            htmlspecialchars($value)
        );
    }
    
    $html .= sprintf(
        '<input type="text" name="search" placeholder="%s" value="%s">',
        htmlspecialchars($placeholder),
        htmlspecialchars($currentSearch)
    );
    $html .= '<button type="submit"><i class="fas fa-search"></i> Search</button>';
    $html .= '</form>';
    $html .= '</div>';
    
    return $html;
}

/**
 * Generate results per page selector
 * 
 * @param int $currentPerPage Current results per page
 * @param array $options Available options
 * @param string $baseUrl Base URL
 * @param array $extraParams Extra parameters
 * @return string HTML selector
 */
function generateResultsPerPage($currentPerPage = 10, $options = [5, 10, 25, 50, 100], $baseUrl = '', $extraParams = []) {
    $queryString = '';
    if (!empty($extraParams)) {
        $queryString = '&' . http_build_query($extraParams);
    }
    
    $html = '<div class="results-per-page">';
    $html .= '<label>Show:</label>';
    $html .= '<select onchange="window.location.href=this.value">';
    
    foreach ($options as $option) {
        $selected = ($option == $currentPerPage) ? 'selected' : '';
        $url = $baseUrl . '?per_page=' . $option . $queryString;
        $html .= sprintf(
            '<option value="%s" %s>%d</option>',
            htmlspecialchars($url),
            $selected,
            $option
        );
    }
    
    $html .= '</select>';
    $html .= '<label>entries</label>';
    $html .= '</div>';
    
    return $html;
}

/**
 * Calculate pagination data
 * 
 * @param int $totalRecords Total number of records
 * @param int $recordsPerPage Records per page
 * @param int $currentPage Current page
 * @return array Pagination data
 */
function calculatePaginationData($totalRecords, $recordsPerPage = 10, $currentPage = 1) {
    $totalPages = ceil($totalRecords / $recordsPerPage);
    $currentPage = max(1, min($currentPage, $totalPages));
    $offset = ($currentPage - 1) * $recordsPerPage;
    
    return [
        'total_records' => $totalRecords,
        'records_per_page' => $recordsPerPage,
        'total_pages' => $totalPages,
        'current_page' => $currentPage,
        'offset' => $offset,
        'has_previous' => $currentPage > 1,
        'has_next' => $currentPage < $totalPages,
        'start_record' => $offset + 1,
        'end_record' => min($offset + $recordsPerPage, $totalRecords)
    ];
}

/**
 * Generate complete pagination with search and controls
 * 
 * @param array $config Configuration array
 * @return string Complete pagination HTML
 */
function generateCompletePagination($config) {
    $defaults = [
        'current_page' => 1,
        'total_pages' => 1,
        'total_records' => 0,
        'records_per_page' => 10,
        'base_url' => '',
        'extra_params' => [],
        'show_search' => true,
        'show_per_page' => true,
        'search_value' => '',
        'search_placeholder' => 'Search...',
        'per_page_options' => [5, 10, 25, 50, 100]
    ];
    
    $config = array_merge($defaults, $config);
    
    $html = '';
    
    // Search box
    if ($config['show_search']) {
        $html .= generateUnifiedSearch(
            $config['search_value'],
            $config['search_placeholder'],
            $config['base_url'],
            $config['extra_params']
        );
    }
    
    // Results per page selector
    if ($config['show_per_page'] && $config['total_records'] > 0) {
        $html .= generateResultsPerPage(
            $config['records_per_page'],
            $config['per_page_options'],
            $config['base_url'],
            $config['extra_params']
        );
    }
    
    // Pagination
    if ($config['total_pages'] > 1) {
        $html .= generateUnifiedPagination(
            $config['current_page'],
            $config['total_pages'],
            $config['total_records'],
            $config['records_per_page'],
            $config['base_url'],
            $config['extra_params']
        );
    }
    
    return $html;
}

/**
 * Simple pagination for basic use cases
 * 
 * @param int $currentPage
 * @param int $totalPages
 * @param string $baseUrl
 * @param array $extraParams
 * @return string
 */
function simplePagination($currentPage, $totalPages, $baseUrl = '', $extraParams = []) {
    if ($totalPages <= 1) {
        return '';
    }
    
    $queryString = '';
    if (!empty($extraParams)) {
        $queryString = '&' . http_build_query($extraParams);
    }
    
    $html = '<div class="unified-pagination">';
    
    // Previous
    if ($currentPage > 1) {
        $html .= sprintf(
            '<a href="%s?page=%d%s"><i class="fas fa-chevron-left"></i></a>',
            $baseUrl,
            $currentPage - 1,
            $queryString
        );
    }
    
    // Pages
    for ($i = 1; $i <= $totalPages; $i++) {
        if ($i == $currentPage) {
            $html .= sprintf('<span class="active">%d</span>', $i);
        } else {
            $html .= sprintf(
                '<a href="%s?page=%d%s">%d</a>',
                $baseUrl,
                $i,
                $queryString,
                $i
            );
        }
    }
    
    // Next
    if ($currentPage < $totalPages) {
        $html .= sprintf(
            '<a href="%s?page=%d%s"><i class="fas fa-chevron-right"></i></a>',
            $baseUrl,
            $currentPage + 1,
            $queryString
        );
    }
    
    $html .= '</div>';
    
    return $html;
}
?>
