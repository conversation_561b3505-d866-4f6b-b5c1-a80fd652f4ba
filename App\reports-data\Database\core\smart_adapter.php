<?php
/**
 * نظام التكيف الذكي مع أي قاعدة بيانات
 * Smart Database Adapter - Works with ANY database structure
 */

class SmartDatabaseAdapter {
    private $conn;
    private $tableName;
    private $tableStructure = [];
    private $columnTypes = [];
    private $filterableColumns = [];
    private $dateColumns = [];
    private $enumColumns = [];
    private $textColumns = [];
    
    public function __construct($connection, $tableName = 'databasehc') {
        $this->conn = $connection;
        $this->tableName = $tableName;
        $this->analyzeTableStructure();
    }
    
    /**
     * تحليل بنية الجدول تلقائياً
     * Automatically analyze table structure
     */
    private function analyzeTableStructure() {
        try {
            $result = $this->conn->query("DESCRIBE {$this->tableName}");
            
            while ($row = $result->fetch_assoc()) {
                $columnName = $row['Field'];
                $columnType = strtolower($row['Type']);
                
                $this->tableStructure[$columnName] = $row;
                $this->columnTypes[$columnName] = $columnType;
                
                // تصنيف الأعمدة حسب النوع
                if (strpos($columnType, 'date') !== false || strpos($columnType, 'time') !== false) {
                    $this->dateColumns[] = $columnName;
                } elseif (strpos($columnType, 'enum') !== false) {
                    $this->enumColumns[] = $columnName;
                } elseif (strpos($columnType, 'text') !== false || strpos($columnType, 'varchar') !== false) {
                    $this->textColumns[] = $columnName;
                }
                
                // تحديد الأعمدة القابلة للفلترة
                if ($this->isFilterableColumn($columnName, $columnType)) {
                    $this->filterableColumns[] = $columnName;
                }
            }
            
        } catch (Exception $e) {
            error_log("Error analyzing table structure: " . $e->getMessage());
        }
    }
    
    /**
     * تحديد ما إذا كان العمود قابل للفلترة
     * Determine if column is filterable
     */
    private function isFilterableColumn($columnName, $columnType) {
        // استبعاد الأعمدة غير المناسبة للفلترة
        $excludeColumns = ['id', 'password', 'token', 'hash', 'created_at', 'updated_at'];
        $excludePatterns = ['password', 'token', 'hash', 'secret', 'key'];
        
        $lowerColumnName = strtolower($columnName);
        
        // تحقق من الأعمدة المستبعدة
        if (in_array($lowerColumnName, $excludeColumns)) {
            return false;
        }
        
        // تحقق من الأنماط المستبعدة
        foreach ($excludePatterns as $pattern) {
            if (strpos($lowerColumnName, $pattern) !== false) {
                return false;
            }
        }
        
        // تضمين الأعمدة المناسبة للفلترة
        if (strpos($columnType, 'enum') !== false ||
            strpos($columnType, 'varchar') !== false ||
            strpos($columnType, 'text') !== false ||
            strpos($columnType, 'date') !== false ||
            strpos($columnType, 'int') !== false) {
            return true;
        }
        
        return false;
    }
    
    /**
     * الحصول على جميع الأعمدة
     * Get all columns
     */
    public function getAllColumns() {
        return array_keys($this->tableStructure);
    }
    
    /**
     * الحصول على الأعمدة القابلة للفلترة
     * Get filterable columns
     */
    public function getFilterableColumns() {
        return $this->filterableColumns;
    }
    
    /**
     * الحصول على أعمدة التاريخ
     * Get date columns
     */
    public function getDateColumns() {
        return $this->dateColumns;
    }
    
    /**
     * الحصول على أعمدة ENUM
     * Get ENUM columns
     */
    public function getEnumColumns() {
        return $this->enumColumns;
    }
    
    /**
     * الحصول على خيارات الفلتر لعمود معين بأمان
     * Safely get filter options for a column
     */
    public function getFilterOptions($columnName, $limit = 100) {
        if (!$this->columnExists($columnName)) {
            return [];
        }
        
        try {
            $sql = $this->buildSafeFilterQuery($columnName, $limit);
            $result = $this->conn->query($sql);
            
            if (!$result) {
                error_log("Query failed for column $columnName: " . $this->conn->error);
                return [];
            }
            
            $options = [];
            while ($row = $result->fetch_assoc()) {
                $value = $row[$columnName];
                if ($this->isValidValue($columnName, $value)) {
                    $options[] = $value;
                }
            }
            
            return $options;
            
        } catch (Exception $e) {
            error_log("Error getting filter options for $columnName: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * بناء استعلام آمن للفلتر
     * Build safe filter query
     */
    private function buildSafeFilterQuery($columnName, $limit) {
        $columnType = $this->columnTypes[$columnName];
        
        $sql = "SELECT DISTINCT `$columnName` FROM {$this->tableName} WHERE `$columnName` IS NOT NULL";
        
        // معالجة خاصة حسب نوع العمود
        if (in_array($columnName, $this->dateColumns)) {
            // أعمدة التاريخ
            $sql .= " AND `$columnName` != '' AND `$columnName` != '0000-00-00' AND `$columnName` > '1900-01-01'";
        } elseif (strpos($columnType, 'int') !== false) {
            // الأعمدة الرقمية
            $sql .= " AND `$columnName` != 0";
        } else {
            // الأعمدة النصية
            $sql .= " AND `$columnName` != ''";
        }
        
        $sql .= " ORDER BY `$columnName` LIMIT $limit";
        
        return $sql;
    }
    
    /**
     * التحقق من صحة القيمة
     * Validate value
     */
    private function isValidValue($columnName, $value) {
        if ($value === null || $value === '') {
            return false;
        }
        
        if (in_array($columnName, $this->dateColumns)) {
            // التحقق من صحة التاريخ
            if ($value === '0000-00-00' || $value < '1900-01-01') {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * التحقق من وجود العمود
     * Check if column exists
     */
    public function columnExists($columnName) {
        return isset($this->tableStructure[$columnName]);
    }
    
    /**
     * الحصول على نوع العمود
     * Get column type
     */
    public function getColumnType($columnName) {
        return $this->columnTypes[$columnName] ?? 'unknown';
    }
    
    /**
     * الحصول على تفاصيل العمود
     * Get column details
     */
    public function getColumnDetails($columnName) {
        return $this->tableStructure[$columnName] ?? null;
    }
    
    /**
     * إنشاء HTML للفلتر تلقائياً
     * Auto-generate filter HTML
     */
    public function generateFilterHTML($columnName, $selectedValues = []) {
        if (!$this->columnExists($columnName)) {
            return '';
        }
        
        $options = $this->getFilterOptions($columnName);
        if (empty($options)) {
            return '';
        }
        
        $displayName = $this->getColumnDisplayName($columnName);
        $filterId = strtolower($columnName) . '_filter';
        
        $html = '<div class="filter-group">';
        $html .= '<label for="' . $filterId . '">' . $displayName . '</label>';
        $html .= '<select name="' . $columnName . '[]" id="' . $filterId . '" multiple class="form-control">';
        
        foreach ($options as $option) {
            $selected = in_array($option, $selectedValues) ? 'selected' : '';
            $html .= '<option value="' . htmlspecialchars($option) . '" ' . $selected . '>';
            $html .= htmlspecialchars($option);
            $html .= '</option>';
        }
        
        $html .= '</select>';
        $html .= '</div>';
        
        return $html;
    }
    
    /**
     * الحصول على اسم العرض للعمود
     * Get display name for column
     */
    public function getColumnDisplayName($columnName) {
        // قاموس الترجمة
        $translations = [
            'id' => 'ID',
            'name' => 'Name',
            'email' => 'Email',
            'status' => 'Status',
            'created_at' => 'Created Date',
            'updated_at' => 'Updated Date'
        ];
        
        $lowerName = strtolower($columnName);
        
        if (isset($translations[$lowerName])) {
            return $translations[$lowerName];
        }
        
        // تحويل تلقائي: camelCase أو snake_case إلى عنوان
        $displayName = preg_replace('/([A-Z])/', ' $1', $columnName);
        $displayName = str_replace('_', ' ', $displayName);
        $displayName = ucwords(trim($displayName));
        
        return $displayName;
    }
    
    /**
     * إنشاء جميع الفلاتر تلقائياً
     * Auto-generate all filters
     */
    public function generateAllFilters($selectedFilters = []) {
        $html = '<div class="filters-container">';
        
        foreach ($this->filterableColumns as $column) {
            $selectedValues = $selectedFilters[$column] ?? [];
            $filterHTML = $this->generateFilterHTML($column, $selectedValues);
            if (!empty($filterHTML)) {
                $html .= $filterHTML;
            }
        }
        
        $html .= '</div>';
        
        return $html;
    }
}
?>
