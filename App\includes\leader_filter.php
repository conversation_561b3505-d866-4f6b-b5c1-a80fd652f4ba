<?php
/**
 * ملف فلترة البيانات بناءً على الليدر
 * يحتوي على دوال لاستخراج اسم الليدر من الإيميل وتطبيق الفلترة
 */

/**
 * استخراج اسم الليدر من الإيميل
 * مثال: <EMAIL> -> <PERSON><PERSON><PERSON> Cherkavska
 * @param string $email
 * @return array
 */
function extractLeaderNameFromEmail($email) {
    // إزالة المسافات الزائدة
    $email = trim($email);

    // التحقق من صحة الإيميل
    if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        return [
            'first' => '',
            'last' => '',
            'full' => ''
        ];
    }

    // الحصول على الجزء قبل @
    $beforeAt = explode('@', $email)[0];

    // التحقق من وجود نقطة
    if (strpos($beforeAt, '.') !== false) {
        $nameParts = explode('.', $beforeAt);

        // التأكد من وجود جزأين على الأقل
        if (count($nameParts) >= 2) {
            $firstName = ucfirst(strtolower(trim($nameParts[0])));
            $lastName = ucfirst(strtolower(trim($nameParts[1])));

            return [
                'first' => $firstName,
                'last' => $lastName,
                'full' => $firstName . ' ' . $lastName
            ];
        }
    }

    // إذا لم توجد نقطة، استخدم الاسم كاملاً كاسم أول
    return [
        'first' => ucfirst(strtolower(trim($beforeAt))),
        'last' => '',
        'full' => ucfirst(strtolower(trim($beforeAt)))
    ];
}

/**
 * التحقق من صلاحية المستخدم وإرجاع معلومات الفلترة
 * @param string $user_permission
 * @param string $user_email
 * @return array
 */
function getLeaderFilterInfo($user_permission, $user_email) {
    $filter_info = [
        'should_filter' => false,
        'leader_name' => '',
        'filter_condition' => '',
        'user_info' => extractLeaderNameFromEmail($user_email)
    ];

    // التحقق من نوع الصلاحية
    if ($user_permission === 'Leader') {
        $filter_info['should_filter'] = true;
        $filter_info['leader_name'] = $filter_info['user_info']['full'];
        $filter_info['filter_condition'] = " AND Leader = '" . addslashes($filter_info['leader_name']) . "'";
    }

    return $filter_info;
}

/**
 * تطبيق فلترة الليدر على استعلام SQL
 * @param string $sql الاستعلام الأصلي
 * @param string $user_permission صلاحية المستخدم
 * @param string $user_email إيميل المستخدم
 * @param string $leader_column اسم عمود الليدر (افتراضي: Leader)
 * @return string الاستعلام مع الفلترة
 */
function applyLeaderFilter($sql, $user_permission, $user_email, $leader_column = 'Leader') {
    $filter_info = getLeaderFilterInfo($user_permission, $user_email);

    if ($filter_info['should_filter'] && !empty($filter_info['leader_name'])) {
        $filter_condition = " AND {$leader_column} = '" . addslashes($filter_info['leader_name']) . "'";

        // إضافة الشرط للاستعلام
        if (stripos($sql, 'WHERE') !== false) {
            $sql .= $filter_condition;
        } else {
            $sql .= ' WHERE 1=1' . $filter_condition;
        }
    }

    return $sql;
}

/**
 * الحصول على شرط WHERE للفلترة
 * @param string $user_permission
 * @param string $user_email
 * @param string $leader_column
 * @return string
 */
function getLeaderWhereCondition($user_permission, $user_email, $leader_column = 'Leader') {
    $filter_info = getLeaderFilterInfo($user_permission, $user_email);

    if ($filter_info['should_filter'] && !empty($filter_info['leader_name'])) {
        return " AND {$leader_column} = '" . addslashes($filter_info['leader_name']) . "'";
    }

    return '';
}

/**
 * فلترة مصفوفة البيانات بناءً على الليدر
 * @param array $data البيانات الأصلية
 * @param string $user_permission صلاحية المستخدم
 * @param string $user_email إيميل المستخدم
 * @param string $leader_key مفتاح الليدر في المصفوفة (افتراضي: Leader)
 * @return array البيانات المفلترة
 */
function filterDataByLeader($data, $user_permission, $user_email, $leader_key = 'Leader') {
    $filter_info = getLeaderFilterInfo($user_permission, $user_email);

    if (!$filter_info['should_filter'] || empty($filter_info['leader_name'])) {
        return $data;
    }

    return array_filter($data, function($item) use ($filter_info, $leader_key) {
        return isset($item[$leader_key]) &&
               trim($item[$leader_key]) === $filter_info['leader_name'];
    });
}

/**
 * دالة للحصول على Active Headcount مع فلترة الليدر
 * @param mysqli $conn اتصال قاعدة البيانات
 * @param string $user_permission صلاحية المستخدم
 * @param string $user_email إيميل المستخدم
 * @return array
 */
function getActiveHeadcountWithLeaderFilter($conn, $user_permission, $user_email) {
    $data = [];
    $total = 0;

    try {
        // بناء الاستعلام الأساسي
        $sql = "SELECT Module, COUNT(*) as count FROM databasehc WHERE Status = 'In Production'";

        // تطبيق فلترة الليدر
        $sql = applyLeaderFilter($sql, $user_permission, $user_email, 'Leader');

        // إضافة GROUP BY
        $sql .= " GROUP BY Module ORDER BY Module";

        $result = $conn->query($sql);

        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $data[] = $row;
                $total += $row['count'];
            }
        }

        // تسجيل الاستعلام للتتبع (في حالة التطوير)
        if (isset($_GET['debug']) && $_GET['debug'] == '1') {
            error_log("Active Headcount SQL: " . $sql);
            error_log("Active Headcount Results: " . json_encode($data));
        }

    } catch (Exception $e) {
        error_log("Error in getActiveHeadcountWithLeaderFilter: " . $e->getMessage());
    }

    return [
        'data' => $data,
        'total' => $total,
        'sql_used' => $sql ?? '',
        'filter_applied' => getLeaderFilterInfo($user_permission, $user_email)['should_filter']
    ];
}

/**
 * دالة للحصول على إحصائيات مع فلترة الليدر
 * @param mysqli $conn اتصال قاعدة البيانات
 * @param string $user_permission صلاحية المستخدم
 * @param string $user_email إيميل المستخدم
 * @return array
 */
function getStatisticsWithLeaderFilter($conn, $user_permission, $user_email) {
    $stats = [];
    $current_date = date('Y-m-d');

    try {
        // الحصول على معلومات الفلترة
        $filter_info = getLeaderFilterInfo($user_permission, $user_email);
        $leader_filter = $filter_info['filter_condition'];

        // Daily schedule count - جميع الجدولة لليوم
        $schedule_count_sql = "SELECT COUNT(*) as count FROM schedule
                              WHERE Date = CURRENT_DATE" . $leader_filter;
        $result = $conn->query($schedule_count_sql);
        $stats['schedule_count'] = ($result && $result->num_rows > 0) ? $result->fetch_assoc()['count'] : 0;

        // Absenteeism count - نفس منطق attendancereport.php بالضبط
        // تضمين نظام الصلاحيات
        include_once $_SERVER['DOCUMENT_ROOT'].'/App/includes/apply_permissions_helper.php';

        $base_absenteeism_sql = "SELECT
                                   CASE
                                       WHEN s.Shift REGEXP '^[A-Za-z ]+$' THEN s.Shift
                                       WHEN STR_TO_DATE(LEFT(s.Shift, 5), '%H:%i') > CURRENT_TIME() THEN 'Shift Not Started'
                                       WHEN i.AuxName IS NULL OR i.Starttime IS NULL THEN 'Absent'
                                       ELSE
                                           CASE
                                               WHEN LEFT(s.Shift, 5) = '00:00' AND i.Starttime > STR_TO_DATE(CONCAT(CURRENT_DATE, ' ', '00:00'), '%Y-%m-%d %H:%i') THEN 'Attended'
                                               WHEN STR_TO_DATE(CONCAT(CURRENT_DATE, ' ', LEFT(s.Shift, 5)), '%Y-%m-%d %H:%i') > i.Starttime THEN 'Attended'
                                               ELSE TIMEDIFF(i.Starttime, STR_TO_DATE(CONCAT(CURRENT_DATE, ' ', LEFT(s.Shift, 5)), '%Y-%m-%d %H:%i'))
                                           END
                                   END AS status
                               FROM schedule s
                               LEFT JOIN (
                                   SELECT TRIM(Email) AS Email, MIN(Starttime) AS MinStarttime
                                   FROM Attendancerecord
                                   WHERE AuxName = 'Online' AND DATE(Starttime) = CURDATE()
                                   GROUP BY TRIM(Email)
                               ) first_online ON TRIM(s.EmailSch) = first_online.Email
                               LEFT JOIN Attendancerecord i ON TRIM(s.EmailSch) = TRIM(i.Email) AND i.Starttime = first_online.MinStarttime
                               WHERE DATE(s.Date) = CURDATE()
                               AND (i.AuxName = 'Online' OR i.AuxName IS NULL)
                               ORDER BY TRIM(s.EmailSch), i.AuxName DESC, i.Starttime";

        // تطبيق فلترة الليدر بنفس طريقة attendancereport.php
        $filtered_sql = addLeaderFilterToSQL($base_absenteeism_sql, $user_permission, $user_email, 's.Leader');

        // تنفيذ الاستعلام وحساب الغائبين
        $result = $conn->query($filtered_sql);
        $absent_count = 0;
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                if ($row['status'] === 'Absent') {
                    $absent_count++;
                }
            }
        }
        $stats['absenteeism_count'] = $absent_count;

        // Vacation count - الموظفين الذين الشيفت بتاعهم vacation
        $vacation_sql = "SELECT COUNT(*) as count FROM schedule
                        WHERE Date = CURRENT_DATE
                        AND Shift = 'vacation'" . $leader_filter;
        $result = $conn->query($vacation_sql);
        $stats['vacation_count'] = ($result && $result->num_rows > 0) ? $result->fetch_assoc()['count'] : 0;

        // Distinct users on vacation - عدد الموظفين المختلفين في الإجازة
        $distinct_vacation_sql = "SELECT COUNT(DISTINCT EmailSch) as count FROM schedule
                                 WHERE Date = CURRENT_DATE AND Shift = 'vacation'" . $leader_filter;
        $result = $conn->query($distinct_vacation_sql);
        $stats['distinct_vacation_count'] = ($result && $result->num_rows > 0) ? $result->fetch_assoc()['count'] : 0;

        // Online agents count - الموظفين المتصلين حالياً (Online)
        $online_agents_sql = "SELECT COUNT(DISTINCT Email) as count FROM realtime
                             WHERE AuxName = 'Online'" .
                             ($filter_info['should_filter'] ? " AND Leader = '" . $conn->real_escape_string($filter_info['leader_name']) . "'" : "");
        $result = $conn->query($online_agents_sql);
        $stats['online_agents_count'] = ($result && $result->num_rows > 0) ? $result->fetch_assoc()['count'] : 0;

        // إضافة معلومات الفلترة للتتبع
        $stats['filter_applied'] = $filter_info['should_filter'];
        $stats['leader_name'] = $filter_info['leader_name'];

    } catch (Exception $e) {
        error_log("Error in getStatisticsWithLeaderFilter: " . $e->getMessage());
        // إرجاع قيم افتراضية في حالة الخطأ
        $stats = [
            'schedule_count' => 0,
            'absenteeism_count' => 0,
            'vacation_count' => 0,
            'distinct_vacation_count' => 0,
            'online_agents_count' => 0,
            'filter_applied' => false,
            'leader_name' => ''
        ];
    }

    return $stats;
}

/**
 * دالة للحصول على بيانات الوقت الفعلي مع فلترة الليدر
 * @param mysqli $conn اتصال قاعدة البيانات
 * @param string $type نوع البيانات (online, offline, B)
 * @param string $user_permission صلاحية المستخدم
 * @param string $user_email إيميل المستخدم
 * @return array
 */
function getRealtimeDataWithLeaderFilter($conn, $type, $user_permission, $user_email) {
    $counts = [
        'online' => 0,
        'offline' => 0,
        'B' => 0
    ];
    $data = [];

    try {
        // الحصول على معلومات الفلترة
        $filter_info = getLeaderFilterInfo($user_permission, $user_email);
        $leader_name = $filter_info['leader_name'];

        // بناء شرط WHERE للفلترة
        $where_leader = "";
        if ($filter_info['should_filter'] && !empty($leader_name)) {
            $where_leader = " AND Leader = '" . addslashes($leader_name) . "'";
        }

        // استعلامات الجلب لكل حالة
        $queries = [
            'online' => "SELECT Email, AuxName, Auxtime, Leader FROM realtime WHERE AuxName = 'online'" . $where_leader . " LIMIT 50",
            'offline' => "SELECT Email, AuxName, Auxtime, Leader FROM realtime WHERE AuxName = 'offline'" . $where_leader . " LIMIT 50",
            'B' => "SELECT Email, AuxName, Auxtime, Leader FROM realtime WHERE AuxName LIKE '%B%'" . $where_leader . " LIMIT 50"
        ];

        // استعلامات العد لكل حالة
        $count_queries = [
            'online' => "SELECT COUNT(*) AS count FROM realtime WHERE AuxName = 'online'" . $where_leader,
            'offline' => "SELECT COUNT(*) AS count FROM realtime WHERE AuxName = 'offline'" . $where_leader,
            'B' => "SELECT COUNT(*) AS count FROM realtime WHERE AuxName LIKE '%B%'" . $where_leader
        ];

        // جلب البيانات للحالة المطلوبة فقط
        if (array_key_exists($type, $queries)) {
            $result = $conn->query($queries[$type]);
            if ($result && $result->num_rows > 0) {
                while ($row = $result->fetch_assoc()) {
                    $data[] = $row;
                }
            }
        }

        // جلب الأعداد لجميع الحالات
        foreach ($count_queries as $key => $sql) {
            $count_result = $conn->query($sql);
            if ($count_result && $count_result->num_rows > 0) {
                $counts[$key] = (int)$count_result->fetch_assoc()['count'];
            }
        }

        // تسجيل للتتبع (في حالة التطوير)
        if (isset($_GET['debug']) && $_GET['debug'] == '1') {
            error_log("Realtime SQL for $type: " . ($queries[$type] ?? 'N/A'));
            error_log("Leader filter applied: " . ($filter_info['should_filter'] ? 'Yes' : 'No'));
            error_log("Leader name: " . $leader_name);
            error_log("Results count: " . count($data));
        }

    } catch (Exception $e) {
        error_log("Error in getRealtimeDataWithLeaderFilter: " . $e->getMessage());
    }

    return [
        'data' => $data,
        'counts' => $counts,
        'filter_applied' => $filter_info['should_filter'] ?? false,
        'leader_name' => $leader_name ?? '',
        'sql_used' => $queries[$type] ?? ''
    ];
}

/**
 * دالة لاختبار استخراج الأسماء من الإيميلات
 * @param array $test_emails مصفوفة الإيميلات للاختبار
 * @return array نتائج الاختبار
 */
function testEmailNameExtraction($test_emails = []) {
    if (empty($test_emails)) {
        $test_emails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            'invalid-email',
            ''
        ];
    }

    $results = [];
    foreach ($test_emails as $email) {
        $results[$email] = extractLeaderNameFromEmail($email);
    }

    return $results;
}

/**
 * دالة لعرض معلومات التتبع والتشخيص
 * @param string $user_permission
 * @param string $user_email
 * @return array
 */
function getLeaderFilterDebugInfo($user_permission, $user_email) {
    $filter_info = getLeaderFilterInfo($user_permission, $user_email);

    return [
        'user_permission' => $user_permission,
        'user_email' => $user_email,
        'extracted_name' => $filter_info['user_info'],
        'should_filter' => $filter_info['should_filter'],
        'leader_name' => $filter_info['leader_name'],
        'filter_condition' => $filter_info['filter_condition'],
        'timestamp' => date('Y-m-d H:i:s')
    ];
}
?>
