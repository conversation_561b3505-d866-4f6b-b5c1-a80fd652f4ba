/* ==================== SIMPLE PROFESSIONAL LEAVES CSS ==================== */

/* Import Root Variables */
@import url('root-variables.css');

/* ==================== GLOBAL STYLES ==================== */
.professional-body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, var(--primary-50) 0%, var(--secondary-100) 100%);
    color: var(--text-primary);
    margin: 0;
    padding: 0;
    min-height: 100vh;
    line-height: 1.6;
    font-size: 14px;
}

/* ==================== NAVBAR ==================== */
.professional-navbar {
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 50%, var(--primary-800) 100%);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    border-bottom: 3px solid var(--primary-500);
    position: sticky;
    top: 0;
    z-index: 1000;
    padding: 0;
}

.navbar-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 2rem;
}

.logo-container {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.professional-logo {
    height: 50px;
    width: auto;
    border-radius: var(--radius-lg);
}

.brand-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-white);
    margin: 0;
}

.brand-subtitle {
    font-size: 0.85rem;
    color: var(--primary-200);
    font-weight: 400;
}

.page-title-section {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
    justify-content: center;
}

.page-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--accent-main), var(--accent-dark));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.page-icon i {
    font-size: 1.8rem;
    color: var(--text-white);
}

.page-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-white);
    margin: 0;
}

.page-description {
    font-size: 0.9rem;
    color: var(--primary-200);
}

.user-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    background: linear-gradient(135deg, var(--success-500), var(--success-700));
    border-radius: 25px;
    color: var(--text-white);
    font-weight: 600;
}

/* Old btn-excel-export styles removed - using new ones below */

/* ==================== CONTAINER ==================== */
.professional-container {
    padding: 2rem 0;
    min-height: calc(100vh - 120px);
}

/* ==================== FILTERS ==================== */
.professional-filters-panel {
    background: var(--bg-white);
    border-radius: var(--radius-xl);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
    border: 1px solid var(--border-light);
}

.filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.6rem 1.2rem;
    background: linear-gradient(135deg, var(--primary-100), var(--primary-200));
    border-bottom: 1px solid var(--border-light);
}

.filters-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filters-title i {
    font-size: 0.8rem;
    color: var(--primary-600);
}

.filters-title h3 {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.btn-toggle-filters {
    background: var(--primary-500);
    border: none;
    border-radius: 50%;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-white);
    transition: all 0.3s ease;
    box-shadow: 0 2px 6px rgba(158, 144, 215, 0.3);
    font-size: 0.75rem;
}

.btn-toggle-filters:hover {
    background: var(--primary-600);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(158, 144, 215, 0.4);
}

.filters-form {
    padding: 1rem;
}

.filters-grid-compact {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.6rem;
    margin-bottom: 1rem;
}

.filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-label {
    display: flex;
    align-items: center;
    gap: 0.4rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.4rem;
    font-size: 0.8rem;
}

.professional-search-input,
.professional-select,
.professional-date-input {
    padding: 0.4rem 0.55rem;
    border: 2px solid var(--border-light);
    border-radius: var(--radius-lg);
    font-size: 0.75rem;
    background: var(--bg-white);
    transition: all 0.3s ease;
    width: 100%;
}

.professional-search-input:focus,
.professional-select:focus,
.professional-date-input:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(158, 144, 215, 0.15);
}

.filters-actions-bar {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-top: 0.8rem;
    border-top: 1px solid var(--border-light);
}

.filters-buttons {
    display: flex;
    gap: 0.8rem;
    align-items: center;
}

.btn-apply-filters,
.btn-reset-filters,
.btn-excel-export {
    display: flex;
    align-items: center;
    gap: 0.4rem;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: 0.8rem;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    min-width: 110px;
    justify-content: center;
}

.btn-apply-filters {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    color: var(--text-white);
    box-shadow: 0 4px 12px rgba(158, 144, 215, 0.3);
}

.btn-apply-filters:hover {
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(158, 144, 215, 0.4);
    color: var(--text-white);
    text-decoration: none;
}

.btn-reset-filters {
    background: var(--bg-white);
    color: var(--text-secondary);
    border: 2px solid var(--border-medium);
}

.btn-reset-filters:hover {
    background: var(--gray-100);
    color: var(--text-primary);
    border-color: var(--border-dark);
    text-decoration: none;
    transform: translateY(-2px);
}

.btn-excel-export {
    background: linear-gradient(135deg, var(--success-500), var(--success-600)) !important;
    color: #000000 !important;
    border: none !important;
}

.btn-excel-export:hover {
    background: linear-gradient(135deg, var(--success-600), var(--success-700)) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(34, 197, 94, 0.4) !important;
    color: #000000 !important;
    text-decoration: none !important;
}

.btn-excel-export,
.btn-excel-export * {
    color: #000000 !important;
}

.btn-excel-export:hover,
.btn-excel-export:hover * {
    color: #000000 !important;
}

/* Date Range Styles */
.date-range-inputs {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.date-range-inputs .professional-date-input {
    flex: 1;
    min-width: 0;
}

.date-separator {
    font-weight: 600;
    color: var(--text-secondary);
    font-size: 0.8rem;
    white-space: nowrap;
}

/* Compact Grid Responsive */
@media (max-width: 1200px) {
    .filters-grid-compact {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .filters-grid-compact {
        grid-template-columns: 1fr;
    }

    .filters-form {
        padding: 1rem;
    }

    .date-range-inputs {
        flex-direction: column;
        gap: 0.5rem;
    }

    .date-separator {
        display: none;
    }

    .filters-buttons {
        flex-direction: column;
        gap: 0.75rem;
        width: 100%;
    }

    .btn-apply-filters,
    .btn-reset-filters {
        width: 100%;
        min-width: auto;
    }
}

/* ==================== FULL WIDTH TABLE ==================== */
.professional-table-container {
    background: var(--bg-white);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    margin: 0;
    width: 100vw;
    margin-left: calc(-50vw + 50%);
    position: relative;
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    border-bottom: 1px solid var(--border-light);
    position: sticky;
    top: 0;
    z-index: 1000;
    color: var(--text-white);
}

.table-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.table-title i {
    font-size: 1.5rem;
    color: var(--text-white) !important;
}

.table-title h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-white) !important;
    margin: 0;
}

.table-subtitle {
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.9);
    margin-top: 0.25rem;
}

.table-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-table-action {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    cursor: pointer;
    font-size: 1rem;
    background: rgba(255, 255, 255, 0.2) !important;
    color: var(--text-white) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

.btn-table-action:hover {
    background: rgba(255, 255, 255, 0.3) !important;
    color: var(--text-white) !important;
    transform: translateY(-2px);
}

/* ==================== TABLE ==================== */
.table-wrapper {
    overflow-x: auto;
    overflow-y: visible;
    height: auto;
    min-height: calc(100vh - 200px);
    position: relative;
    width: 100%;
}

/* Override DataTables default styles - Simple */
.dataTables_wrapper .dataTables_scroll .dataTables_scrollHead {
    background: var(--primary-100) !important;
}

.dataTables_wrapper .dataTables_scroll .dataTables_scrollHead table {
    margin-bottom: 0 !important;
    table-layout: fixed !important;
    width: 100% !important;
}

.dataTables_wrapper .dataTables_scroll .dataTables_scrollHead table thead {
    background: var(--primary-100) !important;
}

.dataTables_wrapper .dataTables_scroll .dataTables_scrollHead table thead th {
    background: var(--primary-100) !important;
    border: 1px solid var(--border-light) !important;
    padding: 0 !important;
    box-sizing: border-box !important;
}

.dataTables_wrapper .dataTables_scroll .dataTables_scrollBody table {
    table-layout: fixed !important;
    width: 100% !important;
}

.dataTables_wrapper .dataTables_scroll .dataTables_scrollBody table tbody td {
    border: 1px solid var(--border-light) !important;
    padding: 0 !important;
    box-sizing: border-box !important;
    vertical-align: top !important;
}

.professional-table {
    width: 100% !important;
    min-width: 1900px !important;
    border-collapse: collapse !important;
    border-spacing: 0 !important;
    font-size: 0.75rem !important;
    margin: 0 !important;
    table-layout: fixed !important;
    background: var(--bg-white) !important;
    border: 1px solid var(--border-light) !important;
}

/* Table Header - Simple Design */
.professional-thead {
    background: var(--primary-100) !important;
    position: sticky;
    top: 0;
    z-index: 999;
    border-bottom: 1px solid var(--border-light);
}

.professional-thead tr {
    background: var(--primary-100) !important;
}

.professional-thead th {
    background: var(--primary-100) !important;
    border: none !important;
    padding: 0 !important;
}



.th-content {
    display: block;
    padding: 0.75rem 0.5rem;
    color: var(--text-primary) !important;
    font-weight: 600;
    font-size: 0.75rem;
    text-align: left;
    white-space: nowrap;
    border-right: 1px solid var(--border-light);
    background: var(--primary-100) !important;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    box-sizing: border-box;
}

.th-content i {
    color: var(--primary-600) !important;
    font-size: 0.7rem;
}

.th-content span {
    color: var(--text-primary) !important;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    font-size: 0.75rem;
}

/* Force table header visibility and alignment - Simple */
.professional-table thead,
.professional-table thead tr,
.professional-table thead th,
.professional-table thead .th-content {
    background: var(--primary-100) !important;
    color: var(--text-primary) !important;
    visibility: visible !important;
}

.professional-table thead th {
    border: 1px solid var(--border-light) !important;
    padding: 0 !important;
    margin: 0 !important;
    box-sizing: border-box !important;
}

.professional-table tbody td {
    border: 1px solid var(--border-light) !important;
    padding: 0 !important;
    margin: 0 !important;
    box-sizing: border-box !important;
    vertical-align: top !important;
}

/* DataTables specific overrides - Simple */
table.dataTable thead th,
table.dataTable thead td {
    background: var(--primary-100) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--border-light) !important;
    padding: 0 !important;
}

table.dataTable tbody td {
    border: 1px solid var(--border-light) !important;
    padding: 0 !important;
}

table.dataTable.no-footer {
    border-bottom: 1px solid var(--border-light) !important;
}

/* Ensure header is always visible - Simple */
.dataTables_scrollHead {
    background: var(--primary-100) !important;
}

.dataTables_scrollHead .professional-thead {
    background: var(--primary-100) !important;
}

/* Table Body */
.professional-tbody {
    background: var(--bg-white);
}

/* ==================== PAGINATION STYLING ==================== */
.dataTables_wrapper .dataTables_paginate {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    margin: 1.5rem 0 !important;
    padding: 0 !important;
}

.dataTables_wrapper .dataTables_info {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    margin: 1rem 0 !important;
    color: var(--text-secondary) !important;
    font-size: 0.85rem !important;
}

.dataTables_wrapper .dataTables_length {
    display: none !important;
}

.dataTables_wrapper .dataTables_filter {
    display: none !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    padding: 0.5rem 0.75rem !important;
    margin: 0 0.25rem !important;
    border: 1px solid var(--border-light) !important;
    border-radius: var(--radius-md) !important;
    background: var(--bg-white) !important;
    color: var(--text-primary) !important;
    text-decoration: none !important;
    transition: all 0.3s ease !important;
    font-size: 0.8rem !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background: var(--primary-100) !important;
    border-color: var(--primary-500) !important;
    color: var(--primary-700) !important;
    transform: translateY(-1px) !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background: var(--primary-500) !important;
    border-color: var(--primary-500) !important;
    color: var(--text-white) !important;
    font-weight: 600 !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
    background: var(--gray-100) !important;
    color: var(--text-tertiary) !important;
    cursor: not-allowed !important;
    opacity: 0.6 !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.disabled:hover {
    background: var(--gray-100) !important;
    border-color: var(--border-light) !important;
    color: var(--text-tertiary) !important;
    transform: none !important;
}

.professional-row {
    transition: all 0.3s ease;
    border-bottom: 1px solid var(--border-light);
}

.professional-row:hover {
    background: var(--primary-50);
}

.professional-row td {
    padding: 0;
    border: none;
    vertical-align: middle;
}

.cell-content {
    padding: 0.75rem 0.5rem;
    border-right: 1px solid var(--border-light);
    font-size: 0.75rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%;
    box-sizing: border-box;
}

/* Allow text wrapping for comment and description cells */
.td-comment .cell-content,
.td-description .cell-content {
    white-space: normal;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* Column Widths - Fixed for alignment */
.professional-table .th-code,
.professional-table .td-code {
    width: 120px !important;
    min-width: 120px !important;
    max-width: 120px !important;
}

.professional-table .th-email,
.professional-table .td-email {
    width: 180px !important;
    min-width: 180px !important;
    max-width: 180px !important;
}

.professional-table .th-date,
.professional-table .td-date {
    width: 110px !important;
    min-width: 110px !important;
    max-width: 110px !important;
}

.professional-table .th-days,
.professional-table .td-days {
    width: 70px !important;
    min-width: 70px !important;
    max-width: 70px !important;
}

.professional-table .th-status,
.professional-table .td-status {
    width: 110px !important;
    min-width: 110px !important;
    max-width: 110px !important;
}

.professional-table .th-approver,
.professional-table .td-approver {
    width: 130px !important;
    min-width: 130px !important;
    max-width: 130px !important;
}

.professional-table .th-comment,
.professional-table .td-comment {
    width: 180px !important;
    min-width: 180px !important;
    max-width: 180px !important;
}

.professional-table .th-request-date,
.professional-table .td-request-date {
    width: 110px !important;
    min-width: 110px !important;
    max-width: 110px !important;
}

.professional-table .th-leave-type,
.professional-table .td-leave-type {
    width: 120px !important;
    min-width: 120px !important;
    max-width: 120px !important;
}

.professional-table .th-attachment,
.professional-table .td-attachment {
    width: 90px !important;
    min-width: 90px !important;
    max-width: 90px !important;
}

.professional-table .th-description,
.professional-table .td-description {
    width: 140px !important;
    min-width: 140px !important;
    max-width: 140px !important;
}

.professional-table .th-seat,
.professional-table .td-seat {
    width: 70px !important;
    min-width: 70px !important;
    max-width: 70px !important;
}

.professional-table .th-language,
.professional-table .td-language {
    width: 90px !important;
    min-width: 90px !important;
    max-width: 90px !important;
}

.professional-table .th-module,
.professional-table .td-module {
    width: 90px !important;
    min-width: 90px !important;
    max-width: 90px !important;
}

.professional-table .th-shift,
.professional-table .td-shift {
    width: 110px !important;
    min-width: 110px !important;
    max-width: 110px !important;
}

.professional-table .th-leader,
.professional-table .td-leader {
    width: 120px !important;
    min-width: 120px !important;
    max-width: 120px !important;
}

.professional-table .th-leader-email,
.professional-table .td-leader-email {
    width: 160px !important;
    min-width: 160px !important;
    max-width: 160px !important;
}

.professional-table .th-actions,
.professional-table .td-actions {
    width: 180px !important;
    min-width: 180px !important;
    max-width: 180px !important;
}

/* Cell Styles */
.code-badge {
    background: var(--primary-200);
    color: var(--primary-700);
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius-md);
    font-weight: 500;
    font-size: 0.75rem;
    border: 1px solid var(--primary-300);
}

.status-badge {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.375rem 0.625rem;
    border-radius: var(--radius-md);
    font-weight: 500;
    font-size: 0.75rem;
}

.badge-success {
    background: var(--success-100);
    color: var(--success-700);
    border: 1px solid var(--success-300);
}

.badge-danger {
    background: var(--danger-100);
    color: var(--danger-700);
    border: 1px solid var(--danger-300);
}

.badge-warning {
    background: var(--warning-100);
    color: var(--warning-700);
    border: 1px solid var(--warning-300);
}

.professional-comment-input {
    flex: 1;
    padding: 0.375rem 0.5rem;
    border: 1px solid var(--border-light);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    background: var(--bg-white);
    transition: all 0.3s ease;
    min-width: 120px;
}

.professional-comment-input:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 2px rgba(158, 144, 215, 0.15);
}

/* ==================== ACTIONS CONTAINER ==================== */
.actions-container {
    display: flex;
    gap: 0.25rem;
    align-items: center;
    justify-content: center;
}

.btn-action {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.375rem 0.625rem;
    border: none;
    border-radius: var(--radius-sm);
    font-weight: 500;
    font-size: 0.7rem;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 0;
}

/* ==================== COMMENT CONTAINER ==================== */
.comment-container {
    display: flex;
    align-items: center;
    gap: 0.375rem;
    width: 100%;
}

.comment-actions {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.btn-save-comment {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    border: none;
    background: linear-gradient(135deg, var(--success-500), var(--success-600));
    color: #000000 !important;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.75rem;
    flex-shrink: 0;
    box-shadow: 0 2px 8px rgba(34, 197, 94, 0.3);
}

.btn-save-comment:hover {
    background: linear-gradient(135deg, var(--success-600), var(--success-700));
    transform: scale(1.05);
    color: #000000 !important;
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.4);
}

.btn-save-comment i {
    color: #000000 !important;
    font-weight: 900;
}

.btn-approve {
    background: var(--success-500);
    color: var(--text-white);
    border: 1px solid var(--success-600);
}

.btn-reject {
    background: var(--danger-500);
    color: var(--text-white);
    border: 1px solid var(--danger-600);
}

.btn-approve:hover {
    background: var(--success-600);
}

.btn-reject:hover {
    background: var(--danger-600);
}

/* ==================== STATS PANEL ==================== */
.professional-stats-panel {
    background: var(--bg-white);
    border-radius: var(--radius-xl);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    padding: 2rem;
    margin-bottom: 2rem;
    border: 1px solid var(--border-light);
    width: 100vw;
    margin-left: calc(-50vw + 50%);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.stat-card {
    background: linear-gradient(135deg, var(--bg-white), var(--secondary-100));
    border-radius: var(--radius-xl);
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border-light);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-4px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--text-white);
}

.stat-total .stat-icon {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
}

.stat-approved .stat-icon {
    background: linear-gradient(135deg, var(--success-500), var(--success-600));
}

.stat-rejected .stat-icon {
    background: linear-gradient(135deg, var(--danger-500), var(--danger-600));
}

.stat-pending .stat-icon {
    background: linear-gradient(135deg, var(--warning-500), var(--warning-600));
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    line-height: 1;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
    margin: 0.25rem 0 0 0;
}

/* ==================== NOTIFICATIONS ==================== */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    max-width: 350px;
    pointer-events: none;
}

.notification {
    background: var(--bg-white);
    border-radius: var(--radius-lg);
    padding: 1rem 1.25rem;
    margin-bottom: 0.75rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    border-left: 4px solid var(--primary-500);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    transform: translateX(400px);
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    pointer-events: auto;
    position: relative;
    overflow: hidden;
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification.success {
    border-left-color: var(--success-500);
    background: linear-gradient(135deg, var(--success-50), var(--bg-white));
}

.notification.error {
    border-left-color: var(--danger-500);
    background: linear-gradient(135deg, var(--danger-50), var(--bg-white));
}

.notification.warning {
    border-left-color: var(--warning-500);
    background: linear-gradient(135deg, var(--warning-50), var(--bg-white));
}

.notification.info {
    border-left-color: var(--info-500);
    background: linear-gradient(135deg, var(--info-50), var(--bg-white));
}

.notification-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    color: var(--text-white);
    flex-shrink: 0;
}

.notification.success .notification-icon {
    background: var(--success-500);
}

.notification.error .notification-icon {
    background: var(--danger-500);
}

.notification.warning .notification-icon {
    background: var(--warning-500);
}

.notification.info .notification-icon {
    background: var(--info-500);
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    font-size: 0.875rem;
    color: var(--text-primary);
    margin: 0 0 0.25rem 0;
}

.notification-message {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin: 0;
    line-height: 1.4;
}

.notification-close {
    width: 20px;
    height: 20px;
    border: none;
    background: none;
    color: var(--text-tertiary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
    font-size: 0.75rem;
    flex-shrink: 0;
}

.notification-close:hover {
    background: rgba(0, 0, 0, 0.1);
    color: var(--text-primary);
}

.notification-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: var(--primary-500);
    border-radius: 0 0 var(--radius-lg) var(--radius-lg);
    animation: notificationProgress 4s linear forwards;
}

.notification.success .notification-progress {
    background: var(--success-500);
}

.notification.error .notification-progress {
    background: var(--danger-500);
}

.notification.warning .notification-progress {
    background: var(--warning-500);
}

.notification.info .notification-progress {
    background: var(--info-500);
}

@keyframes notificationProgress {
    from {
        width: 100%;
    }
    to {
        width: 0%;
    }
}

/* ==================== FLOATING ACTIONS ==================== */
.professional-floating-actions {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    z-index: 1000;
}

.floating-action-group {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.floating-btn {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: var(--text-white);
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.floating-btn:hover {
    transform: translateY(-4px) scale(1.05);
}

.floating-btn-primary {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
}

.floating-btn-success {
    background: linear-gradient(135deg, var(--success-500), var(--success-600));
}

.floating-btn-info {
    background: linear-gradient(135deg, var(--info-500), var(--info-600));
}

.floating-btn-warning {
    background: linear-gradient(135deg, var(--warning-500), var(--warning-600));
}

/* ==================== SCROLLBAR ==================== */
.table-wrapper::-webkit-scrollbar {
    height: 12px;
    width: 12px;
}

.table-wrapper::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: 6px;
}

.table-wrapper::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    border-radius: 6px;
    border: 2px solid var(--gray-100);
}

.table-wrapper::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
}

/* ==================== RESPONSIVE ==================== */
@media (max-width: 768px) {
    .professional-table-container {
        margin-left: 0;
        width: 100%;
    }

    .professional-stats-panel {
        margin-left: 0;
        width: 100%;
        padding: 1.5rem 1rem;
    }

    .navbar-content {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
    }

    .filters-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .table-wrapper {
        height: auto;
        min-height: calc(100vh - 200px);
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .professional-table {
        min-width: 1000px;
    }

    .floating-btn {
        width: 48px;
        height: 48px;
        font-size: 1.1rem;
    }
}
