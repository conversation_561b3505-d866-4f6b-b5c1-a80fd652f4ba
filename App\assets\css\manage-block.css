  @import url('root-variables.css');


        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: var(--light-bg);
            color: var(--dark-text);
        }

        .admin-nav {
            background-color: var(--primary-color);
            padding: 1rem 0;
            box-shadow: var(--shadow-md);
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
        }

        .admin-nav ul {
            list-style: none;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            gap: 2rem;
        }

        .admin-nav a {
            color: var(--light-text);
            text-decoration: none;
            padding: 0.75rem 1.25rem;
            border-radius: 6px;
            transition: var(--transition);
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .admin-nav a:hover, .admin-nav a.active {
            background-color: var(--accent-color);
            color: white;
            transform: translateY(-1px);
        }

        .admin-nav a i {
            font-size: 1.1rem;
        }

        .main-container {
            margin-top: 80px;
            padding: 2rem;
        }

        .admin-content {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: var(--shadow-md);
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid var(--border-color);
        }

        .page-header h1 {
            color: var(--primary-color);
            margin: 0;
            font-size: 2rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .header-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .badge {
            padding: 0.5rem 1rem;
            font-weight: 500;
            border-radius: 20px;
            font-size: 0.9rem;
        }

        .alert {
            padding: 1.25rem;
            margin-bottom: 1.5rem;
            border-radius: 8px;
            border: none;
            box-shadow: var(--shadow-sm);
            display: flex;
            align-items: center;
            gap: 1rem;
            opacity: 1;
            transition: opacity 0.3s ease;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border-left: 4px solid var(--success-color);
        }

        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border-left: 4px solid var(--danger-color);
        }

        .alert i {
            font-size: 1.5rem;
        }

        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            background-color: var(--light-bg);
            border-radius: 12px;
            margin: 2rem 0;
        }

        .empty-state i {
            font-size: 4rem;
            color: var(--accent-color);
            margin-bottom: 1.5rem;
        }

        .empty-state h3 {
            color: var(--primary-color);
            margin-bottom: 1rem;
            font-weight: 600;
        }

        .empty-state p {
            color: var(--secondary-color);
            font-size: 1.1rem;
            margin: 0;
        }

        .table-container {
            overflow-x: auto;
            margin-top: 1.5rem;
            border-radius: 8px;
            box-shadow: var(--shadow-sm);
            background-color: white;
        }

        .table {
            width: 100%;
            margin-bottom: 0;
            border-collapse: separate;
            border-spacing: 0;
        }

        .table th {
            background-color: var(--light-bg);
            color: var(--primary-color);
            font-weight: 600;
            padding: 1rem;
            border-bottom: 2px solid var(--border-color);
            white-space: nowrap;
        }

        .table td {
            padding: 1rem;
            vertical-align: middle;
            border-bottom: 1px solid var(--border-color);
        }

        .table tbody tr:hover {
            background-color: var(--light-bg);
        }

        .badge {
            padding: 0.5rem 1rem;
            font-weight: 500;
            border-radius: 20px;
            font-size: 0.85rem;
        }

        .badge.bg-danger {
            background-color: #fee2e2 !important;
            color: #dc2626;
        }

        .badge.bg-info {
            background-color: #e0f2fe !important;
            color: #0284c7;
        }

        .btn {
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: var(--transition);
            border: none;
            cursor: pointer;
        }

        .btn-success {
            background-color: var(--success-color);
            color: white;
        }

        .btn-success:hover {
            background-color: #27ae60;
            transform: translateY(-1px);
        }

        .btn i {
            font-size: 0.9rem;
        }

        .d-flex {
            display: flex;
            align-items: center;
        }

        .text-primary {
            color: var(--accent-color) !important;
        }

        .text-muted {
            color: #6c757d !important;
        }

        @media (max-width: 768px) {
            .admin-nav ul {
                flex-direction: column;
                align-items: center;
                gap: 0.5rem;
            }

            .admin-nav {
                padding: 0.5rem 0;
            }

            .main-container {
                margin-top: 200px;
                padding: 1rem;
            }

            .admin-content {
                padding: 1rem;
            }

            .page-header {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }

            .table th, .table td {
                padding: 0.75rem;
            }

            .badge {
                padding: 0.35rem 0.75rem;
                font-size: 0.8rem;
            }
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .alert, .table-container, .empty-state {
            animation: fadeIn 0.3s ease-out;
        }

        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--light-bg);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--accent-color);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--primary-color);
        }