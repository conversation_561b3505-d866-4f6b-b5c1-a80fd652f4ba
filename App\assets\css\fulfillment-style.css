@import url('root-variables.css');

/* ==================== ROOT VARIABLES & BASE STYLES ==================== */

/* ==================== INTERPRETER STYLES ==================== */
body.interpreter-body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: var(--interpreter-text-color);
    background-color: var(--interpreter-light-bg);
    padding: 20px;
}

.interpreter-heading {
    color: var(--interpreter-primary-darker);
    margin-bottom: 25px;
    font-weight: 600;
    border-bottom: 2px solid var(--interpreter-primary-light);
    padding-bottom: 10px;
}

.interpreter-filter-container {
    background-color: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 30px;
    box-shadow: 0 4px 12px rgba(203, 164, 203, 0.1);
}

.interpreter-table-wrapper {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(203, 164, 203, 0.1);
}

.interpreter-filter-toolbar {
    background-color: var(--interpreter-primary-light);
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: center;
}

.interpreter-filter-group {
    display: flex;
    align-items: center;
    gap: 8px;
    background-color: white;
    padding: 8px 12px;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.interpreter-filter-group label {
    margin-bottom: 0;
    font-weight: 500;
    color: var(--interpreter-primary-darker);
    white-space: nowrap;
}

.interpreter-search-box {
    position: relative;
    flex-grow: 1;
    min-width: 300px;
}

.interpreter-search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--interpreter-primary-dark);
}

.interpreter-search-box .form-control {
    padding-left: 40px;
    border-radius: 6px;
    border: 1px solid var(--interpreter-primary-light);
}

.interpreter-search-box .form-control:focus {
    border-color: var(--interpreter-primary-color);
    box-shadow: 0 0 0 0.25rem rgba(203, 164, 203, 0.25);
}

#scheduleTable.interpreter-table th {
    background-color: var(--interpreter-primary-color);
    color: white;
    position: sticky;
    top: 0;
}

#scheduleTable.interpreter-table td {
    vertical-align: middle;
}

#scheduleTable.interpreter-table thead th:first-child,
#scheduleTable.interpreter-table tbody td:first-child {
    position: sticky;
    left: 0;
    z-index: 1;
    background-color: white;
}

#scheduleTable.interpreter-table thead th:first-child {
    z-index: 2;
    background-color: var(--interpreter-primary-dark);
}

.interpreter-dataTables_length {
    display: none;
}

.interpreter-dataTables_scrollBody::-webkit-scrollbar {
    height: 8px;
}

.interpreter-dataTables_scrollBody::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.interpreter-dataTables_scrollBody::-webkit-scrollbar-thumb {
    background: var(--interpreter-primary-color);
    border-radius: 4px;
}

.interpreter-btn-export {
    background-color: var(--interpreter-primary-dark);
    border-color: var(--interpreter-primary-dark);
    color: white;
}

.interpreter-btn-export:hover {
    background-color: var(--interpreter-primary-darker);
    border-color: var(--interpreter-primary-darker);
}

.interpreter-fulfillment-high {
    color: #28a745;
    font-weight: bold;
}
.interpreter-fulfillment-medium {
    color: #ffc107;
    font-weight: bold;
}
.interpreter-fulfillment-low {
    color: #dc3545;
    font-weight: bold;
}

.interpreter-occupancy-high {
    color: #28a745;
    font-weight: bold;
}
.interpreter-occupancy-medium {
    color: #ffc107;
    font-weight: bold;
}
.interpreter-occupancy-low {
    color: #dc3545;
    font-weight: bold;
}

.interpreter-overnight-shift {
    background-color: #f0f8ff;
}

.interpreter-late-shift {
    background-color: #fff8f0;
}

.interpreter-late-arrival {
    border-left: 4px solid #dc3545;
}

@media (max-width: 768px) {
    .interpreter-filter-toolbar {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
    
    .interpreter-filter-group {
        width: 100%;
    }
    
    .interpreter-search-box {
        min-width: 100%;
    }
}

/* ==================== INTERPRETER MTD STYLES ==================== */
body.interpreterMTD-body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: var(--interpreterMTD-text-color);
    background-color: var(--interpreterMTD-light-bg);
    padding: 20px;
}

.interpreterMTD-heading {
    color: var(--interpreterMTD-primary-darker);
    margin-bottom: 25px;
    font-weight: 600;
    border-bottom: 2px solid var(--interpreterMTD-primary-light);
    padding-bottom: 10px;
}

.interpreterMTD-filter-container {
    background-color: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    box-shadow: 0 4px 12px rgba(203, 164, 203, 0.1);
}

.interpreterMTD-table-wrapper {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(203, 164, 203, 0.1);
}

.interpreterMTD-filter-toolbar {
    background-color: var(--interpreterMTD-primary-light);
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: center;
}

.interpreterMTD-filter-group {
    display: flex;
    align-items: center;
    gap: 8px;
    background-color: white;
    padding: 8px 12px;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.interpreterMTD-filter-group label {
    margin-bottom: 0;
    font-weight: 500;
    color: var(--interpreterMTD-primary-darker);
    white-space: nowrap;
}

.interpreterMTD-search-box {
    position: relative;
    flex-grow: 1;
    min-width: 300px;
}

.interpreterMTD-search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--interpreterMTD-primary-dark);
}

.interpreterMTD-search-box .form-control {
    padding-left: 40px;
    border-radius: 6px;
    border: 1px solid var(--interpreterMTD-primary-light);
}

.interpreterMTD-search-box .form-control:focus {
    border-color: var(--interpreterMTD-primary-color);
    box-shadow: 0 0 0 0.25rem rgba(203, 164, 203, 0.25);
}

#performanceTable.interpreterMTD-table th {
    background-color: var(--interpreterMTD-primary-color);
    color: white;
    position: sticky;
    top: 0;
}

#performanceTable.interpreterMTD-table td {
    vertical-align: middle;
}

#performanceTable.interpreterMTD-table thead th:first-child,
#performanceTable.interpreterMTD-table tbody td:first-child {
    position: sticky;
    left: 0;
    z-index: 1;
    background-color: white;
}

#performanceTable.interpreterMTD-table thead th:first-child {
    z-index: 2;
    background-color: var(--interpreterMTD-primary-dark);
}

.interpreterMTD-dataTables_length {
    display: none;
}

.interpreterMTD-dataTables_scrollBody::-webkit-scrollbar {
    height: 8px;
}

.interpreterMTD-dataTables_scrollBody::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.interpreterMTD-dataTables_scrollBody::-webkit-scrollbar-thumb {
    background: var(--interpreterMTD-primary-color);
    border-radius: 4px;
}

.interpreterMTD-btn-export {
    background-color: var(--interpreterMTD-primary-dark);
    border-color: var(--interpreterMTD-primary-dark);
    color: white;
}

.interpreterMTD-btn-export:hover {
    background-color: var(--interpreterMTD-primary-darker);
    border-color: var(--interpreterMTD-primary-darker);
}

.interpreterMTD-fulfillment-high {
    color: #28a745;
    font-weight: bold;
}
.interpreterMTD-fulfillment-medium {
    color: #ffc107;
    font-weight: bold;
}
.interpreterMTD-fulfillment-low {
    color: #dc3545;
    font-weight: bold;
}

.interpreterMTD-occupancy-high {
    color: #28a745;
    font-weight: bold;
}
.interpreterMTD-occupancy-medium {
    color: #ffc107;
    font-weight: bold;
}
.interpreterMTD-occupancy-low {
    color: #dc3545;
    font-weight: bold;
}

.interpreterMTD-btn-details:hover {
    color: var(--interpreterMTD-primary-darker);
}

@media (max-width: 768px) {
    .interpreterMTD-filter-toolbar {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
    
    .interpreterMTD-filter-group {
        width: 100%;
    }
    
    .interpreterMTD-search-box {
        min-width: 100%;
    }
}

/* ==================== LEADER STYLES ==================== */
body.leader-body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: var(--leader-text-color);
    background-color: var(--leader-light-bg);
    padding: 20px;
}

.leader-heading {
    color: var(--leader-primary-darker);
    margin-bottom: 25px;
    font-weight: 600;
    border-bottom: 2px solid var(--leader-primary-light);
    padding-bottom: 10px;
}

.leader-filter-container {
    background-color: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 30px;
    box-shadow: 0 4px 12px rgba(203, 164, 203, 0.1);
}

.leader-table-wrapper {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(203, 164, 203, 0.1);
}

.leader-filter-toolbar {
    background-color: var(--leader-primary-light);
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: center;
}

.leader-filter-group {
    display: flex;
    align-items: center;
    gap: 8px;
    background-color: white;
    padding: 8px 12px;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.leader-filter-group label {
    margin-bottom: 0;
    font-weight: 500;
    color: var(--leader-primary-darker);
    white-space: nowrap;
}

.leader-search-box {
    position: relative;
    flex-grow: 1;
    min-width: 300px;
}

.leader-search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--leader-primary-dark);
}

.leader-search-box .form-control {
    padding-left: 40px;
    border-radius: 6px;
    border: 1px solid var(--leader-primary-light);
}

.leader-search-box .form-control:focus {
    border-color: var(--leader-primary-color);
    box-shadow: 0 0 0 0.25rem rgba(203, 164, 203, 0.25);
}

#performanceTable.leader-table th {
    background-color: var(--leader-primary-color);
    color: white;
    position: sticky;
    top: 0;
}

#performanceTable.leader-table td {
    vertical-align: middle;
}

#performanceTable.leader-table thead th:first-child,
#performanceTable.leader-table tbody td:first-child {
    position: sticky;
    left: 0;
    z-index: 1;
    background-color: white;
}

#performanceTable.leader-table thead th:first-child {
    z-index: 2;
    background-color: var(--leader-primary-dark);
}

.leader-dataTables_length {
    display: none;
}

.leader-dataTables_scrollBody::-webkit-scrollbar {
    height: 8px;
}

.leader-dataTables_scrollBody::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.leader-dataTables_scrollBody::-webkit-scrollbar-thumb {
    background: var(--leader-primary-color);
    border-radius: 4px;
}

.leader-btn-export {
    background-color: var(--leader-primary-dark);
    border-color: var(--leader-primary-dark);
    color: white;
}

.leader-btn-export:hover {
    background-color: var(--leader-primary-darker);
    border-color: var(--leader-primary-darker);
}

.leader-fulfillment-high {
    color: #28a745;
    font-weight: bold;
}
.leader-fulfillment-medium {
    color: #ffc107;
    font-weight: bold;
}
.leader-fulfillment-low {
    color: #dc3545;
    font-weight: bold;
}

.leader-occupancy-high {
    color: #28a745;
    font-weight: bold;
}
.leader-occupancy-medium {
    color: #ffc107;
    font-weight: bold;
}
.leader-occupancy-low {
    color: #dc3545;
    font-weight: bold;
}

@media (max-width: 768px) {
    .leader-filter-toolbar {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
    
    .leader-filter-group {
        width: 100%;
    }
    
    .leader-search-box {
        min-width: 100%;
    }
}

/* ==================== FULFILLMENT PAGE STYLES ==================== */
.fulfillmentpage-body {
    font-family: 'Tajawal', sans-serif;
    background-color: var(--fulfillmentpage-bg-light);
    color: var(--fulfillmentpage-text-dark);
}

.fulfillmentpage-navbar-main {
    background-color: var(--fulfillmentpage-bg-card);
    box-shadow: 0 1px 15px rgba(0, 0, 0, 0.05);
    padding: 0.75rem 1.5rem;
}

.fulfillmentpage-navbar-brand {
    font-weight: 700;
    color: var(--fulfillmentpage-primary-darkest);
    font-size: 1.5rem;
}

.fulfillmentpage-nav-link {
    color: var(--fulfillmentpage-text-dark);
    font-weight: 500;
    padding: 0.5rem 1.25rem;
    margin: 0 0.25rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
}

.fulfillmentpage-nav-link:hover, .fulfillmentpage-nav-link.active {
    background-color: var(--fulfillmentpage-primary-light);
    color: var(--fulfillmentpage-primary-darkest);
}

.fulfillmentpage-nav-link i {
    margin-left: 0.5rem;
}

.fulfillmentpage-analytics-card {
    border: none;
    border-radius: 0.75rem;
    background-color: var(--fulfillmentpage-bg-card);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.03);
    transition: transform 0.3s ease;
    overflow: hidden;
    height: 100%;
}
 
.fulfillmentpage-analytics-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
}

.fulfillmentpage-analytics-card .card-header {
    background-color: var(--fulfillmentpage-primary-color);
    color: white;
    font-weight: 600;
    padding: 0.75rem 1.25rem;
    border-bottom: none;
}

.fulfillmentpage-analytics-card .card-body {
    padding: 1.25rem;
}

.fulfillmentpage-data-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f1f1f1;
}

.fulfillmentpage-data-item:last-child {
    border-bottom: none;
}

.fulfillmentpage-data-label {
    color: var(--fulfillmentpage-text-light);
    font-weight: 500;
}

.fulfillmentpage-data-value {
    font-weight: 600;
    color: var(--fulfillmentpage-primary-darkest);
}

.fulfillmentpage-badge-custom {
    background-color: var(--fulfillmentpage-primary-light);
    color: var(--fulfillmentpage-primary-darkest);
    font-weight: 600;
    padding: 0.35rem 0.75rem;
}

.fulfillmentpage-status-badge {
    padding: 0.35rem 0.75rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
}

.fulfillmentpage-status-badge.completed {
    background-color: #d1fae5;
    color: #065f46;
}

.fulfillmentpage-status-badge.absent {
    background-color: #fee2e2;
    color: #b91c1c;
}

.fulfillmentpage-status-badge.off {
    background-color: #e0e7ff;
    color: #4338ca;
}

.fulfillmentpage-status-badge.sick {
    background-color: #fce7f3;
    color: #831843;
}

.fulfillmentpage-status-badge.vacation {
    background-color: #ecfdf5;
    color: #047857;
}

.fulfillmentpage-chart-container {
    background-color: var(--fulfillmentpage-bg-card);
    border-radius: 0.75rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.03);
    padding: 1.25rem;
    height: 100%;
}

.fulfillmentpage-chart-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--fulfillmentpage-primary-darkest);
    margin-bottom: 1.25rem;
    display: flex;
    align-items: center;
}

.fulfillmentpage-chart-title i {
    margin-left: 0.75rem;
    color: var(--fulfillmentpage-primary-color);
}

.fulfillmentpage-data-table {
    background-color: var(--fulfillmentpage-bg-card);
    border-radius: 0.75rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.03);
    overflow: hidden;
}

.fulfillmentpage-data-table thead th {
    background-color: var(--fulfillmentpage-primary-color);
    color: white;
    font-weight: 600;
    padding: 1rem 1.25rem;
    border: none;
}

.fulfillmentpage-data-table tbody tr {
    transition: background-color 0.2s ease;
}

.fulfillmentpage-data-table tbody tr:hover {
    background-color: var(--fulfillmentpage-primary-light);
}

.fulfillmentpage-data-table tbody td {
    padding: 0.875rem 1.25rem;
    vertical-align: middle;
    border-top: 1px solid #f1f1f1;
}

.fulfillmentpage-date-filter {
    background-color: var(--fulfillmentpage-bg-card);
    border-radius: 0.75rem;
    padding: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.03);
}

.fulfillmentpage-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1.5rem;
}

.fulfillmentpage-stat-card {
    background-color: var(--fulfillmentpage-bg-card);
    border-radius: 0.75rem;
    padding: 1rem;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
    text-align: center;
}

.fulfillmentpage-stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--fulfillmentpage-primary-darkest);
    margin-bottom: 0.25rem;
}

.fulfillmentpage-stat-label {
    font-size: 0.85rem;
    color: var(--fulfillmentpage-text-light);
}

.fulfillmentpage-stat-icon {
    font-size: 1.75rem;
    margin-bottom: 0.5rem;
    color: var(--fulfillmentpage-primary-color);
}