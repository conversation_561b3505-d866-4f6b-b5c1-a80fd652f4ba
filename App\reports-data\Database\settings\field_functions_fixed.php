<?php
/**
 * Fixed Field Functions - Working implementations
 */

// Load field configuration
function loadFieldConfiguration() {
    $configFile = __DIR__ . '/field_config.json';
    
    if (!file_exists($configFile)) {
        // Create default configuration if file doesn't exist
        $defaultConfig = [
            'basic_info' => [
                'title' => 'Basic Information',
                'icon' => 'fa-user',
                'fields' => []
            ]
        ];
        file_put_contents($configFile, json_encode($defaultConfig, JSON_PRETTY_PRINT));
        return $defaultConfig;
    }
    
    $content = file_get_contents($configFile);
    $config = json_decode($content, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        error_log("JSON decode error in field configuration: " . json_last_error_msg());
        return [];
    }
    
    return $config ?: [];
}

// Save field configuration
function saveFieldConfiguration() {
    global $conn, $email;
    
    try {
        $configFile = __DIR__ . '/field_config.json';
        
        // Check if file is writable
        if (file_exists($configFile) && !is_writable($configFile)) {
            return ['success' => false, 'message' => 'Configuration file is not writable. Check permissions.'];
        }
        
        if (!file_exists($configFile) && !is_writable(dirname($configFile))) {
            return ['success' => false, 'message' => 'Configuration directory is not writable. Check permissions.'];
        }
        
        // Get the configuration from POST data
        $config = [];
        
        if (isset($_POST['sections'])) {
            foreach ($_POST['sections'] as $sectionKey => $sectionData) {
                $config[$sectionKey] = [
                    'title' => $sectionData['title'] ?? ucwords(str_replace('_', ' ', $sectionKey)),
                    'icon' => $sectionData['icon'] ?? 'fa-cog',
                    'fields' => []
                ];
                
                if (isset($_POST['fields'][$sectionKey])) {
                    foreach ($_POST['fields'][$sectionKey] as $fieldName => $fieldData) {
                        $config[$sectionKey]['fields'][$fieldName] = [
                            'enabled' => isset($fieldData['enabled']) ? '1' : '0',
                            'type' => $fieldData['type'] ?? 'text',
                            'col' => $fieldData['col'] ?? '6',
                            'icon' => $fieldData['icon'] ?? 'fa-edit',
                            'source_type' => $fieldData['source_type'] ?? 'custom',
                            'options' => $fieldData['options'] ?? [],
                            'label' => $fieldData['label'] ?? ucwords(str_replace('_', ' ', $fieldName))
                        ];
                    }
                }
            }
        }
        
        // Save configuration
        $result = file_put_contents($configFile, json_encode($config, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        
        if ($result === false) {
            return ['success' => false, 'message' => 'Failed to write configuration file'];
        }
        
        // Log the save action
        if (isset($conn) && isset($email)) {
            $auditQuery = "INSERT INTO audit_log (
                record_id, changed_by, change_date, field_name,
                old_value, new_value, action, updated_from
            ) VALUES (
                'field_config', ?, NOW(), 'Field Configuration',
                '', 'Configuration saved', 'Save', 'field_settings'
            )";
            
            $stmt = $conn->prepare($auditQuery);
            if ($stmt) {
                $stmt->bind_param("s", $email);
                $stmt->execute();
            }
        }
        
        return ['success' => true, 'message' => 'Field configuration saved successfully'];
        
    } catch (Exception $e) {
        error_log("Error saving field configuration: " . $e->getMessage());
        return ['success' => false, 'message' => 'Error saving configuration: ' . $e->getMessage()];
    }
}

// Sync with database - FIXED VERSION
function syncWithDatabase() {
    global $conn, $email;
    
    try {
        // Get current database columns
        $result = $conn->query("DESCRIBE databasehc");
        if (!$result) {
            return ['success' => false, 'message' => 'Failed to get database structure: ' . $conn->error];
        }
        
        $dbColumns = [];
        while ($row = $result->fetch_assoc()) {
            $dbColumns[$row['Field']] = [
                'type' => $row['Type'],
                'null' => $row['Null'],
                'key' => $row['Key'],
                'default' => $row['Default'],
                'extra' => $row['Extra']
            ];
        }
        
        // Load current configuration
        $config = loadFieldConfiguration();
        
        // Get all currently configured fields
        $configuredFields = [];
        foreach ($config as $sectionKey => $section) {
            if (isset($section['fields'])) {
                foreach ($section['fields'] as $fieldName => $fieldConfig) {
                    $configuredFields[] = $fieldName;
                }
            }
        }
        
        // Find new fields (in database but not in config)
        $newFields = array_diff(array_keys($dbColumns), $configuredFields);
        
        // Add new fields to appropriate sections
        $addedCount = 0;
        foreach ($newFields as $fieldName) {
            // Skip system fields
            if (in_array($fieldName, ['ID', 'created_at', 'updated_at'])) {
                continue;
            }
            
            $section = determineBestSection($fieldName, $dbColumns[$fieldName]);
            
            // Ensure section exists
            if (!isset($config[$section])) {
                $config[$section] = [
                    'title' => ucwords(str_replace('_', ' ', $section)),
                    'icon' => 'fa-cog',
                    'fields' => []
                ];
            }
            
            // Add field to section
            $config[$section]['fields'][$fieldName] = [
                'enabled' => '1',
                'type' => mapDatabaseTypeToInputType($dbColumns[$fieldName]['type']),
                'col' => '6',
                'icon' => getFieldIcon($fieldName),
                'source_type' => 'database',
                'options' => [],
                'label' => ucwords(str_replace('_', ' ', $fieldName))
            ];
            
            $addedCount++;
        }
        
        // Save updated configuration
        $configFile = __DIR__ . '/field_config.json';
        $result = file_put_contents($configFile, json_encode($config, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        
        if ($result === false) {
            return ['success' => false, 'message' => 'Failed to save updated configuration'];
        }
        
        // Log the sync
        if (isset($email)) {
            $auditQuery = "INSERT INTO audit_log (
                record_id, changed_by, change_date, field_name,
                old_value, new_value, action, updated_from
            ) VALUES (
                'field_config', ?, NOW(), 'Field Configuration',
                '', ?, 'Sync', 'field_settings'
            )";
            
            $stmt = $conn->prepare($auditQuery);
            if ($stmt) {
                $message = "Synced with database: added $addedCount new fields";
                $stmt->bind_param("ss", $email, $message);
                $stmt->execute();
            }
        }
        
        return ['success' => true, 'message' => "Successfully synced with database. Added $addedCount new fields."];
        
    } catch (Exception $e) {
        error_log("Error syncing with database: " . $e->getMessage());
        return ['success' => false, 'message' => 'Error syncing with database: ' . $e->getMessage()];
    }
}

// Bulk delete fields - FIXED VERSION
function bulkDeleteFields() {
    global $conn, $email;
    
    try {
        $selectedFields = $_POST['selected_fields'] ?? [];
        
        if (empty($selectedFields)) {
            return ['success' => false, 'message' => 'No fields selected for deletion'];
        }
        
        // Load current configuration
        $config = loadFieldConfiguration();
        
        $deletedCount = 0;
        $deletedFields = [];
        
        foreach ($selectedFields as $fieldIdentifier) {
            // Field identifier format: "section|fieldname"
            $parts = explode('|', $fieldIdentifier);
            if (count($parts) !== 2) continue;
            
            $sectionKey = $parts[0];
            $fieldName = $parts[1];
            
            if (isset($config[$sectionKey]['fields'][$fieldName])) {
                unset($config[$sectionKey]['fields'][$fieldName]);
                $deletedFields[] = $fieldName;
                $deletedCount++;
                
                // Remove section if empty
                if (empty($config[$sectionKey]['fields'])) {
                    unset($config[$sectionKey]);
                }
            }
        }
        
        if ($deletedCount > 0) {
            // Save updated configuration
            $configFile = __DIR__ . '/field_config.json';
            $result = file_put_contents($configFile, json_encode($config, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
            
            if ($result === false) {
                return ['success' => false, 'message' => 'Failed to save configuration after deletion'];
            }
            
            // Log the deletion
            if (isset($email)) {
                $auditQuery = "INSERT INTO audit_log (
                    record_id, changed_by, change_date, field_name,
                    old_value, new_value, action, updated_from
                ) VALUES (
                    'field_config', ?, NOW(), 'Field Configuration',
                    ?, '', 'Bulk Delete', 'field_settings'
                )";
                
                $stmt = $conn->prepare($auditQuery);
                if ($stmt) {
                    $deletedList = implode(', ', $deletedFields);
                    $stmt->bind_param("ss", $email, $deletedList);
                    $stmt->execute();
                }
            }
            
            return ['success' => true, 'message' => "Successfully deleted $deletedCount fields: " . implode(', ', $deletedFields)];
        } else {
            return ['success' => false, 'message' => 'No fields were deleted'];
        }
        
    } catch (Exception $e) {
        error_log("Error in bulk delete: " . $e->getMessage());
        return ['success' => false, 'message' => 'Error deleting fields: ' . $e->getMessage()];
    }
}

// Helper functions
function determineBestSection($fieldName, $fieldInfo) {
    $fieldLower = strtolower($fieldName);
    
    if (strpos($fieldLower, 'email') !== false) return 'contact_info';
    if (strpos($fieldLower, 'phone') !== false) return 'contact_info';
    if (strpos($fieldLower, 'address') !== false) return 'contact_info';
    if (strpos($fieldLower, 'date') !== false) return 'dates';
    if (strpos($fieldLower, 'status') !== false) return 'work_details';
    if (strpos($fieldLower, 'language') !== false) return 'language_info';
    if (strpos($fieldLower, 'name') !== false) return 'basic_info';
    
    return 'additional';
}

function mapDatabaseTypeToInputType($dbType) {
    $dbType = strtolower($dbType);
    
    if (strpos($dbType, 'text') !== false) return 'textarea';
    if (strpos($dbType, 'date') !== false) return 'date';
    if (strpos($dbType, 'time') !== false) return 'time';
    if (strpos($dbType, 'email') !== false) return 'email';
    if (strpos($dbType, 'int') !== false) return 'number';
    if (strpos($dbType, 'decimal') !== false) return 'number';
    if (strpos($dbType, 'float') !== false) return 'number';
    if (strpos($dbType, 'enum') !== false) return 'select';
    
    return 'text';
}

function getFieldIcon($fieldName) {
    $fieldLower = strtolower($fieldName);
    
    if (strpos($fieldLower, 'email') !== false) return 'fa-envelope';
    if (strpos($fieldLower, 'phone') !== false) return 'fa-phone';
    if (strpos($fieldLower, 'date') !== false) return 'fa-calendar';
    if (strpos($fieldLower, 'time') !== false) return 'fa-clock';
    if (strpos($fieldLower, 'language') !== false) return 'fa-globe';
    if (strpos($fieldLower, 'name') !== false) return 'fa-user';
    if (strpos($fieldLower, 'status') !== false) return 'fa-info-circle';
    if (strpos($fieldLower, 'password') !== false) return 'fa-key';
    
    return 'fa-edit';
}
?>
