<?php
session_start();
if (!isset($_SESSION['username']) || !isset($_SESSION['random_code'])) {
    header("Location: Loginpage.php");
    exit(); 
}

$access = $_SESSION['access_level'];

if ($access == 'Admin') {
    // Continue with Admin code
} elseif ($access == 'Leader') {
    header("Location: Teamleader/LeaderHome.php");
    exit();
} elseif ($access == 'Member') {
    header("Location: Memberpages/MemberHome.php");
    exit();
} else {
    echo "Access level unknown. Please contact the system administrator.";
    exit();
}

include $_SERVER['DOCUMENT_ROOT'].'/config/Databasehost.php'; 

$email = $_SESSION['username'];
$message = '';

// Verify database connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['Email'], $_POST['AuxName'], $_POST['Starttime'], $_POST['Endtime'])) {
        $Email = $_POST['Email'];
        $AuxName = $_POST['AuxName'];
        $Starttime = $_POST['Starttime'];
        $Endtime = $_POST['Endtime'];

        // Check the format of the dates
        try {
            $start = new DateTime($Starttime);
            $end = new DateTime($Endtime);
            $interval = $start->diff($end);
            $Duration = $interval->format('%h hours %i minutes');
        } catch (Exception $e) {
            $message = "Error in date format: " . $e->getMessage();
            exit();
        }

        // Insert data into the database
        $query = "INSERT INTO interpretersaux (Email, AuxName, Duration, Starttime, Endtime) VALUES (?, ?, ?, ?, ?)";

        if ($stmt = $conn->prepare($query)) {
            $stmt->bind_param("sssss", $Email, $AuxName, $Duration, $Starttime, $Endtime);
            if ($stmt->execute()) {
                $message = "Data has been successfully added!"; // Success message
                // Optional: Redirect after success
                header("Location: " . $_SERVER['PHP_SELF']);
                exit();
            } else {
                $message = "Error occurred while adding data: " . $stmt->error;
            }
            $stmt->close();
        } else {
            $message = "Error preparing the query: " . $conn->error;
        }

        $conn->close();
    } else {
        $message = "Please fill in all the fields.";
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Data</title>
    <style>
        /* Add a background color */
        body {
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
            margin: 0;
            padding: 0;
        }

        /* Center content */
        .container {
            width: 60%;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            margin-top: 50px;
        }

        h2 {
            text-align: center;
            color: #333;
        }

        /* Style form fields */
        label {
            display: block;
            font-size: 16px;
            margin: 10px 0 5px;
            color: #555;
        }

        input[type="email"], input[type="text"], input[type="datetime-local"] {
            width: 100%;
            padding: 10px;
            font-size: 14px;
            border: 1px solid #ccc;
            border-radius: 4px;
            margin-bottom: 15px;
        }

        input[type="submit"] {
            background-color: #4CAF50;
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
        }

        input[type="submit"]:hover {
            background-color: #45a049;
        }

        .message {
            text-align: center;
            font-weight: bold;
            color: green;
            margin: 20px 0;
        }

        /* Add responsive design */
        @media screen and (max-width: 768px) {
            .container {
                width: 90%;
            }
        }
    </style>
</head>
<body>

    <div class="container">
        <h2>Add Data to Interpretersaux Table</h2>

        <?php if ($message != ''): ?>
            <div class="message"><?php echo $message; ?></div>
        <?php endif; ?>

        <form action="" method="POST">
            <label for="Email">Email:</label>
            <input type="email" id="Email" name="Email" required><br>

            <label for="AuxName">Aux:</label>
            <input type="text" id="AuxName" name="AuxName" required><br>

            <label for="Starttime">Start Time:</label>
            <input type="datetime-local" id="Starttime" name="Starttime" required><br>

            <label for="Endtime">End Time:</label>
            <input type="datetime-local" id="Endtime" name="Endtime" required><br>

            <input type="submit" value="Add Data">
        </form>
    </div>

</body>
</html>
