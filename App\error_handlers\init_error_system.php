<?php
/**
 * Error System Initialization
 * تهيئة نظام معالجة الأخطاء
 * 
 * يجب تضمين هذا الملف في بداية كل ملف PHP في النظام
 * 
 * @version 2.0
 * <AUTHOR> CX Security Team
 */

// منع الوصول المباشر
if (!defined('ERROR_SYSTEM_INIT_LOADED')) {
    define('ERROR_SYSTEM_INIT_LOADED', true);
} else {
    return; // تم تحميله مسبقاً
}

// تضمين ملفات النظام
require_once __DIR__ . '/error_config.php';
require_once __DIR__ . '/secure_error_handler.php';

/**
 * تهيئة نظام معالجة الأخطاء للملف الحالي
 */
function initializeErrorSystemForFile($file_type = 'general') {
    // تطبيق إعدادات الأخطاء حسب البيئة
    ErrorConfig::applyErrorSettings();
    
    // تهيئة معالجات الأخطاء الآمنة
    if (!defined('SECURE_ERROR_HANDLERS_INITIALIZED')) {
        initializeSecureErrorHandlers();
    }
    
    // تسجيل بداية تشغيل الملف
    secureErrorLog("File execution started: " . basename($_SERVER['PHP_SELF']), 'info', [
        'file_type' => $file_type,
        'request_method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown',
        'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown'
    ]);
    
    // إعداد معالج إنهاء التشغيل
    register_shutdown_function('handleFileShutdown', $file_type);
}

/**
 * معالج إنهاء تشغيل الملف
 */
function handleFileShutdown($file_type) {
    $error = error_get_last();
    
    if ($error && in_array($error['type'], [E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR, E_USER_ERROR])) {
        // تسجيل الخطأ القاتل
        secureErrorLog("Fatal error in {$file_type} file: " . $error['message'], 'critical', [
            'file' => basename($error['file']),
            'line' => $error['line'],
            'type' => $error['type']
        ]);
    }
    
    // تسجيل انتهاء تشغيل الملف
    $execution_time = microtime(true) - ($_SERVER['REQUEST_TIME_FLOAT'] ?? microtime(true));
    $memory_usage = memory_get_peak_usage(true);
    
    secureErrorLog("File execution completed: " . basename($_SERVER['PHP_SELF']), 'info', [
        'file_type' => $file_type,
        'execution_time' => round($execution_time, 4),
        'memory_usage' => round($memory_usage / 1024 / 1024, 2) . 'MB'
    ]);
}

/**
 * تهيئة خاصة لصفحات تسجيل الدخول
 */
function initializeLoginPageErrorHandling() {
    initializeErrorSystemForFile('login');
    
    // إعدادات خاصة لصفحات تسجيل الدخول
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_secure', ErrorConfig::isProduction() ? 1 : 0);
    ini_set('session.use_only_cookies', 1);
    ini_set('session.cookie_samesite', ErrorConfig::isProduction() ? 'Strict' : 'Lax');
    
    // تسجيل محاولة الوصول لصفحة تسجيل الدخول
    secureErrorLog("Login page access", 'info', [
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => substr($_SERVER['HTTP_USER_AGENT'] ?? 'unknown', 0, 200),
        'referer' => $_SERVER['HTTP_REFERER'] ?? 'direct'
    ]);
}

/**
 * تهيئة خاصة للصفحات الإدارية
 */
function initializeAdminPageErrorHandling() {
    initializeErrorSystemForFile('admin');
    
    // فحص إضافي للصلاحيات
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    if (!isset($_SESSION['username']) || !isset($_SESSION['access_level'])) {
        handleSecurityError('Unauthorized admin page access');
    }
    
    $admin_levels = ['Admin', 'Super Admin', 'Manager', 'Leader', 'Editor'];
    if (!in_array($_SESSION['access_level'], $admin_levels)) {
        handleSecurityError('Insufficient privileges for admin page');
    }
    
    // تسجيل الوصول للصفحة الإدارية
    secureErrorLog("Admin page access", 'info', [
        'user' => $_SESSION['username'],
        'access_level' => $_SESSION['access_level'],
        'page' => basename($_SERVER['PHP_SELF'])
    ]);
}

/**
 * تهيئة خاصة لصفحات الأعضاء
 */
function initializeMemberPageErrorHandling() {
    initializeErrorSystemForFile('member');
    
    // فحص صلاحيات الأعضاء
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    if (!isset($_SESSION['username']) || $_SESSION['access_level'] !== 'Member') {
        handleSecurityError('Unauthorized member page access');
    }
    
    // تسجيل الوصول لصفحة الأعضاء
    secureErrorLog("Member page access", 'info', [
        'user' => $_SESSION['username'],
        'page' => basename($_SERVER['PHP_SELF'])
    ]);
}

/**
 * تهيئة خاصة لملفات API
 */
function initializeApiErrorHandling() {
    initializeErrorSystemForFile('api');
    
    // إعدادات خاصة لـ API
    header('Content-Type: application/json; charset=utf-8');
    
    // معالج أخطاء خاص لـ API
    set_exception_handler('apiExceptionHandler');
    set_error_handler('apiErrorHandler');
    
    // تسجيل طلب API
    secureErrorLog("API request", 'info', [
        'endpoint' => $_SERVER['REQUEST_URI'] ?? 'unknown',
        'method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown',
        'content_type' => $_SERVER['CONTENT_TYPE'] ?? 'unknown'
    ]);
}

/**
 * معالج أخطاء خاص لـ API
 */
function apiErrorHandler($errno, $errstr, $errfile, $errline) {
    $error_response = [
        'success' => false,
        'error' => [
            'code' => 'INTERNAL_ERROR',
            'message' => ErrorConfig::isProduction() ? 
                'حدث خطأ داخلي في الخادم' : 
                $errstr,
            'timestamp' => date('c')
        ]
    ];
    
    // تسجيل الخطأ
    secureErrorLog("API Error: $errstr", 'error', [
        'file' => basename($errfile),
        'line' => $errline,
        'errno' => $errno
    ]);
    
    http_response_code(500);
    echo json_encode($error_response, JSON_UNESCAPED_UNICODE);
    exit();
}

/**
 * معالج استثناءات خاص لـ API
 */
function apiExceptionHandler($exception) {
    $error_response = [
        'success' => false,
        'error' => [
            'code' => 'EXCEPTION',
            'message' => ErrorConfig::isProduction() ? 
                'حدث خطأ غير متوقع' : 
                $exception->getMessage(),
            'timestamp' => date('c')
        ]
    ];
    
    // تسجيل الاستثناء
    secureErrorLog("API Exception: " . $exception->getMessage(), 'critical', [
        'file' => basename($exception->getFile()),
        'line' => $exception->getLine(),
        'class' => get_class($exception)
    ]);
    
    http_response_code(500);
    echo json_encode($error_response, JSON_UNESCAPED_UNICODE);
    exit();
}

/**
 * تهيئة خاصة لملفات قاعدة البيانات
 */
function initializeDatabaseErrorHandling() {
    initializeErrorSystemForFile('database');
    
    // معالج أخطاء خاص لقاعدة البيانات
    function handleDatabaseConnectionError($conn) {
        if ($conn && $conn->connect_error) {
            secureErrorLog("Database connection failed: " . $conn->connect_error, 'critical', [
                'host' => $_ENV['DB_HOST'] ?? 'unknown',
                'database' => $_ENV['DB_NAME'] ?? 'unknown'
            ]);
            
            if (ErrorConfig::isProduction()) {
                die('خطأ في الاتصال بقاعدة البيانات. يرجى المحاولة لاحقاً.');
            } else {
                die('Database connection failed: ' . $conn->connect_error);
            }
        }
    }
}

/**
 * تهيئة خاصة لملفات رفع الملفات
 */
function initializeFileUploadErrorHandling() {
    initializeErrorSystemForFile('file_upload');
    
    // فحص إعدادات رفع الملفات
    $max_file_size = ini_get('upload_max_filesize');
    $max_post_size = ini_get('post_max_size');
    
    secureErrorLog("File upload page initialized", 'info', [
        'max_file_size' => $max_file_size,
        'max_post_size' => $max_post_size,
        'temp_dir' => sys_get_temp_dir()
    ]);
    
    // معالج أخطاء رفع الملفات
    function handleFileUploadError($error_code) {
        $upload_errors = [
            UPLOAD_ERR_INI_SIZE => 'الملف أكبر من الحد المسموح به في إعدادات الخادم',
            UPLOAD_ERR_FORM_SIZE => 'الملف أكبر من الحد المسموح به في النموذج',
            UPLOAD_ERR_PARTIAL => 'تم رفع جزء من الملف فقط',
            UPLOAD_ERR_NO_FILE => 'لم يتم رفع أي ملف',
            UPLOAD_ERR_NO_TMP_DIR => 'مجلد الملفات المؤقتة غير موجود',
            UPLOAD_ERR_CANT_WRITE => 'فشل في كتابة الملف على القرص',
            UPLOAD_ERR_EXTENSION => 'امتداد PHP أوقف رفع الملف'
        ];
        
        $error_message = $upload_errors[$error_code] ?? 'خطأ غير معروف في رفع الملف';
        
        secureErrorLog("File upload error: $error_message", 'error', [
            'error_code' => $error_code
        ]);
        
        throw new Exception($error_message);
    }
}

/**
 * دالة مساعدة لتطبيق النظام على ملف محدد
 */
function applyErrorSystemToFile($file_path, $file_type = 'general') {
    if (!file_exists($file_path)) {
        secureErrorLog("Attempted to apply error system to non-existent file: $file_path", 'warning');
        return false;
    }
    
    $file_content = file_get_contents($file_path);
    
    // التحقق من وجود تهيئة النظام
    if (strpos($file_content, 'ERROR_SYSTEM_INIT_LOADED') === false) {
        $init_code = "<?php\n// تهيئة نظام معالجة الأخطاء\nrequire_once __DIR__ . '/error_handlers/init_error_system.php';\ninitializeErrorSystemForFile('$file_type');\n?>\n";
        
        // إدراج الكود في بداية الملف
        $file_content = preg_replace('/^<\?php/', $init_code, $file_content, 1);
        
        // حفظ الملف المحدث
        if (file_put_contents($file_path, $file_content)) {
            secureErrorLog("Error system applied to file: $file_path", 'info', [
                'file_type' => $file_type
            ]);
            return true;
        } else {
            secureErrorLog("Failed to apply error system to file: $file_path", 'error');
            return false;
        }
    }
    
    return true; // النظام مطبق مسبقاً
}

// تهيئة تلقائية عند تضمين الملف
if (!defined('ERROR_SYSTEM_AUTO_INITIALIZED')) {
    define('ERROR_SYSTEM_AUTO_INITIALIZED', true);
    
    // تحديد نوع الملف بناءً على المسار
    $current_file = basename($_SERVER['PHP_SELF']);
    $current_path = $_SERVER['REQUEST_URI'] ?? '';
    
    if (strpos($current_file, 'Login') !== false || strpos($current_path, 'login') !== false) {
        initializeLoginPageErrorHandling();
    } elseif (strpos($current_path, '/App/') !== false) {
        initializeAdminPageErrorHandling();
    } elseif (strpos($current_path, '/Memberpages/') !== false) {
        initializeMemberPageErrorHandling();
    } elseif (strpos($current_path, '/api/') !== false) {
        initializeApiErrorHandling();
    } else {
        initializeErrorSystemForFile();
    }
}

?>
