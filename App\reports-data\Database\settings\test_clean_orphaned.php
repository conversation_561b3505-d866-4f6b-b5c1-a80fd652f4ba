<?php
/**
 * Test Clean Orphaned Fields - Direct test for cleanOrphanedFields function
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

try {
    include $_SERVER['DOCUMENT_ROOT'].'/config/Databasehost.php';
} catch (Exception $e) {
    die("Database config error: " . $e->getMessage());
}

try {
    include '../system/database_config.php';
} catch (Exception $e) {
    die("System config error: " . $e->getMessage());
}

try {
    require_once __DIR__ . '/field_manager.php';
} catch (Exception $e) {
    die("Field manager error: " . $e->getMessage());
}

// Check session and access
if (!isset($_SESSION['username']) || !isset($_SESSION['random_code'])) {
    header("Location: ../../../../Loginpage.php");
    exit();
}

$access = $_SESSION['access_level'];
$email = $_SESSION['username'];

// Force debug mode
$_GET['debug'] = '1';

echo "<!DOCTYPE html>";
echo "<html><head>";
echo "<title>Test Clean Orphaned Fields</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'>";
echo "<style>";
echo "body { background: #f8f9fa; font-family: 'Segoe UI', sans-serif; }";
echo ".test-section { background: white; padding: 20px; border-radius: 8px; margin: 15px 0; border: 1px solid #dee2e6; }";
echo ".debug-output { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; margin: 10px 0; font-family: monospace; }";
echo "</style>";
echo "</head><body>";

echo "<div class='container mt-4'>";
echo "<h1>🧪 Test Clean Orphaned Fields</h1>";

// Initialize field manager
try {
    $fieldManager = new FieldManager($conn);
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "❌ <strong>Error initializing FieldManager:</strong> " . $e->getMessage();
    echo "</div>";
    echo "</div></body></html>";
    exit();
}

// Test 1: Check current state
echo "<div class='test-section'>";
echo "<h2>📊 Test 1: Current State Analysis</h2>";

echo "<h4>Current Configuration:</h4>";
$config = $fieldManager->loadConfiguration();
echo "<div class='debug-output'>";
echo "<strong>Sections found:</strong> " . count($config) . "<br>";
foreach ($config as $sectionKey => $section) {
    $fieldCount = isset($section['fields']) ? count($section['fields']) : 0;
    echo "- <strong>$sectionKey:</strong> $fieldCount fields<br>";
    if (isset($section['fields']) && !empty($section['fields'])) {
        foreach ($section['fields'] as $fieldName => $fieldConfig) {
            echo "&nbsp;&nbsp;• $fieldName<br>";
        }
    }
}
echo "</div>";

echo "<h4>Database Columns:</h4>";
$dbColumns = $fieldManager->getDatabaseColumns();
echo "<div class='debug-output'>";
echo "<strong>Database columns found:</strong> " . count($dbColumns) . "<br>";
$columnList = array_keys($dbColumns);
sort($columnList);
foreach (array_slice($columnList, 0, 20) as $column) {
    echo "• $column<br>";
}
if (count($columnList) > 20) {
    echo "... and " . (count($columnList) - 20) . " more<br>";
}
echo "</div>";

echo "<h4>Orphaned Fields:</h4>";
$orphanedFields = $fieldManager->getOrphanedFields();
echo "<div class='debug-output'>";
echo "<strong>Orphaned fields found:</strong> " . count($orphanedFields) . "<br>";
foreach ($orphanedFields as $orphan) {
    echo "• {$orphan['section']}|{$orphan['field']}<br>";
}
echo "</div>";

echo "</div>";

// Test 2: Manual clean test
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $_POST['action'] === 'test_clean') {
    echo "<div class='test-section'>";
    echo "<h2>🧹 Test 2: Clean Orphaned Fields Execution</h2>";
    
    echo "<div class='alert alert-info'>";
    echo "<strong>🔄 Executing cleanOrphanedFields()...</strong>";
    echo "</div>";
    
    $beforeCount = count($fieldManager->getOrphanedFields());
    echo "<p><strong>Before:</strong> $beforeCount orphaned fields</p>";
    
    $cleaned = $fieldManager->cleanOrphanedFields();
    
    $afterCount = count($fieldManager->getOrphanedFields());
    echo "<p><strong>After:</strong> $afterCount orphaned fields</p>";
    
    if ($cleaned > 0) {
        echo "<div class='alert alert-success'>";
        echo "✅ <strong>Success!</strong> Cleaned $cleaned orphaned fields.";
        echo "</div>";
    } else {
        echo "<div class='alert alert-warning'>";
        echo "⚠️ <strong>No fields cleaned.</strong> This could mean:";
        echo "<ul>";
        echo "<li>No orphaned fields exist</li>";
        echo "<li>Configuration save failed</li>";
        echo "<li>Fields were not actually orphaned</li>";
        echo "</ul>";
        echo "</div>";
    }
    
    echo "</div>";
}

// Test 3: Configuration save test
echo "<div class='test-section'>";
echo "<h2>💾 Test 3: Configuration Save Test</h2>";

$testConfig = [
    'test_section' => [
        'title' => 'Test Section',
        'icon' => 'fa-test',
        'fields' => [
            'test_field' => [
                'enabled' => '1',
                'type' => 'text',
                'label' => 'Test Field'
            ]
        ]
    ]
];

echo "<h4>Testing save functionality:</h4>";
$saveResult = $fieldManager->saveConfiguration($testConfig);

echo "<div class='debug-output'>";
echo "<strong>Save result:</strong> " . ($saveResult ? "✅ SUCCESS" : "❌ FAILED") . "<br>";

if ($saveResult) {
    echo "<strong>Verification:</strong> ";
    $loadedConfig = $fieldManager->loadConfiguration();
    if (isset($loadedConfig['test_section'])) {
        echo "✅ Configuration loaded successfully<br>";
    } else {
        echo "❌ Configuration not found after save<br>";
    }
} else {
    echo "<strong>Error:</strong> Failed to save configuration<br>";
}
echo "</div>";

echo "</div>";

// Test 4: File permissions check
echo "<div class='test-section'>";
echo "<h2>🔐 Test 4: File Permissions Check</h2>";

$configFile = __DIR__ . '/field_config.json';
echo "<div class='debug-output'>";
echo "<strong>Config file:</strong> $configFile<br>";
echo "<strong>File exists:</strong> " . (file_exists($configFile) ? "✅ Yes" : "❌ No") . "<br>";
if (file_exists($configFile)) {
    echo "<strong>File readable:</strong> " . (is_readable($configFile) ? "✅ Yes" : "❌ No") . "<br>";
    echo "<strong>File writable:</strong> " . (is_writable($configFile) ? "✅ Yes" : "❌ No") . "<br>";
}
echo "<strong>Directory writable:</strong> " . (is_writable(dirname($configFile)) ? "✅ Yes" : "❌ No") . "<br>";
echo "</div>";

// Check database fallback
echo "<h4>Database Storage Check:</h4>";
try {
    $result = $conn->query("SHOW TABLES LIKE 'field_config_storage'");
    if ($result && $result->num_rows > 0) {
        echo "<div class='alert alert-success'>";
        echo "✅ Database storage table exists";
        
        $configCheck = $conn->query("SELECT COUNT(*) as count FROM field_config_storage WHERE config_key = 'main_config'");
        if ($configCheck) {
            $row = $configCheck->fetch_assoc();
            echo "<br>📊 Configuration records: " . $row['count'];
        }
        echo "</div>";
    } else {
        echo "<div class='alert alert-warning'>";
        echo "⚠️ Database storage table does not exist (will be created automatically)";
        echo "</div>";
    }
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "❌ Database error: " . $e->getMessage();
    echo "</div>";
}

echo "</div>";

// Action buttons
echo "<div class='test-section'>";
echo "<h2>🎮 Actions</h2>";

echo "<form method='POST' class='d-inline'>";
echo "<input type='hidden' name='action' value='test_clean'>";
echo "<button type='submit' class='btn btn-danger btn-lg me-3'>";
echo "<i class='fas fa-broom me-2'></i>Test Clean Orphaned Fields";
echo "</button>";
echo "</form>";

echo "<a href='field_diagnostics.php?debug=1' class='btn btn-info btn-lg me-3'>";
echo "<i class='fas fa-stethoscope me-2'></i>Field Diagnostics (Debug)";
echo "</a>";

echo "<a href='field_settings.php?debug=1' class='btn btn-primary btn-lg'>";
echo "<i class='fas fa-cogs me-2'></i>Field Settings (Debug)";
echo "</a>";

echo "</div>";

// Console logging
echo "<script>";
echo "console.log('🧪 Test Clean Orphaned Fields Page Loaded');";
echo "console.log('📊 Orphaned fields found: " . count($orphanedFields) . "');";
echo "console.log('📋 Configuration sections: " . count($config) . "');";
echo "console.log('🗄️ Database columns: " . count($dbColumns) . "');";
echo "</script>";

echo "</div>"; // container
echo "</body></html>";
?>
