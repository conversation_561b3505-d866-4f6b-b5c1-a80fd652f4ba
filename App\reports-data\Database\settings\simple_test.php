<?php
/**
 * Simple test to check if the buttons work
 */

echo "<h1>🔧 Simple Button Test</h1>";

// Check if POST data is received
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h2>✅ POST Request Received!</h2>";
    echo "<p><strong>Action:</strong> " . ($_POST['action'] ?? 'None') . "</p>";
    echo "<p><strong>All POST data:</strong></p>";
    echo "<pre>" . print_r($_POST, true) . "</pre>";
    echo "</div>";
    
    // Try to include the required files
    try {
        echo "<p>Including database config...</p>";
        include $_SERVER['DOCUMENT_ROOT'].'/config/Databasehost.php';
        echo "<p>✅ Database config included</p>";
        
        echo "<p>Including system database config...</p>";
        include '../system/database_config.php';
        echo "<p>✅ System database config included</p>";
        
        echo "<p>Including field manager...</p>";
        require_once __DIR__ . '/field_manager.php';
        echo "<p>✅ Field manager included</p>";
        
        echo "<p>Creating field manager instance...</p>";
        $fieldManager = new FieldManager($conn);
        echo "<p>✅ Field manager created</p>";
        
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'auto_map':
                echo "<p>Executing auto-map...</p>";
                $result = $fieldManager->autoMapFields();
                echo "<p style='color: green;'>✅ Auto-mapped $result fields!</p>";
                break;
                
            case 'clean_orphaned':
                echo "<p>Executing clean orphaned...</p>";
                $result = $fieldManager->cleanOrphanedFields();
                echo "<p style='color: green;'>✅ Cleaned $result orphaned fields!</p>";
                break;
                
            default:
                echo "<p style='color: orange;'>⚠️ Unknown action: $action</p>";
        }
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; color: #721c24;'>";
        echo "<h3>❌ Error</h3>";
        echo "<p>" . $e->getMessage() . "</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
        echo "</div>";
    }
} else {
    echo "<p>No POST request received yet.</p>";
}

// Show test buttons
echo "<div style='background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #dee2e6;'>";
echo "<h2>🧪 Test Buttons</h2>";

echo "<form method='POST' style='margin: 10px 0;'>";
echo "<input type='hidden' name='action' value='auto_map'>";
echo "<button type='submit' style='background: #28a745; color: white; border: none; padding: 12px 20px; border-radius: 6px; cursor: pointer; font-size: 16px;'>";
echo "🪄 Test Auto-Map";
echo "</button>";
echo "</form>";

echo "<form method='POST' style='margin: 10px 0;'>";
echo "<input type='hidden' name='action' value='clean_orphaned'>";
echo "<button type='submit' style='background: #ffc107; color: black; border: none; padding: 12px 20px; border-radius: 6px; cursor: pointer; font-size: 16px;'>";
echo "🧹 Test Clean Orphaned";
echo "</button>";
echo "</form>";

echo "<form method='POST' style='margin: 10px 0;'>";
echo "<input type='hidden' name='action' value='test_action'>";
echo "<button type='submit' style='background: #007bff; color: white; border: none; padding: 12px 20px; border-radius: 6px; cursor: pointer; font-size: 16px;'>";
echo "🔍 Test POST";
echo "</button>";
echo "</form>";

echo "</div>";

// Debug information
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
echo "<h3>🔍 Debug Information</h3>";
echo "<p><strong>Current Directory:</strong> " . __DIR__ . "</p>";
echo "<p><strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p><strong>Script Name:</strong> " . $_SERVER['SCRIPT_NAME'] . "</p>";
echo "<p><strong>Request Method:</strong> " . $_SERVER['REQUEST_METHOD'] . "</p>";

// Check if files exist
$files = [
    'Database config' => $_SERVER['DOCUMENT_ROOT'].'/config/Databasehost.php',
    'System config' => '../system/database_config.php',
    'Field manager' => __DIR__ . '/field_manager.php',
    'Field config' => __DIR__ . '/field_config.json'
];

echo "<h4>File Existence Check:</h4>";
echo "<ul>";
foreach ($files as $name => $path) {
    $exists = file_exists($path);
    $color = $exists ? 'green' : 'red';
    $icon = $exists ? '✅' : '❌';
    echo "<li style='color: $color;'>$icon <strong>$name:</strong> $path</li>";
}
echo "</ul>";

echo "</div>";

// Show current working directory contents
echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
echo "<h3>📁 Current Directory Contents</h3>";
$files = scandir(__DIR__);
echo "<ul>";
foreach ($files as $file) {
    if ($file !== '.' && $file !== '..') {
        echo "<li>$file</li>";
    }
}
echo "</ul>";
echo "</div>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background: #f8f9fa;
}

h1, h2, h3 {
    color: #2c3e50;
}

pre {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    font-size: 12px;
    max-height: 200px;
    overflow-y: auto;
}

button:hover {
    opacity: 0.9;
    transform: translateY(-1px);
}
</style>
