<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unified Tables Example</title>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    
    <!-- Bootstrap (Optional) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Unified Tables CSS -->
    <link rel="stylesheet" href="../css/root-variables.css">
    <link rel="stylesheet" href="../css/unified-tables.css">
    
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f8f9fa;
            padding: 20px;
        }
        .example-section {
            margin-bottom: 40px;
        }
        .example-title {
            color: #6c5ce7;
            margin-bottom: 20px;
            border-bottom: 2px solid #6c5ce7;
            padding-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <h1 class="text-center mb-5">🎨 نظام الجداول الموحد - أمثلة تطبيقية</h1>
        
        <!-- Example 1: Basic Table -->
        <div class="example-section">
            <h2 class="example-title">1. جدول أساسي موحد</h2>
            
            <div class="unified-table-container">
                <div class="unified-table-responsive">
                    <table class="unified-table">
                        <thead>
                            <tr>
                                <th><i class="fas fa-hashtag"></i>ID</th>
                                <th><i class="fas fa-user"></i>Name</th>
                                <th><i class="fas fa-envelope"></i>Email</th>
                                <th><i class="fas fa-check-circle"></i>Status</th>
                                <th><i class="fas fa-cogs"></i>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><span class="employee-code">001</span></td>
                                <td>أحمد محمد</td>
                                <td><EMAIL></td>
                                <td><span class="status-badge status-active">Active</span></td>
                                <td>
                                    <div class="table-actions">
                                        <a href="#" class="btn-table-action btn-edit" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="#" class="btn-table-action btn-view" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="#" class="btn-table-action btn-delete" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><span class="employee-code">002</span></td>
                                <td>فاطمة علي</td>
                                <td><EMAIL></td>
                                <td><span class="status-badge status-inactive">Inactive</span></td>
                                <td>
                                    <div class="table-actions">
                                        <a href="#" class="btn-table-action btn-edit" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="#" class="btn-table-action btn-archive" title="Archive">
                                            <i class="fas fa-archive"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Example 2: Table with Filters -->
        <div class="example-section">
            <h2 class="example-title">2. جدول مع فلاتر</h2>
            
            <div class="unified-table-container">
                <!-- Filters -->
                <div class="table-filters">
                    <div class="filter-group">
                        <label>Department</label>
                        <select>
                            <option value="">All Departments</option>
                            <option value="it">IT</option>
                            <option value="hr">HR</option>
                            <option value="finance">Finance</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>Status</label>
                        <select>
                            <option value="">All Status</option>
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>Date From</label>
                        <input type="date">
                    </div>
                    <div class="filter-group">
                        <label>Date To</label>
                        <input type="date">
                    </div>
                </div>

                <!-- Table -->
                <div class="unified-table-responsive">
                    <table class="unified-table">
                        <thead>
                            <tr>
                                <th><i class="fas fa-image"></i>Photo</th>
                                <th><i class="fas fa-user"></i>Employee</th>
                                <th><i class="fas fa-building"></i>Department</th>
                                <th><i class="fas fa-calendar"></i>Join Date</th>
                                <th><i class="fas fa-check-circle"></i>Status</th>
                                <th><i class="fas fa-cogs"></i>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <div class="table-profile-placeholder">
                                        <i class="fas fa-user"></i>
                                    </div>
                                </td>
                                <td>محمد أحمد</td>
                                <td>IT Department</td>
                                <td><span class="table-date">2023-01-15</span></td>
                                <td><span class="status-badge status-active">Active</span></td>
                                <td>
                                    <div class="table-actions">
                                        <a href="#" class="btn-table-action btn-edit" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="#" class="btn-table-action btn-view" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Example 3: Leaves Requests Table -->
        <div class="example-section">
            <h2 class="example-title">3. جدول طلبات الإجازات</h2>
            
            <div class="unified-table-container">
                <div class="unified-table-responsive">
                    <table class="unified-table">
                        <thead>
                            <tr>
                                <th><i class="fas fa-hashtag"></i>Code</th>
                                <th><i class="fas fa-envelope"></i>Employee</th>
                                <th><i class="fas fa-calendar-day"></i>From</th>
                                <th><i class="fas fa-calendar-day"></i>To</th>
                                <th><i class="fas fa-calculator"></i>Days</th>
                                <th><i class="fas fa-check-circle"></i>Status</th>
                                <th><i class="fas fa-comment"></i>Comment</th>
                                <th><i class="fas fa-paperclip"></i>Attachment</th>
                                <th><i class="fas fa-cogs"></i>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>LV001</td>
                                <td><EMAIL></td>
                                <td>2024-02-01</td>
                                <td>2024-02-05</td>
                                <td>5</td>
                                <td><span class="status-badge status-pending">Pending</span></td>
                                <td>
                                    <input type="text" class="table-input" placeholder="Add comment..." value="Annual leave">
                                </td>
                                <td>
                                    <a href="#" class="table-attachment">
                                        <i class="fas fa-paperclip"></i> View
                                    </a>
                                </td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-table-action btn-approved" title="Approve">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button class="btn-table-action btn-rejected" title="Reject">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>LV002</td>
                                <td><EMAIL></td>
                                <td>2024-01-20</td>
                                <td>2024-01-22</td>
                                <td>3</td>
                                <td><span class="status-badge status-approved">Approved</span></td>
                                <td>
                                    <input type="text" class="table-input" value="Sick leave - approved by manager" readonly>
                                </td>
                                <td>
                                    <span class="text-muted">-</span>
                                </td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-table-action btn-view" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Example 4: Compact Table -->
        <div class="example-section">
            <h2 class="example-title">4. جدول مضغوط</h2>
            
            <div class="unified-table-container">
                <div class="unified-table-responsive">
                    <table class="unified-table table-compact">
                        <thead>
                            <tr>
                                <th><i class="fas fa-hashtag"></i>ID</th>
                                <th><i class="fas fa-tag"></i>Name</th>
                                <th><i class="fas fa-check-circle"></i>Status</th>
                                <th><i class="fas fa-calendar"></i>Date</th>
                                <th><i class="fas fa-cogs"></i>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td>Task 1</td>
                                <td><span class="status-badge status-approved">Complete</span></td>
                                <td><span class="table-date">2024-01-15</span></td>
                                <td>
                                    <div class="table-actions">
                                        <a href="#" class="btn-table-action btn-view" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td>Task 2</td>
                                <td><span class="status-badge status-pending">In Progress</span></td>
                                <td><span class="table-date">2024-01-16</span></td>
                                <td>
                                    <div class="table-actions">
                                        <a href="#" class="btn-table-action btn-edit" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Example 5: Status Badges Showcase -->
        <div class="example-section">
            <h2 class="example-title">5. عرض جميع أنواع الحالات</h2>
            
            <div class="unified-table-container">
                <div class="unified-table-responsive">
                    <table class="unified-table">
                        <thead>
                            <tr>
                                <th><i class="fas fa-tag"></i>Status Type</th>
                                <th><i class="fas fa-palette"></i>Badge</th>
                                <th><i class="fas fa-code"></i>CSS Class</th>
                                <th><i class="fas fa-info-circle"></i>Usage</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Pending</td>
                                <td><span class="status-badge status-pending">Pending</span></td>
                                <td><code>status-pending</code></td>
                                <td>For pending requests/approvals</td>
                            </tr>
                            <tr>
                                <td>Approved</td>
                                <td><span class="status-badge status-approved">Approved</span></td>
                                <td><code>status-approved</code></td>
                                <td>For approved items</td>
                            </tr>
                            <tr>
                                <td>Rejected</td>
                                <td><span class="status-badge status-rejected">Rejected</span></td>
                                <td><code>status-rejected</code></td>
                                <td>For rejected items</td>
                            </tr>
                            <tr>
                                <td>Active</td>
                                <td><span class="status-badge status-active">Active</span></td>
                                <td><code>status-active</code></td>
                                <td>For active users/items</td>
                            </tr>
                            <tr>
                                <td>Inactive</td>
                                <td><span class="status-badge status-inactive">Inactive</span></td>
                                <td><code>status-inactive</code></td>
                                <td>For inactive users/items</td>
                            </tr>
                            <tr>
                                <td>Processing</td>
                                <td><span class="status-badge status-processing">Processing</span></td>
                                <td><code>status-processing</code></td>
                                <td>For items being processed</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Example 6: Action Buttons Showcase -->
        <div class="example-section">
            <h2 class="example-title">6. عرض جميع أنواع أزرار الإجراءات</h2>
            
            <div class="unified-table-container">
                <div class="unified-table-responsive">
                    <table class="unified-table">
                        <thead>
                            <tr>
                                <th><i class="fas fa-tag"></i>Button Type</th>
                                <th><i class="fas fa-mouse-pointer"></i>Button</th>
                                <th><i class="fas fa-code"></i>CSS Class</th>
                                <th><i class="fas fa-info-circle"></i>Usage</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Edit</td>
                                <td>
                                    <a href="#" class="btn-table-action btn-edit" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </td>
                                <td><code>btn-edit</code></td>
                                <td>For editing records</td>
                            </tr>
                            <tr>
                                <td>Delete</td>
                                <td>
                                    <a href="#" class="btn-table-action btn-delete" title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </td>
                                <td><code>btn-delete</code></td>
                                <td>For deleting records</td>
                            </tr>
                            <tr>
                                <td>View</td>
                                <td>
                                    <a href="#" class="btn-table-action btn-view" title="View">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                                <td><code>btn-view</code></td>
                                <td>For viewing details</td>
                            </tr>
                            <tr>
                                <td>Archive</td>
                                <td>
                                    <a href="#" class="btn-table-action btn-archive" title="Archive">
                                        <i class="fas fa-archive"></i>
                                    </a>
                                </td>
                                <td><code>btn-archive</code></td>
                                <td>For archiving records</td>
                            </tr>
                            <tr>
                                <td>Approve</td>
                                <td>
                                    <button class="btn-table-action btn-approved" title="Approve">
                                        <i class="fas fa-check"></i>
                                    </button>
                                </td>
                                <td><code>btn-approved</code></td>
                                <td>For approval actions</td>
                            </tr>
                            <tr>
                                <td>Reject</td>
                                <td>
                                    <button class="btn-table-action btn-rejected" title="Reject">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </td>
                                <td><code>btn-rejected</code></td>
                                <td>For rejection actions</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Code Examples -->
        <div class="example-section">
            <h2 class="example-title">7. أمثلة الكود</h2>
            
            <div class="row">
                <div class="col-md-6">
                    <h4>HTML Structure:</h4>
                    <pre><code>&lt;div class="unified-table-container"&gt;
    &lt;div class="unified-table-responsive"&gt;
        &lt;table class="unified-table"&gt;
            &lt;thead&gt;
                &lt;tr&gt;
                    &lt;th&gt;&lt;i class="fas fa-icon"&gt;&lt;/i&gt;Column&lt;/th&gt;
                &lt;/tr&gt;
            &lt;/thead&gt;
            &lt;tbody&gt;
                &lt;tr&gt;
                    &lt;td&gt;Data&lt;/td&gt;
                &lt;/tr&gt;
            &lt;/tbody&gt;
        &lt;/table&gt;
    &lt;/div&gt;
&lt;/div&gt;</code></pre>
                </div>
                
                <div class="col-md-6">
                    <h4>CSS Include:</h4>
                    <pre><code>&lt;link rel="stylesheet" href="../assets/css/unified-tables.css"&gt;</code></pre>
                    
                    <h4>Status Badge:</h4>
                    <pre><code>&lt;span class="status-badge status-pending"&gt;Pending&lt;/span&gt;</code></pre>
                    
                    <h4>Action Button:</h4>
                    <pre><code>&lt;a href="#" class="btn-table-action btn-edit"&gt;
    &lt;i class="fas fa-edit"&gt;&lt;/i&gt;
&lt;/a&gt;</code></pre>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS (Optional) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
