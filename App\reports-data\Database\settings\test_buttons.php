<?php
/**
 * Test the Auto-Map and Clean buttons functionality
 */

session_start();
include $_SERVER['DOCUMENT_ROOT'].'/config/Databasehost.php';
include '../system/database_config.php';
require_once __DIR__ . '/field_manager.php';

echo "<h1>🧪 Test Auto-Map and Clean Buttons</h1>";

// Debug information
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
echo "<h3>Debug Information</h3>";
echo "<p><strong>Request Method:</strong> " . $_SERVER['REQUEST_METHOD'] . "</p>";
echo "<p><strong>POST Data:</strong> " . (empty($_POST) ? 'None' : print_r($_POST, true)) . "</p>";
echo "<p><strong>Session:</strong> " . (isset($_SESSION['username']) ? $_SESSION['username'] : 'Not logged in') . "</p>";
echo "</div>";

try {
    // Initialize field manager
    $fieldManager = new FieldManager($conn);
    
    // Handle POST actions first
    $message = '';
    $messageType = '';
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $action = $_POST['action'] ?? '';
        
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "<h3>🔄 Processing Action: " . htmlspecialchars($action) . "</h3>";
        
        try {
            switch ($action) {
                case 'auto_map':
                    echo "<p>Calling autoMapFields()...</p>";
                    $mapped = $fieldManager->autoMapFields();
                    $message = "✅ Auto-mapped $mapped fields to appropriate sections.";
                    $messageType = 'success';
                    echo "<p style='color: green;'>$message</p>";
                    break;
                    
                case 'clean_orphaned':
                    echo "<p>Calling cleanOrphanedFields()...</p>";
                    $cleaned = $fieldManager->cleanOrphanedFields();
                    $message = "✅ Cleaned $cleaned orphaned fields from configuration.";
                    $messageType = 'success';
                    echo "<p style='color: green;'>$message</p>";
                    break;
                    
                default:
                    $message = "⚠️ Unknown action: " . htmlspecialchars($action);
                    $messageType = 'warning';
                    echo "<p style='color: orange;'>$message</p>";
                    break;
            }
        } catch (Exception $e) {
            $message = "❌ Error processing action: " . $e->getMessage();
            $messageType = 'danger';
            echo "<p style='color: red;'>$message</p>";
            echo "<pre style='background: #f8d7da; padding: 10px; border-radius: 4px;'>" . $e->getTraceAsString() . "</pre>";
        }
        echo "</div>";
    }
    
    // Get current statistics
    $stats = $fieldManager->getFieldStats();
    $unmappedFields = $fieldManager->getUnmappedFields();
    $orphanedFields = $fieldManager->getOrphanedFields();
    
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h2>📊 Current Statistics</h2>";
    echo "<ul>";
    echo "<li><strong>Total DB Columns:</strong> {$stats['total_db_columns']}</li>";
    echo "<li><strong>Configured Fields:</strong> {$stats['configured_fields']}</li>";
    echo "<li><strong>Unmapped Fields:</strong> {$stats['unmapped_fields']}</li>";
    echo "<li><strong>Orphaned Fields:</strong> {$stats['orphaned_fields']}</li>";
    echo "</ul>";
    echo "</div>";
    
    // Show unmapped fields
    if (!empty($unmappedFields)) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "<h3>🔍 Unmapped Fields (" . count($unmappedFields) . ")</h3>";
        echo "<ul>";
        foreach ($unmappedFields as $name => $info) {
            echo "<li><strong>$name</strong> - {$info['type']}</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    // Show orphaned fields
    if (!empty($orphanedFields)) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "<h3>🗑️ Orphaned Fields (" . count($orphanedFields) . ")</h3>";
        echo "<ul>";
        foreach ($orphanedFields as $orphan) {
            echo "<li><strong>{$orphan['field']}</strong> in section '{$orphan['section']}'</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    // Action buttons
    echo "<div style='background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #dee2e6;'>";
    echo "<h2>🎮 Action Buttons</h2>";
    
    if ($stats['unmapped_fields'] > 0) {
        echo "<form method='POST' style='display: inline-block; margin-right: 15px;'>";
        echo "<input type='hidden' name='action' value='auto_map'>";
        echo "<button type='submit' style='background: #28a745; color: white; border: none; padding: 12px 20px; border-radius: 6px; cursor: pointer; font-size: 16px;' onclick='return confirm(\"Auto-map {$stats['unmapped_fields']} unmapped fields?\");'>";
        echo "🪄 Auto-Map {$stats['unmapped_fields']} Fields";
        echo "</button>";
        echo "</form>";
    } else {
        echo "<button disabled style='background: #6c757d; color: white; border: none; padding: 12px 20px; border-radius: 6px; font-size: 16px;'>";
        echo "✅ All Fields Mapped";
        echo "</button>";
    }
    
    if ($stats['orphaned_fields'] > 0) {
        echo "<form method='POST' style='display: inline-block;'>";
        echo "<input type='hidden' name='action' value='clean_orphaned'>";
        echo "<button type='submit' style='background: #ffc107; color: black; border: none; padding: 12px 20px; border-radius: 6px; cursor: pointer; font-size: 16px;' onclick='return confirm(\"Clean {$stats['orphaned_fields']} orphaned fields? This cannot be undone!\");'>";
        echo "🧹 Clean {$stats['orphaned_fields']} Orphaned Fields";
        echo "</button>";
        echo "</form>";
    } else {
        echo "<button disabled style='background: #6c757d; color: white; border: none; padding: 12px 20px; border-radius: 6px; font-size: 16px; margin-left: 15px;'>";
        echo "✅ No Orphaned Fields";
        echo "</button>";
    }
    
    echo "</div>";
    
    // Test individual functions
    echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h2>🔧 Manual Function Tests</h2>";
    
    echo "<form method='POST' style='margin: 10px 0;'>";
    echo "<input type='hidden' name='action' value='test_automap'>";
    echo "<button type='submit' style='background: #2196f3; color: white; border: none; padding: 8px 16px; border-radius: 4px;'>";
    echo "Test Auto-Map Function";
    echo "</button>";
    echo "</form>";
    
    echo "<form method='POST' style='margin: 10px 0;'>";
    echo "<input type='hidden' name='action' value='test_clean'>";
    echo "<button type='submit' style='background: #ff9800; color: white; border: none; padding: 8px 16px; border-radius: 4px;'>";
    echo "Test Clean Function";
    echo "</button>";
    echo "</form>";
    
    // Handle test actions
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'test_automap') {
            echo "<h3>Testing Auto-Map Function:</h3>";
            try {
                $beforeCount = count($fieldManager->getUnmappedFields());
                echo "<p>Before: $beforeCount unmapped fields</p>";
                
                $mapped = $fieldManager->autoMapFields();
                echo "<p style='color: green;'>✅ Function executed successfully!</p>";
                echo "<p>Mapped: $mapped fields</p>";
                
                $afterCount = count($fieldManager->getUnmappedFields());
                echo "<p>After: $afterCount unmapped fields</p>";
                
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
            }
        }
        
        if ($action === 'test_clean') {
            echo "<h3>Testing Clean Function:</h3>";
            try {
                $beforeCount = count($fieldManager->getOrphanedFields());
                echo "<p>Before: $beforeCount orphaned fields</p>";
                
                $cleaned = $fieldManager->cleanOrphanedFields();
                echo "<p style='color: green;'>✅ Function executed successfully!</p>";
                echo "<p>Cleaned: $cleaned fields</p>";
                
                $afterCount = count($fieldManager->getOrphanedFields());
                echo "<p>After: $afterCount orphaned fields</p>";
                
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    echo "</div>";
    
    // Links
    echo "<div style='text-align: center; margin: 30px 0;'>";
    echo "<a href='field_diagnostics.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 0 10px;'>";
    echo "🔙 Back to Field Diagnostics";
    echo "</a>";
    echo "<a href='debug_field_actions.php' style='background: #6f42c1; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 0 10px;'>";
    echo "🔍 Debug Actions";
    echo "</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; color: #721c24;'>";
    echo "<h3>❌ Fatal Error</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
    echo "</div>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background: #f8f9fa;
}

h1, h2, h3 {
    color: #2c3e50;
}

pre {
    font-size: 12px;
    max-height: 200px;
    overflow-y: auto;
}

button:hover {
    opacity: 0.9;
    transform: translateY(-1px);
}

button:disabled {
    cursor: not-allowed !important;
    opacity: 0.6;
}
</style>
