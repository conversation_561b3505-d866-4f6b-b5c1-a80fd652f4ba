<?php
/**
 * Fix Date Values Script
 * This script fixes empty string values in date columns that should be NULL
 * to prevent "Incorrect DATE value" MySQL errors
 */

require_once '../../../config/database.php';

echo "<h2>Fixing Date Values in Database</h2>\n";

try {
    // Connect to database
    $conn = new mysqli($servername, $username, $password, $dbname);
    
    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }
    
    echo "<p>Connected to database successfully.</p>\n";
    
    // Get all date columns from the table
    $dateColumns = [];
    $result = $conn->query("DESCRIBE databasehc");
    
    while ($row = $result->fetch_assoc()) {
        if (strpos(strtolower($row['Type']), 'date') !== false) {
            $dateColumns[] = $row['Field'];
        }
    }
    
    echo "<p>Found date columns: " . implode(', ', $dateColumns) . "</p>\n";
    
    // Fix each date column
    foreach ($dateColumns as $column) {
        echo "<h3>Fixing column: $column</h3>\n";
        
        // Check for problematic values
        $checkSql = "SELECT COUNT(*) as count FROM databasehc WHERE `$column` = '' OR `$column` = '0000-00-00'";
        $checkResult = $conn->query($checkSql);
        $checkRow = $checkResult->fetch_assoc();
        $problemCount = $checkRow['count'];
        
        echo "<p>Found $problemCount problematic values in $column</p>\n";
        
        if ($problemCount > 0) {
            // Fix empty strings and invalid dates
            $fixSql = "UPDATE databasehc SET `$column` = NULL WHERE `$column` = '' OR `$column` = '0000-00-00'";
            
            if ($conn->query($fixSql)) {
                echo "<p style='color: green;'>✓ Fixed $problemCount values in $column</p>\n";
            } else {
                echo "<p style='color: red;'>✗ Error fixing $column: " . $conn->error . "</p>\n";
            }
        } else {
            echo "<p style='color: blue;'>✓ No issues found in $column</p>\n";
        }
    }
    
    echo "<h3>Verification</h3>\n";
    
    // Verify the fixes
    foreach ($dateColumns as $column) {
        $verifySql = "SELECT COUNT(*) as count FROM databasehc WHERE `$column` = '' OR `$column` = '0000-00-00'";
        $verifyResult = $conn->query($verifySql);
        $verifyRow = $verifyResult->fetch_assoc();
        $remainingProblems = $verifyRow['count'];
        
        if ($remainingProblems == 0) {
            echo "<p style='color: green;'>✓ $column is now clean</p>\n";
        } else {
            echo "<p style='color: red;'>✗ $column still has $remainingProblems problematic values</p>\n";
        }
    }
    
    echo "<h3>Testing Query</h3>\n";
    
    // Test the problematic query
    try {
        $testSql = "SELECT DISTINCT `Startdate` FROM databasehc WHERE `Startdate` IS NOT NULL AND `Startdate` != '0000-00-00' ORDER BY `Startdate` LIMIT 5";
        $testResult = $conn->query($testSql);
        
        if ($testResult) {
            echo "<p style='color: green;'>✓ Test query executed successfully</p>\n";
            echo "<p>Sample Startdate values:</p>\n";
            echo "<ul>\n";
            while ($row = $testResult->fetch_assoc()) {
                echo "<li>" . htmlspecialchars($row['Startdate']) . "</li>\n";
            }
            echo "</ul>\n";
        } else {
            echo "<p style='color: red;'>✗ Test query failed: " . $conn->error . "</p>\n";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Test query exception: " . $e->getMessage() . "</p>\n";
    }
    
    $conn->close();
    echo "<p>Database connection closed.</p>\n";
    echo "<h3 style='color: green;'>Fix completed successfully!</h3>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>\n";
}
?>
