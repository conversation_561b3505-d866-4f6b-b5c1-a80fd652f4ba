/* Unified Logout Modal Styles */
@import url('root-variables.css');

/* Custom backdrop with blur effect */
.unified-logout-modal.modal {
    background-color: rgba(var(--gray-900), 0.5) !important;
    backdrop-filter: blur(8px) !important;
    -webkit-backdrop-filter: blur(8px) !important;
    /* Remove Bootstrap fade animation */
    transition: none !important;
}

/* Override Bootstrap's fade class */
.unified-logout-modal.fade {
    transition: none !important;
}

.unified-logout-modal.fade.show {
    opacity: 1 !important;
}

.unified-logout-modal .modal-dialog {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    z-index: 1055 !important;
    margin: 0 !important;
    max-width: 450px;
    width: 90%;
}

/* Remove Bootstrap's default backdrop */
.unified-logout-modal + .modal-backdrop {
    display: none !important;
}

/* Modal Content Styling */
.unified-logout-modal .modal-content {
    background: var(--bg-white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    border: none;
    overflow: hidden;
    /* Remove animation for instant appearance */
    animation: none;
}

/* Header Styling */
.unified-logout-modal .modal-header {
    background: var(--gradient-primary);
    color: var(--text-white);
    border-bottom: none;
    padding: var(--spacing-xl) var(--spacing-2xl);
    text-align: center;
    position: relative;
}

.unified-logout-modal .modal-title {
    font-weight: 700;
    font-size: 1.3rem;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.unified-logout-modal .modal-title i {
    font-size: 1.4rem;
}

.unified-logout-modal .btn-close {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-lg);
    background: none;
    border: none;
    color: var(--text-white);
    opacity: 0.8;
    font-size: 1.3rem;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-normal);
}

.unified-logout-modal .btn-close:hover {
    opacity: 1;
    background: rgba(255, 255, 255, 0.2);
    transform: rotate(90deg);
}

/* Body Styling */
.unified-logout-modal .modal-body {
    padding: var(--spacing-2xl) var(--spacing-xl);
    text-align: center;
    background: var(--bg-light);
}

.unified-logout-modal .logout-icon {
    margin-bottom: var(--spacing-xl);
}

.unified-logout-modal .logout-icon i {
    font-size: 4rem;
    color: var(--warning-500);
    animation: pulse 2s infinite;
    filter: drop-shadow(0 4px 8px rgba(var(--warning-500), 0.3));
}

.unified-logout-modal .modal-body h6 {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    font-size: 1.2rem;
    line-height: 1.4;
}

.unified-logout-modal .modal-body p {
    color: var(--text-secondary);
    margin-bottom: 0;
    font-size: 1rem;
    line-height: 1.5;
}

/* Footer Styling */
.unified-logout-modal .modal-footer {
    padding: var(--spacing-xl) var(--spacing-2xl);
    border-top: 1px solid var(--border-light);
    background: var(--bg-light);
    justify-content: center;
    gap: var(--spacing-md);
    flex-wrap: nowrap;
}

/* Button Styling */
.unified-logout-modal .cancel-btn {
    background: linear-gradient(135deg, var(--gray-600) 0%, var(--gray-700) 100%);
    color: var(--text-white);
    border: none;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: 0.95rem;
    transition: var(--transition-normal);
    min-width: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.unified-logout-modal .cancel-btn:hover {
    background: linear-gradient(135deg, var(--gray-700) 0%, var(--gray-800) 100%);
    color: var(--text-white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.unified-logout-modal .logout-btn {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--text-white);
    border: none;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: 0.95rem;
    transition: var(--transition-normal);
    min-width: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.unified-logout-modal .logout-btn:hover {
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-darker) 100%);
    color: var(--text-white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.unified-logout-modal .logout-btn:active,
.unified-logout-modal .cancel-btn:active {
    transform: translateY(0);
}

/* Animations */
@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translate(-50%, -60%) scale(0.8);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

/* Loading state */
.unified-logout-modal .loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: var(--spacing-2xl) var(--spacing-xl);
    background: var(--bg-light);
}

.unified-logout-modal .loading-state .spinner-border {
    width: 3rem;
    height: 3rem;
    margin-bottom: var(--spacing-lg);
    color: var(--primary-500);
}

.unified-logout-modal .loading-state p {
    color: var(--text-secondary);
    font-size: 1.1rem;
    margin: 0;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 576px) {
    .unified-logout-modal .modal-dialog {
        width: 95%;
        max-width: 350px;
    }

    .unified-logout-modal .modal-header,
    .unified-logout-modal .modal-body,
    .unified-logout-modal .modal-footer {
        padding: var(--spacing-lg);
    }

    .unified-logout-modal .modal-footer {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .unified-logout-modal .modal-footer .btn {
        width: 100%;
        min-width: auto;
    }

    .unified-logout-modal .logout-icon i {
        font-size: 3rem;
    }

    .unified-logout-modal .modal-title {
        font-size: 1.1rem;
    }

    .unified-logout-modal .modal-body h6 {
        font-size: 1.1rem;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .unified-logout-modal .modal-content {
        background: var(--gray-800);
        color: var(--text-white);
    }

    .unified-logout-modal .modal-body,
    .unified-logout-modal .modal-footer {
        background: var(--gray-700);
    }

    .unified-logout-modal .modal-body h6 {
        color: var(--text-white);
    }

    .unified-logout-modal .modal-body p {
        color: var(--gray-300);
    }
}

/* Accessibility improvements */
.unified-logout-modal .modal-content:focus {
    outline: 3px solid var(--primary-500);
    outline-offset: 2px;
}

.unified-logout-modal .btn:focus {
    outline: 2px solid var(--text-white);
    outline-offset: 2px;
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .unified-logout-modal .modal-header {
        background: var(--text-black);
        color: var(--text-white);
    }

    .unified-logout-modal .logout-btn {
        background: var(--danger-700);
        border: 2px solid var(--text-white);
    }

    .unified-logout-modal .cancel-btn {
        background: var(--gray-600);
        border: 2px solid var(--text-white);
    }
}
