<?php
/**
 * Live Button Test - Test the exact same functionality as field_diagnostics.php
 */

session_start();
include $_SERVER['DOCUMENT_ROOT'].'/config/Databasehost.php';
include '../system/database_config.php';
require_once __DIR__ . '/field_manager.php';

// Check session and access
if (!isset($_SESSION['username']) || !isset($_SESSION['random_code'])) {
    header("Location: ../../../../Loginpage.php");
    exit();
}

$access = $_SESSION['access_level'];
$email = $_SESSION['username'];

echo "<!DOCTYPE html>";
echo "<html><head>";
echo "<title>Live Button Test</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'>";
echo "<style>";
echo "body { background: #f8f9fa; font-family: 'Segoe UI', sans-serif; }";
echo ".debug-info { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin: 10px 0; font-family: monospace; font-size: 14px; }";
echo ".success-msg { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 8px; margin: 10px 0; }";
echo ".error-msg { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 8px; margin: 10px 0; }";
echo ".btn:hover { transform: translateY(-2px); box-shadow: 0 4px 8px rgba(0,0,0,0.2); }";
echo "</style>";
echo "</head><body>";

echo "<div class='container mt-4'>";
echo "<h1>🧪 Live Button Test</h1>";

// Initialize field manager
try {
    $fieldManager = new FieldManager($conn);
    echo "<div class='success-msg'>✅ Field Manager initialized successfully</div>";
} catch (Exception $e) {
    echo "<div class='error-msg'>❌ Error initializing Field Manager: " . $e->getMessage() . "</div>";
    exit();
}

// Handle POST actions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    echo "<div class='debug-info'>";
    echo "<strong>🔄 Processing POST Action:</strong> " . htmlspecialchars($action) . "<br>";
    echo "<strong>User:</strong> " . $email . "<br>";
    echo "<strong>Access:</strong> " . $access . "<br>";
    echo "<strong>Time:</strong> " . date('Y-m-d H:i:s') . "<br>";
    echo "</div>";
    
    if ($access !== 'Admin' && $access !== 'Manager') {
        echo "<div class='error-msg'>❌ Access denied. Only Admin and Manager can perform this action.</div>";
    } else {
        try {
            switch ($action) {
                case 'auto_map':
                    echo "<div class='debug-info'>🔄 Starting auto-map process...</div>";
                    
                    $beforeCount = count($fieldManager->getUnmappedFields());
                    echo "<div class='debug-info'>📊 Before: $beforeCount unmapped fields</div>";
                    
                    $mapped = $fieldManager->autoMapFields();
                    
                    $afterCount = count($fieldManager->getUnmappedFields());
                    echo "<div class='debug-info'>📊 After: $afterCount unmapped fields</div>";
                    
                    $message = "✅ Auto-mapped $mapped fields successfully! ($beforeCount → $afterCount unmapped)";
                    $messageType = 'success';
                    
                    echo "<div class='success-msg'>$message</div>";
                    echo "<div class='debug-info'>🔄 Auto-reload in 3 seconds...</div>";
                    echo "<script>setTimeout(() => window.location.reload(), 3000);</script>";
                    break;
                    
                case 'clean_orphaned':
                    echo "<div class='debug-info'>🔄 Starting clean orphaned process...</div>";
                    
                    $beforeCount = count($fieldManager->getOrphanedFields());
                    echo "<div class='debug-info'>📊 Before: $beforeCount orphaned fields</div>";
                    
                    $cleaned = $fieldManager->cleanOrphanedFields();
                    
                    $afterCount = count($fieldManager->getOrphanedFields());
                    echo "<div class='debug-info'>📊 After: $afterCount orphaned fields</div>";
                    
                    $message = "✅ Cleaned $cleaned orphaned fields successfully! ($beforeCount → $afterCount orphaned)";
                    $messageType = 'success';
                    
                    echo "<div class='success-msg'>$message</div>";
                    echo "<div class='debug-info'>🔄 Auto-reload in 3 seconds...</div>";
                    echo "<script>setTimeout(() => window.location.reload(), 3000);</script>";
                    break;
                    
                default:
                    echo "<div class='error-msg'>⚠️ Unknown action: " . htmlspecialchars($action) . "</div>";
                    break;
            }
        } catch (Exception $e) {
            echo "<div class='error-msg'>";
            echo "<strong>❌ Error processing action:</strong><br>";
            echo "<strong>Message:</strong> " . $e->getMessage() . "<br>";
            echo "<strong>File:</strong> " . $e->getFile() . "<br>";
            echo "<strong>Line:</strong> " . $e->getLine() . "<br>";
            echo "<details><summary>Stack Trace</summary><pre>" . $e->getTraceAsString() . "</pre></details>";
            echo "</div>";
        }
    }
}

// Get current statistics
try {
    $stats = $fieldManager->getFieldStats();
    $unmappedFields = $fieldManager->getUnmappedFields();
    $orphanedFields = $fieldManager->getOrphanedFields();
    
    echo "<div class='row mt-4'>";
    echo "<div class='col-md-12'>";
    echo "<div class='card'>";
    echo "<div class='card-header bg-primary text-white'>";
    echo "<h5 class='mb-0'>📊 Current Statistics</h5>";
    echo "</div>";
    echo "<div class='card-body'>";
    echo "<div class='row text-center'>";
    
    $statItems = [
        ['label' => 'Total DB Columns', 'value' => $stats['total_db_columns'], 'icon' => 'fa-database', 'color' => 'primary'],
        ['label' => 'Configured Fields', 'value' => $stats['configured_fields'], 'icon' => 'fa-cog', 'color' => 'info'],
        ['label' => 'Unmapped Fields', 'value' => $stats['unmapped_fields'], 'icon' => 'fa-question-circle', 'color' => 'warning'],
        ['label' => 'Orphaned Fields', 'value' => $stats['orphaned_fields'], 'icon' => 'fa-trash', 'color' => 'danger']
    ];
    
    foreach ($statItems as $item) {
        echo "<div class='col-md-3'>";
        echo "<div class='card border-{$item['color']}'>";
        echo "<div class='card-body text-center'>";
        echo "<i class='fas {$item['icon']} fa-2x text-{$item['color']} mb-2'></i>";
        echo "<h4 class='mb-0'>{$item['value']}</h4>";
        echo "<small class='text-muted'>{$item['label']}</small>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }
    
    echo "</div>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    // Show unmapped fields details
    if (!empty($unmappedFields)) {
        echo "<div class='card mt-3'>";
        echo "<div class='card-header bg-warning text-dark'>";
        echo "<h5 class='mb-0'>🔍 Unmapped Fields (" . count($unmappedFields) . ")</h5>";
        echo "</div>";
        echo "<div class='card-body'>";
        echo "<div class='row'>";
        foreach ($unmappedFields as $name => $info) {
            echo "<div class='col-md-4 mb-2'>";
            echo "<span class='badge bg-warning text-dark'>$name</span> ";
            echo "<small class='text-muted'>({$info['type']})</small>";
            echo "</div>";
        }
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }
    
    // Show orphaned fields details
    if (!empty($orphanedFields)) {
        echo "<div class='card mt-3'>";
        echo "<div class='card-header bg-danger text-white'>";
        echo "<h5 class='mb-0'>🗑️ Orphaned Fields (" . count($orphanedFields) . ")</h5>";
        echo "</div>";
        echo "<div class='card-body'>";
        echo "<div class='row'>";
        foreach ($orphanedFields as $orphan) {
            echo "<div class='col-md-4 mb-2'>";
            echo "<span class='badge bg-danger'>{$orphan['field']}</span> ";
            echo "<small class='text-muted'>in {$orphan['section']}</small>";
            echo "</div>";
        }
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }
    
    // Action buttons
    echo "<div class='card mt-4'>";
    echo "<div class='card-header bg-success text-white'>";
    echo "<h5 class='mb-0'>🎮 Action Buttons</h5>";
    echo "</div>";
    echo "<div class='card-body'>";
    echo "<div class='d-flex gap-3 flex-wrap'>";
    
    if ($stats['unmapped_fields'] > 0) {
        echo "<form method='POST' class='d-inline'>";
        echo "<input type='hidden' name='action' value='auto_map'>";
        echo "<button type='submit' class='btn btn-success btn-lg' onclick='return confirm(\"Auto-map {$stats['unmapped_fields']} unmapped fields?\");'>";
        echo "<i class='fas fa-magic me-2'></i>";
        echo "Auto-Map {$stats['unmapped_fields']} Fields";
        echo "</button>";
        echo "</form>";
    } else {
        echo "<button class='btn btn-success btn-lg' disabled>";
        echo "<i class='fas fa-check me-2'></i>";
        echo "All Fields Mapped";
        echo "</button>";
    }
    
    if ($stats['orphaned_fields'] > 0) {
        echo "<form method='POST' class='d-inline'>";
        echo "<input type='hidden' name='action' value='clean_orphaned'>";
        echo "<button type='submit' class='btn btn-warning btn-lg' onclick='return confirm(\"Clean {$stats['orphaned_fields']} orphaned fields? This cannot be undone!\");'>";
        echo "<i class='fas fa-broom me-2'></i>";
        echo "Clean {$stats['orphaned_fields']} Orphaned Fields";
        echo "</button>";
        echo "</form>";
    } else {
        echo "<button class='btn btn-warning btn-lg' disabled>";
        echo "<i class='fas fa-check me-2'></i>";
        echo "No Orphaned Fields";
        echo "</button>";
    }
    
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error-msg'>";
    echo "<strong>❌ Error getting statistics:</strong><br>";
    echo $e->getMessage();
    echo "</div>";
}

// Navigation links
echo "<div class='card mt-4'>";
echo "<div class='card-body text-center'>";
echo "<h5>🔗 Navigation</h5>";
echo "<a href='field_diagnostics.php' class='btn btn-primary me-2'>🔙 Back to Field Diagnostics</a>";
echo "<a href='field_diagnostics.php?debug=1' class='btn btn-info me-2'>🔍 Field Diagnostics (Debug)</a>";
echo "<a href='fix_buttons_issue.php' class='btn btn-secondary'>🔧 Fix Issues</a>";
echo "</div>";
echo "</div>";

echo "</div>"; // container

echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js'></script>";
echo "</body></html>";
?>
