<?php
/**
 * Fix Buttons Issue - Comprehensive diagnosis and repair
 */

session_start();
echo "<h1>🔧 Fix Auto-Map and Clean Buttons Issue</h1>";

// Step 1: Check session and authentication
echo "<h2>Step 1: Authentication Check</h2>";
if (!isset($_SESSION['username'])) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; color: #721c24;'>";
    echo "❌ Not logged in. Please log in first.";
    echo "</div>";
    exit();
} else {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; color: #155724;'>";
    echo "✅ Logged in as: " . $_SESSION['username'];
    echo "</div>";
}

// Step 2: Check file includes
echo "<h2>Step 2: File Includes Check</h2>";
$files = [
    'Database config' => $_SERVER['DOCUMENT_ROOT'].'/config/Databasehost.php',
    'System config' => '../system/database_config.php',
    'Field manager' => __DIR__ . '/field_manager.php',
    'Field config' => __DIR__ . '/field_config.json'
];

$allFilesExist = true;
foreach ($files as $name => $path) {
    $exists = file_exists($path);
    $color = $exists ? '#d4edda' : '#f8d7da';
    $textColor = $exists ? '#155724' : '#721c24';
    $icon = $exists ? '✅' : '❌';
    
    echo "<div style='background: $color; padding: 10px; margin: 5px 0; border-radius: 5px; color: $textColor;'>";
    echo "$icon <strong>$name:</strong> $path";
    if ($exists) {
        echo " (Size: " . filesize($path) . " bytes)";
    }
    echo "</div>";
    
    if (!$exists) $allFilesExist = false;
}

if (!$allFilesExist) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; color: #721c24;'>";
    echo "❌ Some required files are missing. Please check the file paths.";
    echo "</div>";
    exit();
}

// Step 3: Include files and test
echo "<h2>Step 3: Include Files and Initialize</h2>";
try {
    include $_SERVER['DOCUMENT_ROOT'].'/config/Databasehost.php';
    echo "<p>✅ Database config included</p>";
    
    include '../system/database_config.php';
    echo "<p>✅ System config included</p>";
    
    require_once __DIR__ . '/field_manager.php';
    echo "<p>✅ Field manager included</p>";
    
    $fieldManager = new FieldManager($conn);
    echo "<p>✅ Field manager initialized</p>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; color: #721c24;'>";
    echo "❌ Error during initialization: " . $e->getMessage();
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
    echo "</div>";
    exit();
}

// Step 4: Test database connection
echo "<h2>Step 4: Database Connection Test</h2>";
try {
    $result = $conn->query("SELECT 1");
    if ($result) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; color: #155724;'>";
        echo "✅ Database connection is working";
        echo "</div>";
    } else {
        throw new Exception("Query failed: " . $conn->error);
    }
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; color: #721c24;'>";
    echo "❌ Database connection error: " . $e->getMessage();
    echo "</div>";
    exit();
}

// Step 5: Test field manager functions
echo "<h2>Step 5: Field Manager Functions Test</h2>";

try {
    // Test getUnmappedFields
    echo "<h3>Testing getUnmappedFields()</h3>";
    $unmappedFields = $fieldManager->getUnmappedFields();
    echo "<p>✅ Found " . count($unmappedFields) . " unmapped fields</p>";
    
    // Test getOrphanedFields
    echo "<h3>Testing getOrphanedFields()</h3>";
    $orphanedFields = $fieldManager->getOrphanedFields();
    echo "<p>✅ Found " . count($orphanedFields) . " orphaned fields</p>";
    
    // Test getFieldStats
    echo "<h3>Testing getFieldStats()</h3>";
    $stats = $fieldManager->getFieldStats();
    echo "<p>✅ Statistics retrieved successfully</p>";
    echo "<ul>";
    foreach ($stats as $key => $value) {
        echo "<li><strong>$key:</strong> $value</li>";
    }
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; color: #721c24;'>";
    echo "❌ Error testing field manager functions: " . $e->getMessage();
    echo "</div>";
    exit();
}

// Step 6: Handle POST actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    echo "<h2>Step 6: Processing POST Action: " . htmlspecialchars($action) . "</h2>";
    
    try {
        switch ($action) {
            case 'test_auto_map':
                echo "<p>Testing auto-map function...</p>";
                $beforeCount = count($fieldManager->getUnmappedFields());
                echo "<p>Before: $beforeCount unmapped fields</p>";
                
                $mapped = $fieldManager->autoMapFields();
                echo "<p style='color: green;'>✅ Auto-mapped $mapped fields successfully!</p>";
                
                $afterCount = count($fieldManager->getUnmappedFields());
                echo "<p>After: $afterCount unmapped fields</p>";
                break;
                
            case 'test_clean':
                echo "<p>Testing clean orphaned function...</p>";
                $beforeCount = count($fieldManager->getOrphanedFields());
                echo "<p>Before: $beforeCount orphaned fields</p>";
                
                $cleaned = $fieldManager->cleanOrphanedFields();
                echo "<p style='color: green;'>✅ Cleaned $cleaned orphaned fields successfully!</p>";
                
                $afterCount = count($fieldManager->getOrphanedFields());
                echo "<p>After: $afterCount orphaned fields</p>";
                break;
                
            case 'fix_permissions':
                echo "<p>Checking and fixing file permissions...</p>";
                
                $configFile = __DIR__ . '/field_config.json';
                if (is_writable($configFile)) {
                    echo "<p style='color: green;'>✅ Config file is writable</p>";
                } else {
                    echo "<p style='color: red;'>❌ Config file is not writable</p>";
                    if (chmod($configFile, 0666)) {
                        echo "<p style='color: green;'>✅ Fixed permissions</p>";
                    } else {
                        echo "<p style='color: red;'>❌ Could not fix permissions</p>";
                    }
                }
                break;
                
            default:
                echo "<p style='color: orange;'>⚠️ Unknown action: $action</p>";
        }
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; color: #721c24;'>";
        echo "❌ Error processing action: " . $e->getMessage();
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
        echo "</div>";
    }
}

// Step 7: Show action buttons
echo "<h2>Step 7: Test Action Buttons</h2>";
echo "<div style='background: white; padding: 20px; border-radius: 8px; border: 1px solid #dee2e6;'>";

if ($stats['unmapped_fields'] > 0) {
    echo "<form method='POST' style='display: inline-block; margin: 10px;'>";
    echo "<input type='hidden' name='action' value='test_auto_map'>";
    echo "<button type='submit' style='background: #28a745; color: white; border: none; padding: 12px 20px; border-radius: 6px; cursor: pointer;'>";
    echo "🪄 Test Auto-Map ({$stats['unmapped_fields']} fields)";
    echo "</button>";
    echo "</form>";
}

if ($stats['orphaned_fields'] > 0) {
    echo "<form method='POST' style='display: inline-block; margin: 10px;'>";
    echo "<input type='hidden' name='action' value='test_clean'>";
    echo "<button type='submit' style='background: #ffc107; color: black; border: none; padding: 12px 20px; border-radius: 6px; cursor: pointer;'>";
    echo "🧹 Test Clean ({$stats['orphaned_fields']} fields)";
    echo "</button>";
    echo "</form>";
}

echo "<form method='POST' style='display: inline-block; margin: 10px;'>";
echo "<input type='hidden' name='action' value='fix_permissions'>";
echo "<button type='submit' style='background: #007bff; color: white; border: none; padding: 12px 20px; border-radius: 6px; cursor: pointer;'>";
echo "🔧 Fix Permissions";
echo "</button>";
echo "</form>";

echo "</div>";

// Step 8: Final recommendations
echo "<h2>Step 8: Recommendations</h2>";
echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 8px;'>";
echo "<h3>🎯 Next Steps:</h3>";
echo "<ol>";
echo "<li><strong>Test the buttons above</strong> - They should work if all previous steps passed</li>";
echo "<li><strong>Check file permissions</strong> - Make sure field_config.json is writable</li>";
echo "<li><strong>Try the original page</strong> - Go back to field_diagnostics.php and test</li>";
echo "<li><strong>Enable debug mode</strong> - Add ?debug=1 to the URL for detailed logging</li>";
echo "</ol>";

echo "<h3>🔗 Quick Links:</h3>";
echo "<ul>";
echo "<li><a href='field_diagnostics.php?debug=1' target='_blank'>Field Diagnostics (Debug Mode)</a></li>";
echo "<li><a href='simple_test.php' target='_blank'>Simple Button Test</a></li>";
echo "<li><a href='test_buttons.php' target='_blank'>Advanced Button Test</a></li>";
echo "</ul>";
echo "</div>";

// Step 9: System information
echo "<h2>Step 9: System Information</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px;'>";
echo "<ul>";
echo "<li><strong>PHP Version:</strong> " . PHP_VERSION . "</li>";
echo "<li><strong>Server:</strong> " . $_SERVER['SERVER_SOFTWARE'] . "</li>";
echo "<li><strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "</li>";
echo "<li><strong>Current Directory:</strong> " . __DIR__ . "</li>";
echo "<li><strong>Script Name:</strong> " . $_SERVER['SCRIPT_NAME'] . "</li>";
echo "<li><strong>Request Method:</strong> " . $_SERVER['REQUEST_METHOD'] . "</li>";
echo "</ul>";
echo "</div>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background: #f8f9fa;
}

h1, h2, h3 {
    color: #2c3e50;
}

pre {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    font-size: 12px;
    max-height: 200px;
    overflow-y: auto;
}

button:hover {
    opacity: 0.9;
    transform: translateY(-1px);
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
