<?php
/**
 * Field Diagnostics - Analyze and manage field configurations
 */

session_start();
include $_SERVER['DOCUMENT_ROOT'].'/config/Databasehost.php';
include '../system/database_config.php';
require_once __DIR__ . '/field_manager.php';

// Check session and access
if (!isset($_SESSION['username']) || !isset($_SESSION['random_code'])) {
    header("Location: ../../../../Loginpage.php");
    exit();
}

$access = $_SESSION['access_level'];
$email = $_SESSION['username'];

// Debug mode
$debug = isset($_GET['debug']) ? true : false;

if ($debug) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    echo "<div style='background: #fff3cd; padding: 10px; margin: 10px 0; border-radius: 5px; font-family: monospace;'>";
    echo "<strong>🔍 Debug Mode Active</strong><br>";
    echo "Session User: " . $email . "<br>";
    echo "Access Level: " . $access . "<br>";
    echo "Request Method: " . $_SERVER['REQUEST_METHOD'] . "<br>";
    if (!empty($_POST)) {
        echo "POST Data: <pre>" . print_r($_POST, true) . "</pre>";
    }
    echo "</div>";
}

// Check access level
$allowed_roles = ['Admin', 'Super Admin', 'Editor', 'admin', 'super admin', 'editor'];
if (!in_array($access, $allowed_roles)) {
    header("Location: /App/access_denied.php?page=Field Diagnostics&required_role=Admin/Super Admin/Editor");
    exit();
}

$message = '';
$messageType = '';

// Initialize field manager
try {
    $fieldManager = new FieldManager($conn);

    if ($debug) {
        echo "<div style='background: #d4edda; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
        echo "✅ Field Manager initialized successfully";
        echo "</div>";
    }
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; color: #721c24; margin: 10px 0;'>";
    echo "<h3>❌ Error initializing Field Manager</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    if ($debug) {
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    echo "</div>";
    exit();
}

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    if ($debug) {
        echo "<div class='debug-info'>";
        echo "<strong>🔄 Processing POST Action:</strong> " . htmlspecialchars($action) . "<br>";
        echo "<strong>POST Data:</strong> <pre>" . print_r($_POST, true) . "</pre>";
        echo "<strong>Session User:</strong> " . $email . "<br>";
        echo "<strong>Access Level:</strong> " . $access . "<br>";
        echo "</div>";
    }

    // Verify user has permission
    if ($access !== 'Admin' && $access !== 'Manager') {
        $message = "❌ Access denied. Only Admin and Manager can perform this action.";
        $messageType = 'danger';
        if ($debug) echo "<p style='color: red;'>$message</p>";
    } else {
        try {
            switch ($action) {
                case 'auto_map':
                    if ($debug) echo "<p>🔄 Calling autoMapFields()...</p>";

                    // Get before count
                    $beforeCount = count($fieldManager->getUnmappedFields());
                    if ($debug) echo "<p>Before: $beforeCount unmapped fields</p>";

                    $mapped = $fieldManager->autoMapFields();

                    // Get after count
                    $afterCount = count($fieldManager->getUnmappedFields());
                    if ($debug) echo "<p>After: $afterCount unmapped fields</p>";

                    $message = "✅ Auto-mapped $mapped fields to appropriate sections. ($beforeCount → $afterCount unmapped)";
                    $messageType = 'success';
                    if ($debug) echo "<p style='color: green;'>Success: $message</p>";

                    // Add JavaScript to log the result
                    echo "<script>";
                    echo "document.addEventListener('DOMContentLoaded', function() {";
                    echo "console.group('📊 Auto-Map Operation Result');";
                    echo "console.log('⏰ Completion time:', new Date().toLocaleString());";
                    echo "console.log('🎯 Action completed: auto_map');";
                    echo "console.log('✅ Success: true');";
                    echo "console.log('📊 Fields mapped:', $mapped);";
                    echo "console.log('📈 Before count:', $beforeCount);";
                    echo "console.log('📉 After count:', $afterCount);";
                    echo "console.log('💬 Message:', '" . htmlspecialchars($message) . "');";
                    echo "console.groupEnd();";
                    echo "});";
                    echo "</script>";

                    // Force page reload to show changes
                    if (!$debug) {
                        echo "<script>setTimeout(() => window.location.reload(), 1000);</script>";
                    }
                    break;

                case 'clean_orphaned':
                    if ($debug) echo "<p>🔄 Calling cleanOrphanedFields()...</p>";

                    // Get before count
                    $beforeCount = count($fieldManager->getOrphanedFields());
                    if ($debug) echo "<p>Before: $beforeCount orphaned fields</p>";

                    $cleaned = $fieldManager->cleanOrphanedFields();

                    // Get after count
                    $afterCount = count($fieldManager->getOrphanedFields());
                    if ($debug) echo "<p>After: $afterCount orphaned fields</p>";

                    $message = "✅ Cleaned $cleaned orphaned fields from configuration. ($beforeCount → $afterCount orphaned)";
                    $messageType = 'success';
                    if ($debug) echo "<p style='color: green;'>Success: $message</p>";

                    // Add JavaScript to log the result
                    echo "<script>";
                    echo "document.addEventListener('DOMContentLoaded', function() {";
                    echo "console.group('📊 Clean Orphaned Operation Result');";
                    echo "console.log('⏰ Completion time:', new Date().toLocaleString());";
                    echo "console.log('🎯 Action completed: clean_orphaned');";
                    echo "console.log('✅ Success: true');";
                    echo "console.log('🧹 Fields cleaned:', $cleaned);";
                    echo "console.log('📈 Before count:', $beforeCount);";
                    echo "console.log('📉 After count:', $afterCount);";
                    echo "console.log('💬 Message:', '" . htmlspecialchars($message) . "');";
                    echo "console.groupEnd();";
                    echo "});";
                    echo "</script>";

                    // Force page reload to show changes
                    if (!$debug) {
                        echo "<script>setTimeout(() => window.location.reload(), 1000);</script>";
                    }
                    break;

                default:
                    $message = "⚠️ Unknown action: " . htmlspecialchars($action);
                    $messageType = 'warning';
                    if ($debug) echo "<p style='color: orange;'>Warning: $message</p>";
                    break;
            }
        } catch (Exception $e) {
            $message = "❌ Error processing action: " . $e->getMessage();
            $messageType = 'danger';
            error_log("Field Diagnostics Error: " . $e->getMessage());

            // Add JavaScript to log the error
            echo "<script>";
            echo "document.addEventListener('DOMContentLoaded', function() {";
            echo "console.group('❌ Operation Error');";
            echo "console.log('⏰ Error time:', new Date().toLocaleString());";
            echo "console.log('🎯 Failed action:', '" . htmlspecialchars($action) . "');";
            echo "console.error('💥 Error message:', '" . htmlspecialchars($e->getMessage()) . "');";
            echo "console.log('📁 Error file:', '" . addslashes($e->getFile()) . "');";
            echo "console.log('📍 Error line:', " . $e->getLine() . ");";
            echo "console.groupEnd();";
            echo "});";
            echo "</script>";

            if ($debug) {
                echo "<div class='debug-info' style='background: #f8d7da; color: #721c24;'>";
                echo "<strong>❌ Exception Details:</strong><br>";
                echo "<strong>Message:</strong> " . $e->getMessage() . "<br>";
                echo "<strong>File:</strong> " . $e->getFile() . "<br>";
                echo "<strong>Line:</strong> " . $e->getLine() . "<br>";
                echo "<strong>Stack Trace:</strong><pre>" . $e->getTraceAsString() . "</pre>";
                echo "</div>";
            }
        }
    }
}

// Get diagnostics data
$stats = $fieldManager->getFieldStats();
$unmappedFields = $fieldManager->getUnmappedFields();
$orphanedFields = $fieldManager->getOrphanedFields();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Field Diagnostics</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome 6 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <style>
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 20px;
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            border: none;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .debug-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-0">
                            <i class="fas fa-stethoscope me-2"></i>
                            Field Diagnostics
                        </h1>
                        <p class="text-muted mb-0">Analyze and manage field configurations</p>
                    </div>
                    <div>
                        <a href="field_settings.php" class="btn btn-outline-primary">
                            <i class="fas fa-cogs me-1"></i> Field Settings
                        </a>
                        <a href="../index.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i> Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Messages -->
        <?php if ($message): ?>
            <div class="alert alert-<?= $messageType ?> alert-dismissible fade show">
                <i class="fas fa-<?= $messageType === 'success' ? 'check-circle' : 'exclamation-circle' ?> me-2"></i>
                <?= htmlspecialchars($message) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-database fa-2x text-primary mb-2"></i>
                        <h4 class="mb-0"><?= $stats['total_db_columns'] ?></h4>
                        <small class="text-muted">DB Columns</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-cogs fa-2x text-info mb-2"></i>
                        <h4 class="mb-0"><?= $stats['configured_fields'] ?></h4>
                        <small class="text-muted">Configured</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                        <h4 class="mb-0"><?= $stats['enabled_fields'] ?></h4>
                        <small class="text-muted">Enabled</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-layer-group fa-2x text-warning mb-2"></i>
                        <h4 class="mb-0"><?= $stats['sections'] ?></h4>
                        <small class="text-muted">Sections</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-question-circle fa-2x text-danger mb-2"></i>
                        <h4 class="mb-0"><?= $stats['unmapped_fields'] ?></h4>
                        <small class="text-muted">Unmapped</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-unlink fa-2x text-secondary mb-2"></i>
                        <h4 class="mb-0"><?= $stats['orphaned_fields'] ?></h4>
                        <small class="text-muted">Orphaned</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-tools me-2"></i>
                            Quick Actions
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex gap-2 flex-wrap">
                            <?php if ($stats['unmapped_fields'] > 0): ?>
                                <form method="POST" class="d-inline">
                                    <input type="hidden" name="action" value="auto_map">
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-magic me-1"></i>
                                        Auto-Map <?= $stats['unmapped_fields'] ?> Unmapped Fields
                                    </button>
                                </form>
                            <?php endif; ?>
                            
                            <?php if ($stats['orphaned_fields'] > 0): ?>
                                <form method="POST" class="d-inline" onsubmit="return confirm('This will remove orphaned fields from configuration. Continue?')">
                                    <input type="hidden" name="action" value="clean_orphaned">
                                    <button type="submit" class="btn btn-warning">
                                        <i class="fas fa-broom me-1"></i>
                                        Clean <?= $stats['orphaned_fields'] ?> Orphaned Fields
                                    </button>
                                </form>
                            <?php endif; ?>
                            
                            <a href="../management/manage_columns.php" class="btn btn-primary">
                                <i class="fas fa-columns me-1"></i>
                                Manage Database Columns
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Unmapped Fields -->
            <?php if (!empty($unmappedFields)): ?>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0 text-danger">
                                <i class="fas fa-question-circle me-2"></i>
                                Unmapped Fields (<?= count($unmappedFields) ?>)
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted">These database columns exist but are not configured in forms:</p>
                            <div class="list-group">
                                <?php foreach ($unmappedFields as $fieldName => $fieldInfo): ?>
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong><?= htmlspecialchars($fieldName) ?></strong>
                                            <br>
                                            <small class="text-muted"><?= htmlspecialchars($fieldInfo['type']) ?></small>
                                        </div>
                                        <span class="badge bg-danger">Unmapped</span>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Orphaned Fields -->
            <?php if (!empty($orphanedFields)): ?>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0 text-warning">
                                <i class="fas fa-unlink me-2"></i>
                                Orphaned Fields (<?= count($orphanedFields) ?>)
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted">These fields are configured but don't exist in the database:</p>
                            <div class="list-group">
                                <?php foreach ($orphanedFields as $orphan): ?>
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong><?= htmlspecialchars($orphan['field']) ?></strong>
                                            <br>
                                            <small class="text-muted">Section: <?= htmlspecialchars($orphan['section']) ?></small>
                                        </div>
                                        <span class="badge bg-warning">Orphaned</span>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- All Good Message -->
        <?php if (empty($unmappedFields) && empty($orphanedFields)): ?>
            <div class="row">
                <div class="col-12">
                    <div class="card border-success">
                        <div class="card-body text-center">
                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                            <h4 class="text-success">All Fields Are Properly Configured!</h4>
                            <p class="text-muted">
                                All database columns are mapped to form fields and all configured fields exist in the database.
                            </p>
                            <a href="field_settings.php" class="btn btn-success">
                                <i class="fas fa-cogs me-1"></i>
                                Manage Field Settings
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Enhanced button functionality with detailed console logging
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Field Diagnostics Console Logger Initialized');
            console.log('📊 Page loaded at:', new Date().toLocaleString());
            console.log('👤 Current user access level:', '<?php echo $access; ?>');
            console.log('🔍 Debug mode:', <?php echo $debug ? 'true' : 'false'; ?>);

            // Add loading state to buttons with detailed logging
            const actionButtons = document.querySelectorAll('button[type="submit"]');
            console.log('🔘 Found', actionButtons.length, 'action buttons');

            actionButtons.forEach((button, index) => {
                button.addEventListener('click', function(e) {
                    const form = this.closest('form');
                    const action = form.querySelector('input[name="action"]').value;

                    console.group('🔄 Diagnostics Action #' + (index + 1));
                    console.log('⏰ Timestamp:', new Date().toLocaleString());
                    console.log('🎯 Action:', action);
                    console.log('🔘 Button text:', this.textContent.trim());

                    // Log specific action details
                    switch(action) {
                        case 'auto_map':
                            console.log('🗺️ Auto-mapping unmapped fields to configuration');
                            console.log('📊 This will analyze field names and assign appropriate sections');
                            console.log('⚙️ Fields will be enabled by default');
                            break;
                        case 'clean_orphaned':
                            console.log('🧹 Cleaning orphaned fields from configuration');
                            console.log('🗑️ This will remove fields that no longer exist in database');
                            console.log('⚠️ This action cannot be undone');
                            break;
                        default:
                            console.log('❓ Unknown action type');
                    }

                    // Show confirmation for destructive actions
                    if (action === 'clean_orphaned') {
                        console.log('⚠️ Requesting user confirmation for destructive action');
                        if (!confirm('⚠️ This will permanently remove orphaned fields from the configuration.\n\nAre you sure you want to continue?')) {
                            console.log('❌ User cancelled clean orphaned operation');
                            console.groupEnd();
                            e.preventDefault();
                            return false;
                        }
                        console.log('✅ User confirmed clean orphaned operation');
                    }

                    if (action === 'auto_map') {
                        console.log('🪄 Requesting user confirmation for auto-mapping');
                        if (!confirm('🪄 This will automatically map unmapped fields to appropriate sections.\n\nContinue?')) {
                            console.log('❌ User cancelled auto-map operation');
                            console.groupEnd();
                            e.preventDefault();
                            return false;
                        }
                        console.log('✅ User confirmed auto-map operation');
                    }

                    // Add loading state
                    const originalText = this.innerHTML;
                    this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Processing...';
                    this.disabled = true;
                    console.log('🔄 Button state changed to loading');
                    console.log('📤 Form submission started');
                    console.groupEnd();

                    // Re-enable button after 10 seconds (fallback)
                    setTimeout(() => {
                        this.innerHTML = originalText;
                        this.disabled = false;
                        console.log('🔄 Button state reset (timeout fallback)');
                    }, 10000);
                });
            });

            // Auto-refresh after successful action
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('action_completed')) {
                // Remove the parameter and refresh
                urlParams.delete('action_completed');
                const newUrl = window.location.pathname + (urlParams.toString() ? '?' + urlParams.toString() : '');
                window.history.replaceState({}, '', newUrl);
            }

            // Add tooltips to buttons
            const tooltips = {
                'auto_map': 'Automatically assigns unmapped database columns to appropriate form sections',
                'clean_orphaned': 'Removes configuration entries for fields that no longer exist in the database'
            };

            actionButtons.forEach(button => {
                const form = button.closest('form');
                if (form) {
                    const actionInput = form.querySelector('input[name="action"]');
                    if (actionInput && tooltips[actionInput.value]) {
                        button.title = tooltips[actionInput.value];
                    }
                }
            });
        });

        // Debug mode toggle
        function toggleDebug() {
            const url = new URL(window.location);
            if (url.searchParams.has('debug')) {
                url.searchParams.delete('debug');
            } else {
                url.searchParams.set('debug', '1');
            }
            window.location.href = url.toString();
        }

        // Add debug toggle button if not in debug mode
        if (!window.location.search.includes('debug')) {
            const debugButton = document.createElement('button');
            debugButton.innerHTML = '🔍 Debug Mode';
            debugButton.className = 'btn btn-outline-secondary btn-sm';
            debugButton.style.position = 'fixed';
            debugButton.style.bottom = '20px';
            debugButton.style.right = '20px';
            debugButton.style.zIndex = '1000';
            debugButton.onclick = toggleDebug;
            document.body.appendChild(debugButton);
        }
    </script>
</body>
</html>
