<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

// Database.php - Main database management page
include $_SERVER['DOCUMENT_ROOT'].'/config/Databasehost.php';
include '../system/database_config.php';
include '../system/dynamic_filters.php';

// Check if user is logged in first
if (!isset($_SESSION['username'])) {
    header("Location: ../../../Loginpage.php");
    exit();
}

$email = $_SESSION['username'];
$access = $_SESSION['access_level'];

// Get all columns dynamically from database structure
$allColumns = getAllColumns();

// Ensure filtermode table has all necessary columns
try {
    $existingFilterColumns = [];
    $result = $conn->query("SHOW COLUMNS FROM filtermode");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $existingFilterColumns[] = $row['Field'];
        }
    }

    // Check for missing columns and add them
    $missingColumns = array_diff($allColumns, $existingFilterColumns);
    foreach ($missingColumns as $column) {
        if ($column !== 'Email') { // Skip Email as it should already exist
            $conn->query("ALTER TABLE filtermode ADD COLUMN `$column` TINYINT(1) DEFAULT 1");
        }
    }
} catch (Exception $e) {
    error_log("Error checking/updating filtermode table: " . $e->getMessage());
}

// Legacy column definition (kept for reference)
/*
$allColumns = [
    'ID', 'Module', 'Status', 'Dedication', 'Account', 'SEAT', 'InterpreterName',
    'Language', 'SeatLang', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday',
    'Saturday', 'Sunday', 'Startdate', 'FGEmails', 'Crowd', 'Legal', 'Certified',
    'IMS', 'TTG', 'MW', 'Lango', 'Bar', 'GlB', 'Eff', 'Foc', 'TUS', 'Mav', 'Abs', 'Inlin',
    'LB', 'Propio', 'Vendor', 'Leader', 'ALTAScore', 'ShiftType', 'Floaters',
    'Statusdetails', 'Reason', 'SubReason', 'LastActivitydate', 'AttritionDate',
    'AppliedNotice', 'RehirePotential', 'IMSPassword', 'EmailPassword', 'DateofBirth',
    'requisitions'
];
*/

// Group columns for easier management
$groupColumns = [
    'crowd' => 'Crowd',
    'legal' => 'Legal',
    'certified' => 'Certified',
    'ims' => 'IMS',
    'ttg' => 'TTG',
    'mw' => 'MW',
    'lango' => 'Lango',
    'bar' => 'Bar',
    'glb' => 'GlB',
    'eff' => 'Eff',
    'foc' => 'Foc',
    'tus' => 'TUS',
    'mav' => 'Mav',
    'abs' => 'Abs',
    'inlin' => 'Inlin',
    'lb' => 'LB',
    'propio' => 'Propio'
];

// Handle record deletion
if (isset($_POST['delete_record'])) {
    $id = $_POST['record_id'];
    $response = array();

    // First get the record data before deletion
    $stmt = $conn->prepare("SELECT * FROM databasehc WHERE ID = ?");
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $record = $result->fetch_assoc();
        $record_email = $record['FGEmails'];

        // Log the deletion in audit table
        $audit_stmt = $conn->prepare("INSERT INTO audit_log
                                    (record_id, changed_by, change_date, field_name, old_value, new_value, action, updated_from)
                                    VALUES (?, ?, CURRENT_TIMESTAMP, 'All fields', '', 'Deleted record', 'delete', 'database')");
        $audit_stmt->bind_param("ss", $record_email, $email);
        $audit_stmt->execute();

        // Now delete the record
        $delete_stmt = $conn->prepare("DELETE FROM databasehc WHERE ID = ?");
        $delete_stmt->bind_param("i", $id);

        if ($delete_stmt->execute()) {
            $response['success'] = true;
            $response['message'] = "Record deleted successfully";
        } else {
            $response['success'] = false;
            $response['message'] = "Error deleting record";
        }
    } else {
        $response['success'] = false;
        $response['message'] = "Record not found";
    }

    header('Content-Type: application/json');
    echo json_encode($response);
    exit();
}

// Function to get or create user's column visibility preferences
function getUserColumnPreferences($conn, $email, $allColumns) {
    // Check if user has existing preferences
    $stmt = $conn->prepare("SELECT * FROM filtermode WHERE Email = ?");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        return $result->fetch_assoc();
    } else {
        // Get existing columns in filtermode table
        $existingColumns = [];
        $columnsResult = $conn->query("SHOW COLUMNS FROM filtermode");
        while ($row = $columnsResult->fetch_assoc()) {
            if ($row['Field'] !== 'Email') { // Exclude Email column
                $existingColumns[] = $row['Field'];
            }
        }

        // Filter allColumns to only include columns that exist in filtermode table
        $validColumns = array_intersect($allColumns, $existingColumns);

        if (empty($validColumns)) {
            // If no valid columns, return default with Email only
            return ['Email' => $email];
        }

        // Create default preferences for new user with only valid columns
        try {
            $sql = "INSERT INTO filtermode (Email, " . implode(', ', $validColumns) . ") VALUES (?" . str_repeat(', ?', count($validColumns)) . ")";
            $stmt = $conn->prepare($sql);

            // Bind parameters correctly
            $types = 's' . str_repeat('i', count($validColumns)); // s for string (email), then all integers
            $values = array_merge([$email], array_fill(0, count($validColumns), 1));

            $stmt->bind_param($types, ...$values);
            $stmt->execute();
        } catch (Exception $e) {
            error_log("Error creating user preferences for $email: " . $e->getMessage());
            // Return minimal default preferences
            return ['Email' => $email];
        }

        // Return default preferences
        $defaults = array_fill_keys($validColumns, 1);
        $defaults['Email'] = $email;
        return $defaults;
    }
}

// Get user's column preferences
$columnPreferences = getUserColumnPreferences($conn, $email, $allColumns);

// Handle column visibility updates
if (isset($_POST['updateColumnVisibility'])) {
    $updates = json_decode($_POST['updates'], true);
    $email = $_SESSION['username'];

    // Start transaction
    $conn->begin_transaction();

    try {
        foreach ($updates as $update) {
            $column = $update['column'];
            $visible = $update['isVisible'] ? 1 : 0;

            // Check if column exists in the main table
            $result = $conn->query("SHOW COLUMNS FROM databasehc LIKE '$column'");
            if ($result->num_rows == 0) {
                throw new Exception("Column '$column' does not exist in the database.");
            }

            // Check if column exists in filtermode table, if not add it
            $filterResult = $conn->query("SHOW COLUMNS FROM filtermode LIKE '$column'");
            if ($filterResult->num_rows == 0) {
                // Add column to filtermode table
                $conn->query("ALTER TABLE filtermode ADD COLUMN `$column` TINYINT(1) DEFAULT 1");
            }

            // Update database
            $stmt = $conn->prepare("UPDATE filtermode SET `$column` = ? WHERE Email = ?");
            $stmt->bind_param("is", $visible, $email);
            $stmt->execute();

            // Update local preferences
            $columnPreferences[$column] = $visible;
        }

        $conn->commit();
        echo json_encode(['success' => true]);
    } catch (Exception $e) {
        $conn->rollback();
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }

    exit;
}



// Handle AJAX request for filter options
if (isset($_GET['get_filter_options'])) {
    $whereClauses = [];
    $params = [];
    $types = '';

    // Helper function to handle multi-select filters for filter options
    function addMultiSelectFilterForOptions($fieldName, $columnName, $getData, &$whereClauses, &$params, &$types) {
        if (!empty($getData[$fieldName]) && is_array($getData[$fieldName])) {
            $values = array_filter($getData[$fieldName]); // Remove empty values
            if (!empty($values)) {
                $placeholders = str_repeat('?,', count($values) - 1) . '?';
                $whereClauses[] = "$columnName IN ($placeholders)";
                foreach ($values as $value) {
                    $params[] = $value;
                    $types .= 's';
                }
            }
        }
    }

    // Build filter conditions from current selections
    addMultiSelectFilterForOptions('module', 'Module', $_GET, $whereClauses, $params, $types);
    addMultiSelectFilterForOptions('status', 'Status', $_GET, $whereClauses, $params, $types);
    addMultiSelectFilterForOptions('leader', 'Leader', $_GET, $whereClauses, $params, $types);
    addMultiSelectFilterForOptions('dedication', 'Dedication', $_GET, $whereClauses, $params, $types);
    addMultiSelectFilterForOptions('language', 'SeatLang', $_GET, $whereClauses, $params, $types);

    // Handle startdate filter
    if (!empty($_GET['startdate']) && is_array($_GET['startdate'])) {
        $dates = array_filter($_GET['startdate']);
        if (!empty($dates)) {
            $placeholders = str_repeat('?,', count($dates) - 1) . '?';
            $whereClauses[] = "DATE(Startdate) IN ($placeholders)";
            foreach ($dates as $date) {
                $params[] = date('Y-m-d', strtotime($date));
                $types .= 's';
            }
        }
    }

    // Group filters
    foreach ($groupColumns as $key => $column) {
        addMultiSelectFilterForOptions($key, $column, $_GET, $whereClauses, $params, $types);
    }

    // Handle dynamic filters for AJAX
    foreach ($_GET as $key => $value) {
        // Dynamic dropdown filters
        if (strpos($key, 'dynamic_') === 0 && is_array($value) && !empty($value)) {
            $column = substr($key, 8); // Remove 'dynamic_' prefix (8 characters)
            $values = array_filter($value);
            if (!empty($values)) {
                $placeholders = str_repeat('?,', count($values) - 1) . '?';
                $whereClauses[] = "`$column` IN ($placeholders)";
                foreach ($values as $val) {
                    $params[] = $val;
                    $types .= 's';
                }
                error_log("Applied dynamic filter for column: $column with values: " . implode(', ', $values));
            }
        }

        // Dynamic search filters
        if (strpos($key, 'search_') === 0 && !empty($value)) {
            $column = substr($key, 7); // Remove 'search_' prefix (7 characters)
            $whereClauses[] = "`$column` LIKE ?";
            $params[] = '%' . $value . '%';
            $types .= 's';
            error_log("Applied search filter for column: $column with value: $value");
        }

        // Dynamic range filters
        if (strpos($key, 'range_') === 0 && !empty($value)) {
            if (strpos($key, '_from') !== false) {
                $column = str_replace(['range_', '_from'], '', $key);
                $whereClauses[] = "`$column` >= ?";
                $params[] = $value;
                $types .= 's';
                error_log("Applied range filter (from) for column: $column with value: $value");
            } elseif (strpos($key, '_to') !== false) {
                $column = str_replace(['range_', '_to'], '', $key);
                $whereClauses[] = "`$column` <= ?";
                $params[] = $value;
                $types .= 's';
                error_log("Applied range filter (to) for column: $column with value: $value");
            }
        }
    }

    // Helper function to get filter options with current filters applied for multi-select
    function getFilterOptions($conn, $field, $whereClauses, $params, $types) {
        try {
            $sql = "SELECT DISTINCT $field FROM databasehc WHERE $field IS NOT NULL";

            if (!empty($whereClauses)) {
                // Remove clauses that reference the current field to avoid conflicts
                $filteredClauses = [];
                $filteredParams = [];
                $filteredTypes = '';
                $paramIndex = 0;

                foreach ($whereClauses as $clause) {
                    // Check if this clause references the current field
                    if (strpos($clause, $field) === false) {
                        $filteredClauses[] = $clause;

                        // Count placeholders in this clause
                        $placeholderCount = substr_count($clause, '?');
                        for ($i = 0; $i < $placeholderCount; $i++) {
                            if (isset($params[$paramIndex])) {
                                $filteredParams[] = $params[$paramIndex];
                                $filteredTypes .= $types[$paramIndex];
                            }
                            $paramIndex++;
                        }
                    } else {
                        // Skip params for excluded clauses
                        $placeholderCount = substr_count($clause, '?');
                        $paramIndex += $placeholderCount;
                    }
                }

                if (!empty($filteredClauses)) {
                    $sql .= " AND " . implode(" AND ", $filteredClauses);
                }
            }

            $sql .= " ORDER BY $field";

            $stmt = $conn->prepare($sql);

            if (!empty($filteredParams)) {
                $stmt->bind_param($filteredTypes, ...$filteredParams);
            }

            $stmt->execute();
            $result = $stmt->get_result();

            $options = [];
            while ($row = $result->fetch_assoc()) {
                if ($row[$field] !== null && $row[$field] !== '') {
                    $options[] = $row[$field];
                }
            }

            return $options;
        } catch (Exception $e) {
            error_log("Error in getFilterOptions for field $field: " . $e->getMessage());
            return [];
        }
    }

    // Get updated options for each filter
    $response = [
        'modules' => getFilterOptions($conn, 'Module', $whereClauses, $params, $types),
        'statuses' => getFilterOptions($conn, 'Status', $whereClauses, $params, $types),
        'leaders' => getFilterOptions($conn, 'Leader', $whereClauses, $params, $types),
        'dedications' => getFilterOptions($conn, 'Dedication', $whereClauses, $params, $types),
        'languages' => getFilterOptions($conn, 'SeatLang', $whereClauses, $params, $types),
        'startdates' => getFilterOptions($conn, 'Startdate', $whereClauses, $params, $types),
        'crowd' => getFilterOptions($conn, 'Crowd', $whereClauses, $params, $types),
        'legal' => getFilterOptions($conn, 'Legal', $whereClauses, $params, $types),
        'certified' => getFilterOptions($conn, 'Certified', $whereClauses, $params, $types),
        'ims' => getFilterOptions($conn, 'IMS', $whereClauses, $params, $types),
        'ttg' => getFilterOptions($conn, 'TTG', $whereClauses, $params, $types),
        'mw' => getFilterOptions($conn, 'MW', $whereClauses, $params, $types),
        'lango' => getFilterOptions($conn, 'Lango', $whereClauses, $params, $types),
        'bar' => getFilterOptions($conn, 'Bar', $whereClauses, $params, $types),
        'glb' => getFilterOptions($conn, 'GlB', $whereClauses, $params, $types),
        'eff' => getFilterOptions($conn, 'Eff', $whereClauses, $params, $types),
        'foc' => getFilterOptions($conn, 'Foc', $whereClauses, $params, $types),
        'tus' => getFilterOptions($conn, 'TUS', $whereClauses, $params, $types),
        'mav' => getFilterOptions($conn, 'Mav', $whereClauses, $params, $types),
        'abs' => getFilterOptions($conn, 'Abs', $whereClauses, $params, $types),
        'inlin' => getFilterOptions($conn, 'Inlin', $whereClauses, $params, $types),
        'lb' => getFilterOptions($conn, 'LB', $whereClauses, $params, $types),
        'propio' => getFilterOptions($conn, 'Propio', $whereClauses, $params, $types)
    ];

    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}

// Display upload success message if exists
$uploadSuccess = isset($_GET['upload']) && $_GET['upload'] == 'success';
$uploadMessage = isset($_GET['message']) ? urldecode($_GET['message']) : '';

// Display delete message if exists
$deleteMessage = '';
if (isset($_SESSION['delete_message'])) {
    $deleteMessage = $_SESSION['delete_message'];
    unset($_SESSION['delete_message']);
}

// Pagination setup
$limit = 150;
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$start = ($page - 1) * $limit;

// Search functionality
if (isset($_GET['q'])) {
    $query = $_GET['q'];

    // Use only visible columns for search
    $visibleColumns = array_filter($allColumns, function($col) use ($columnPreferences) {
        return isset($columnPreferences[$col]) && $columnPreferences[$col];
    });

    // Add ID column if not visible (needed for actions)
    if (!in_array('ID', $visibleColumns)) {
        array_unshift($visibleColumns, 'ID');
    }

    $sql = "SELECT * FROM databasehc WHERE ";
    $conditions = [];

    foreach ($visibleColumns as $column) {
        if ($column !== 'Actions') { // Exclude actions column
            $conditions[] = "$column LIKE ?";
        }
    }

    $sql .= implode(" OR ", $conditions) . " LIMIT $start, $limit";

    $stmt = $conn->prepare($sql);
    $searchTerm = "%$query%";
    $params = array_fill(0, count($conditions), $searchTerm);
    $stmt->bind_param(str_repeat('s', count($conditions)), ...$params);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            foreach ($visibleColumns as $column) {
                if ($column !== 'Actions') {
                    echo "<td>" . htmlspecialchars($row[$column] ?? '') . "</td>";
                }
            }
            // Hide entire Actions column for Viewer permission
            if ($access !== 'Viewer') {
                echo "<td>
                    <a href='editdatabase.php?ID=" . htmlspecialchars($row['ID']) . "' class='btn btn-sm btn-outline-primary'><i class='fas fa-edit'></i> Edit</a>";
                    // Hide Delete button for Leader permission
                    if ($access !== 'Leader') {
                        echo "<button class='btn btn-sm btn-outline-danger delete-btn' data-id='" . htmlspecialchars($row['ID']) . "'><i class='fas fa-trash-alt'></i> Delete</button>";
                    }
                echo "</td>";
            }
            echo "</tr>";
        }
    } else {
        $colspan = count($visibleColumns) + ($access !== 'Viewer' ? 1 : 0); // Add 1 for Actions column unless Viewer
        echo "<tr><td colspan='" . $colspan . "' class='text-center py-4'>No data found</td></tr>";
    }
    exit;
}

// Main filter handling with improved date filtering
if (isset($_POST['applyFilters'])) {
    $whereClauses = [];
    $params = [];
    $types = '';

    // Debug logging
    error_log("=== FILTER APPLICATION START ===");
    error_log("POST data: " . json_encode($_POST));

    // Helper function to handle multi-select filters
    function addMultiSelectFilter($fieldName, $columnName, $postData, &$whereClauses, &$params, &$types) {
        if (!empty($postData[$fieldName]) && is_array($postData[$fieldName])) {
            $values = array_filter($postData[$fieldName]); // Remove empty values
            if (!empty($values)) {
                $placeholders = str_repeat('?,', count($values) - 1) . '?';
                $whereClauses[] = "$columnName IN ($placeholders)";
                foreach ($values as $value) {
                    $params[] = $value;
                    $types .= 's';
                }
            }
        }
    }

    // Basic filters - now supporting multiple selections
    addMultiSelectFilter('module', 'Module', $_POST, $whereClauses, $params, $types);
    addMultiSelectFilter('status', 'Status', $_POST, $whereClauses, $params, $types);
    addMultiSelectFilter('leader', 'Leader', $_POST, $whereClauses, $params, $types);
    addMultiSelectFilter('dedication', 'Dedication', $_POST, $whereClauses, $params, $types);
    addMultiSelectFilter('language', 'SeatLang', $_POST, $whereClauses, $params, $types);

    // Improved Startdate filter - supporting multiple dates
    if (!empty($_POST['startdate']) && is_array($_POST['startdate'])) {
        $dates = array_filter($_POST['startdate']); // Remove empty values
        if (!empty($dates)) {
            $placeholders = str_repeat('?,', count($dates) - 1) . '?';
            $whereClauses[] = "DATE(Startdate) IN ($placeholders)";
            foreach ($dates as $date) {
                $params[] = date('Y-m-d', strtotime($date));
                $types .= 's';
            }
        }
    }

    // Group filters using the predefined array - now supporting multiple selections
    foreach ($groupColumns as $key => $column) {
        addMultiSelectFilter($key, $column, $_POST, $whereClauses, $params, $types);
    }

    // Handle dynamic filters
    foreach ($_POST as $key => $value) {
        // Dynamic dropdown filters
        if (strpos($key, 'dynamic_') === 0 && is_array($value) && !empty($value)) {
            $column = substr($key, 8); // Remove 'dynamic_' prefix (8 characters)
            $values = array_filter($value);
            if (!empty($values)) {
                $placeholders = str_repeat('?,', count($values) - 1) . '?';
                $whereClauses[] = "`$column` IN ($placeholders)";
                foreach ($values as $val) {
                    $params[] = $val;
                    $types .= 's';
                }
                error_log("Applied dynamic filter for column: $column with values: " . implode(', ', $values));
            }
        }

        // Dynamic search filters
        if (strpos($key, 'search_') === 0 && !empty($value)) {
            $column = substr($key, 7); // Remove 'search_' prefix (7 characters)
            $whereClauses[] = "`$column` LIKE ?";
            $params[] = '%' . $value . '%';
            $types .= 's';
            error_log("Applied search filter for column: $column with value: $value");
        }

        // Dynamic range filters
        if (strpos($key, 'range_') === 0 && !empty($value)) {
            if (strpos($key, '_from') !== false) {
                $column = str_replace(['range_', '_from'], '', $key);
                $whereClauses[] = "`$column` >= ?";
                $params[] = $value;
                $types .= 's';
                error_log("Applied range filter (from) for column: $column with value: $value");
            } elseif (strpos($key, '_to') !== false) {
                $column = str_replace(['range_', '_to'], '', $key);
                $whereClauses[] = "`$column` <= ?";
                $params[] = $value;
                $types .= 's';
                error_log("Applied range filter (to) for column: $column with value: $value");
            }
        }
    }

    // Build base SQL query
    $sql = "SELECT * FROM databasehc";
    if (!empty($whereClauses)) {
        $sql .= " WHERE " . implode(" AND ", $whereClauses);
    }

    // Debug logging
    error_log("Final SQL: " . $sql);
    error_log("Parameters: " . json_encode($params));
    error_log("Types: " . $types);
    error_log("Where clauses: " . json_encode($whereClauses));

    // Get total results count
    $totalQuery = "SELECT COUNT(*) as total FROM databasehc";
    if (!empty($whereClauses)) {
        $totalQuery .= " WHERE " . implode(" AND ", $whereClauses);
    }

    $totalStmt = $conn->prepare($totalQuery);
    if (!empty($params)) {
        $totalStmt->bind_param($types, ...$params);
    }
    $totalStmt->execute();
    $totalResult = $totalStmt->get_result();
    $totalRow = $totalResult->fetch_assoc();
    $totalRecords = $totalRow['total'];

    // Pagination
    $limit = 150;
    $page = isset($_POST['page']) ? (int)$_POST['page'] : 1;
    $start = ($page - 1) * $limit;
    $sql .= " LIMIT $start, $limit";

    // Execute query
    $stmt = $conn->prepare($sql);
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $result = $stmt->get_result();

    $output = '';
    if ($result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $output .= "<tr>";
            foreach ($allColumns as $column) {
                // Always show ID column and other columns based on preferences
                if ($column === 'ID' || (isset($columnPreferences[$column]) && $columnPreferences[$column])) {
                    $output .= "<td>" . htmlspecialchars($row[$column] ?? '') . "</td>";
                }
            }
            // Hide entire Actions column for Viewer permission
            if ($access !== 'Viewer') {
                $output .= "<td>
                    <a href='editdatabase.php?ID=" . htmlspecialchars($row['ID']) . "' class='btn btn-sm btn-outline-primary'><i class='fas fa-edit'></i> Edit</a>";
                    // Hide Delete button for Leader permission
                    if ($access !== 'Leader') {
                        $output .= "<button class='btn btn-sm btn-outline-danger delete-btn' data-id='" . htmlspecialchars($row['ID']) . "'><i class='fas fa-trash-alt'></i> Delete</button>";
                    }
                $output .= "</td>";
            }
            $output .= "</tr>";
        }
    } else {
        $colspan = count($allColumns) + ($access !== 'Viewer' ? 1 : 0); // Add 1 for Actions column unless Viewer
        $output .= "<tr><td colspan='" . $colspan . "' class='text-center py-4'>
            <div class='empty-state'>
                <i class='fas fa-database'></i>
                <h4>No data found</h4>
                <p>There are no records matching your criteria</p>
            </div>
        </td></tr>";
    }

    // Debug logging
    error_log("Filter results: Total records = $totalRecords, Page = $page");
    error_log("=== FILTER APPLICATION END ===");

    // Return data as JSON
    echo json_encode([
        'table' => $output,
        'total' => $totalRecords,
        'page' => $page
    ]);
    exit;
}

// Filter columns based on user preferences
$visibleColumns = [];
foreach ($allColumns as $column) {
    // Always show ID column and other columns based on preferences
    if ($column === 'ID' || (isset($columnPreferences[$column]) && $columnPreferences[$column])) {
        $visibleColumns[] = $column;
    }
}

// Main query for pagination (without filters)
$sql = "SELECT " . implode(', ', $visibleColumns) . " FROM databasehc LIMIT $start, $limit";
$result = $conn->query($sql);

// Total records for pagination
$totalQuery = "SELECT COUNT(*) as total FROM databasehc";
$totalResult = $conn->query($totalQuery);
$totalRow = $totalResult->fetch_assoc();
$totalRecords = $totalRow['total'];
$totalPages = ceil($totalRecords / $limit);

// Export functionality
if (isset($_POST['export'])) {
    // Get only visible columns
    $visibleColumns = array_filter($allColumns, function($col) use ($columnPreferences) {
        return $col === 'ID' || (isset($columnPreferences[$col]) && $columnPreferences[$col]);
    });

    // Build SQL query with only visible columns
    $sql = "SELECT " . implode(', ', $visibleColumns) . " FROM databasehc";
    $result = $conn->query($sql);

    header('Content-Type: text/csv');
    header('Content-Disposition: attachment;filename="database_export.csv"');
    header('Cache-Control: max-age=0');

    $output = fopen('php://output', 'w');

    // Write file header with only visible columns
    fputcsv($output, $visibleColumns);

    if ($result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            // Order data according to visible columns
            $rowData = [];
            foreach ($visibleColumns as $column) {
                $rowData[] = $row[$column] ?? '';
            }
            fputcsv($output, $rowData);
        }
    }

    fclose($output);
    exit;
}

// Get current table structure to check which columns exist
$tableStructure = $conn->query("DESCRIBE databasehc");
$availableColumns = [];
while ($row = $tableStructure->fetch_assoc()) {
    $availableColumns[] = $row['Field'];
}

// Function to safely get filter options for a column if it exists
function getFilterOptionsIfExists($conn, $columnName, $availableColumns) {
    if (in_array($columnName, $availableColumns)) {
        try {
            // Special handling for date columns to avoid empty string and invalid date errors
            if (strtolower($columnName) === 'startdate' || strpos(strtolower($columnName), 'date') !== false) {
                // For date columns, use safe date filtering
                $sql = "SELECT DISTINCT `$columnName` FROM databasehc WHERE `$columnName` IS NOT NULL AND `$columnName` != '' AND `$columnName` != '0000-00-00' AND `$columnName` > '1900-01-01' ORDER BY `$columnName`";
            } else {
                // For non-date columns, use regular filtering
                $sql = "SELECT DISTINCT `$columnName` FROM databasehc WHERE `$columnName` IS NOT NULL AND `$columnName` != '' ORDER BY `$columnName`";
            }

            $result = $conn->query($sql);
            if (!$result) {
                error_log("Query failed for column $columnName: " . $conn->error);
                return false;
            }
            return $result;

        } catch (Exception $e) {
            error_log("Error in getFilterOptionsIfExists for column $columnName: " . $e->getMessage());
            return false;
        }
    }
    return false;
}

// Function to auto-detect filterable columns
function autoDetectFilterableColumns($conn) {
    $filterableColumns = [];
    $result = $conn->query("DESCRIBE databasehc");

    while ($row = $result->fetch_assoc()) {
        $columnName = $row['Field'];
        $columnType = strtolower($row['Type']);

        // Skip columns not suitable for filtering
        if (in_array($columnName, ['ID', 'InterpreterName', 'FGEmails', 'EmailPassword', 'IMSPassword'])) {
            continue;
        }

        // Include columns suitable for filtering
        if (strpos($columnType, 'enum') !== false ||
            strpos($columnType, 'varchar') !== false ||
            strpos($columnType, 'text') !== false ||
            strpos($columnType, 'date') !== false) {

            $filterableColumns[] = $columnName;
        }
    }

    return $filterableColumns;
}

// Function to generate filter HTML for a column if it exists
function generateFilterHTML($filterData, $filterId, $filterLabel, $columnName) {
    if (!$filterData) return '';

    $html = '<div class="filter-group">';
    $html .= '<label for="' . $filterId . '">' . $filterLabel . '</label>';
    $html .= '<div class="dropdown-checkbox">';
    $html .= '<div class="dropdown-toggle-custom" data-filter="' . $filterId . '">';
    $html .= '<span class="filter-text">Select ' . $filterLabel . '</span>';
    $html .= '<i class="fas fa-chevron-down dropdown-arrow"></i>';
    $html .= '</div>';
    $html .= '<div class="dropdown-menu-custom" id="' . $filterId . '-menu">';

    while ($row = $filterData->fetch_assoc()) {
        $value = htmlspecialchars($row[$columnName]);
        $html .= '<div class="dropdown-item-custom">';
        $html .= '<input type="checkbox" value="' . $value . '" data-filter="' . $filterId . '">';
        $html .= '<span>' . $value . '</span>';
        $html .= '</div>';
    }

    $html .= '<div class="clear-all-btn" data-filter="' . $filterId . '">Clear All</div>';
    $html .= '</div>';
    $html .= '</div>';
    $html .= '</div>';

    return $html;
}

// Function to generate group filter HTML for a column if it exists
function generateGroupFilterHTML($filterData, $filterId, $filterLabel, $columnName, $colClass = 'col-md-3 mb-2') {
    if (!$filterData) return '';

    $html = '<div class="' . $colClass . '">';
    $html .= '<label for="' . $filterId . '">' . $filterLabel . '</label>';
    $html .= '<div class="dropdown-checkbox">';
    $html .= '<div class="dropdown-toggle-custom" data-filter="' . $filterId . '">';
    $html .= '<span class="filter-text">Select ' . $filterLabel . '</span>';
    $html .= '<i class="fas fa-chevron-down dropdown-arrow"></i>';
    $html .= '</div>';
    $html .= '<div class="dropdown-menu-custom" id="' . $filterId . '-menu">';

    while ($row = $filterData->fetch_assoc()) {
        $value = htmlspecialchars($row[$columnName]);
        $html .= '<div class="dropdown-item-custom">';
        $html .= '<input type="checkbox" value="' . $value . '" data-filter="' . $filterId . '">';
        $html .= '<span>' . $value . '</span>';
        $html .= '</div>';
    }

    $html .= '<div class="clear-all-btn" data-filter="' . $filterId . '">Clear All</div>';
    $html .= '</div>';
    $html .= '</div>';
    $html .= '</div>';

    return $html;
}

// Auto-detect filterable columns from database structure
$autoFilterableColumns = autoDetectFilterableColumns($conn);

// Merge with predefined important columns (for backward compatibility)
$priorityColumns = ['Module', 'Status', 'Leader', 'Dedication', 'SeatLang', 'Startdate'];
$filterableColumns = array_unique(array_merge($priorityColumns, $autoFilterableColumns));

// Generate filters dynamically - only for columns that exist
$dynamicFilters = [];
foreach ($filterableColumns as $column) {
    if (in_array($column, $availableColumns)) {
        $filterData = getFilterOptionsIfExists($conn, $column, $availableColumns);
        if ($filterData) {
            $dynamicFilters[$column] = $filterData;
        }
    }
}

// Maintain backward compatibility with existing variable names
$modules = $dynamicFilters['Module'] ?? false;
$statuses = $dynamicFilters['Status'] ?? false;
$leaders = $dynamicFilters['Leader'] ?? false;
$dedications = $dynamicFilters['Dedication'] ?? false;
$languages = $dynamicFilters['SeatLang'] ?? false;
$startDates = $dynamicFilters['Startdate'] ?? false;
$crowdValues = $dynamicFilters['Crowd'] ?? false;
$legalValues = $dynamicFilters['Legal'] ?? false;
$certifiedValues = $dynamicFilters['Certified'] ?? false;
$imsValues = $dynamicFilters['IMS'] ?? false;
$ttgValues = $dynamicFilters['TTG'] ?? false;
$mwValues = $dynamicFilters['MW'] ?? false;
$langoValues = $dynamicFilters['Lango'] ?? false;
$barValues = $dynamicFilters['Bar'] ?? false;
$glbValues = $dynamicFilters['GlB'] ?? false;
$effValues = $dynamicFilters['Eff'] ?? false;
$focValues = getFilterOptionsIfExists($conn, 'Foc', $availableColumns);
$tusValues = getFilterOptionsIfExists($conn, 'TUS', $availableColumns);
$mavValues = getFilterOptionsIfExists($conn, 'Mav', $availableColumns);
$absValues = getFilterOptionsIfExists($conn, 'Abs', $availableColumns);
$inlinValues = getFilterOptionsIfExists($conn, 'Inlin', $availableColumns);
$lbValues = getFilterOptionsIfExists($conn, 'LB', $availableColumns);
$propioValues = getFilterOptionsIfExists($conn, 'Propio', $availableColumns);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Management System</title>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="/App/assets/css/Database.css">

    <!-- Datepicker CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">

    <style>
        /* Dropdown Filter Styles */
        .dropdown-checkbox {
            position: relative;
            display: inline-block;
            width: 100%;
        }

        .dropdown-toggle-custom {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            background-color: #fff;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .dropdown-toggle-custom:hover {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }

        .dropdown-menu-custom {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            z-index: 1000;
            display: none;
            max-height: 200px;
            overflow-y: auto;
            background-color: #fff;
            border: 1px solid #ced4da;
            border-radius: 4px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .dropdown-menu-custom.show {
            display: block;
        }

        .dropdown-item-custom {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .dropdown-item-custom:hover {
            background-color: #f8f9fa;
        }

        .dropdown-item-custom input[type="checkbox"] {
            margin-right: 8px;
        }

        .clear-all-btn {
            padding: 8px 12px;
            background-color: #dc3545;
            color: white;
            text-align: center;
            cursor: pointer;
            border-top: 1px solid #ced4da;
            transition: background-color 0.2s;
        }

        .clear-all-btn:hover {
            background-color: #c82333;
        }

        .selected-count {
            background-color: #0d6efd;
            color: white;
            border-radius: 12px;
            padding: 2px 8px;
            font-size: 12px;
            margin-left: 8px;
        }

        .dropdown-arrow {
            transition: transform 0.3s ease;
        }

        .dropdown-toggle-custom.active .dropdown-arrow {
            transform: rotate(180deg);
        }

        /* Loading indicator for filters */
        .dropdown-toggle-custom.loading {
            opacity: 0.7;
            pointer-events: none;
            position: relative;
        }

        .dropdown-toggle-custom.loading::after {
            content: '';
            position: absolute;
            right: 30px;
            top: 50%;
            transform: translateY(-50%);
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #0d6efd;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: translateY(-50%) rotate(0deg); }
            100% { transform: translateY(-50%) rotate(360deg); }
        }

        /* Filter update notification */
        .filter-update-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 10px 15px;
            border-radius: 5px;
            z-index: 999999;
            font-size: 0.9rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            animation: slideInRight 0.3s ease-out;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* Filter updated visual feedback */
        .dropdown-toggle-custom.filter-updated {
            background-color: #e7f3ff !important;
            border-color: #0d6efd !important;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25) !important;
            transition: all 0.3s ease;
        }
    </style>

</head>
<body class="database-main">
    <!-- Main Header -->
    <header class="main-header animate__animated animate__fadeIn">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                        <img src="/Logos/kalam.png" alt="Logo" class="img-fluid me-3 animate__animated animate__bounceIn" style="max-height: 60px;">
                        <h1 class="mb-0 animate__animated animate__fadeInRight">Database Management</h1>
                    </div>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="dropdown animate__animated animate__fadeInDown">
                        <button class="btn btn-light dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle me-2"></i><?php echo htmlspecialchars($email); ?>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end animate__animated animate__fadeIn" aria-labelledby="userDropdown">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>Profile</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../../../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>

                        <a href="/App/reports-data/Database/database-analysis" class="btn btn-light">
                            <i class="fas fa-chart-pie me-2"></i> Database Analysis
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <div class="container animate__animated animate__fadeInUp">
        <?php if ($uploadSuccess): ?>
        <div class="alert alert-success alert-dismissible fade show animate__animated animate__bounceIn" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <strong>Success!</strong> <?php echo $uploadMessage; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <?php if ($deleteMessage): ?>
        <div class="alert alert-info alert-dismissible fade show animate__animated animate__bounceIn" role="alert">
            <i class="fas fa-info-circle me-2"></i>
            <strong>Notice:</strong> <?php echo $deleteMessage; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <!-- Action Buttons -->
        <div class="action-buttons-container animate__animated animate__fadeInUp">
            <div class="row">
                <div class="col-md-6">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="searchInput" class="form-control" placeholder="Search across all fields...">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="button-group justify-content-md-end">
                        <button class="btn btn-warning text-white" id="columnVisibilityBtn">
                            <i class="fas fa-columns me-1"></i>Column Visibility
                        </button>
                        <?php
                        // Dashboard button is only visible to Admin, Super Admin, and Editor
                        // Hidden from Viewer, Leader, and Manager permissions
                        if ($access === 'Admin' || $access === 'Super Admin' || $access === 'Editor'):
                        ?>
                        <a href="/App/reports-data/Database/index.php?v=<?php echo time(); ?>" class="btn btn-dark">
                            <i class="fas fa-cogs me-1"></i>Dashboard
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters Section -->
        <div class="filter-section animate__animated animate__fadeInUp">
            <h5 class="mb-3"><i class="fas fa-filter me-2"></i>Filters</h5>
            <form id="filterForm">
                <div class="filter-row">
                    <?php echo generateFilterHTML($modules, 'moduleFilter', 'Module', 'Module'); ?>
                    <?php echo generateFilterHTML($statuses, 'statusFilter', 'Status', 'Status'); ?>
                    <?php echo generateFilterHTML($leaders, 'leaderFilter', 'Leader', 'Leader'); ?>
                    <?php echo generateFilterHTML($dedications, 'dedicationFilter', 'Dedication', 'Dedication'); ?>
                    <?php echo generateFilterHTML($languages, 'languageFilter', 'Language', 'SeatLang'); ?>
                    <?php echo generateFilterHTML($startDates, 'startdateFilter', 'Start Date', 'Startdate'); ?>

                    <!-- Button to open group filters -->
                    <div class="filter-group">
                        <button type="button" id="openGroupFilters" class="btn btn-outline-primary w-100">
                            <i class="fas fa-layer-group me-1"></i> Group Filters
                        </button>
                    </div>

                    <div class="filter-group">
                        <button type="button" id="resetFilters" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-redo me-1"></i> Reset
                        </button>
                    </div>
                </div>

                <!-- Group filters container (hidden by default) -->
                <div id="groupFiltersContainer" class="mt-3 p-3 bg-light rounded" style="display: none;">
                    <h6 class="mb-3"><i class="fas fa-filter-circle-dollar me-2"></i>Group Filters</h6>
                    <div class="row">
                        <?php
                        // Generate all group filters dynamically
                        echo generateGroupFilterHTML($crowdValues, 'crowdFilter', 'Crowd', 'Crowd');
                        echo generateGroupFilterHTML($legalValues, 'legalFilter', 'Legal', 'Legal');
                        echo generateGroupFilterHTML($certifiedValues, 'certifiedFilter', 'Certified', 'Certified');
                        echo generateGroupFilterHTML($imsValues, 'imsFilter', 'IMS', 'IMS');
                        echo generateGroupFilterHTML($ttgValues, 'ttgFilter', 'TTG', 'TTG');
                        echo generateGroupFilterHTML($mwValues, 'mwFilter', 'MW', 'MW');
                        echo generateGroupFilterHTML($langoValues, 'langoFilter', 'Lango', 'Lango');
                        echo generateGroupFilterHTML($barValues, 'barFilter', 'Bar', 'Bar');
                        echo generateGroupFilterHTML($glbValues, 'glbFilter', 'GlB', 'GlB');
                        echo generateGroupFilterHTML($effValues, 'effFilter', 'Eff', 'Eff');
                        echo generateGroupFilterHTML($focValues, 'focFilter', 'Foc', 'Foc');
                        echo generateGroupFilterHTML($tusValues, 'tusFilter', 'TUS', 'TUS');
                        echo generateGroupFilterHTML($mavValues, 'mavFilter', 'Mav', 'Mav');
                        echo generateGroupFilterHTML($absValues, 'absFilter', 'Abs', 'Abs');
                        echo generateGroupFilterHTML($inlinValues, 'inlinFilter', 'Inlin', 'Inlin');
                        echo generateGroupFilterHTML($lbValues, 'lbFilter', 'LB', 'LB');
                        echo generateGroupFilterHTML($propioValues, 'propioFilter', 'Propio', 'Propio');
                        ?>



                        <!-- Additional Dynamic Filters -->
                        <?= generateAdditionalFilters('Database') ?>
                    </div>

                    <div class="mt-2 text-end">
                        <button type="button" id="applyGroupFilters" class="btn btn-sm btn-primary me-2">
                            <i class="fas fa-check me-1"></i> Apply
                        </button>
                        <button type="button" id="closeGroupFilters" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-times me-1"></i> Close
                        </button>
                    </div>
                </div>
            </form>
        </div>



        <!-- Main Table -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-table me-2"></i>Database Records</h5>
                <span class="badge bg-light text-dark">Total: <?php echo $totalRecords; ?> records</span>
            </div>
            <div class="card-body p-0">
                <div class="table-container">
                    <table id="dataTable" class="table table-striped table-hover mb-0">
                        <thead>
                            <tr>
                                <?php foreach ($visibleColumns as $column): ?>
                                    <th><?php echo $column; ?></th>
                                <?php endforeach; ?>
                                <?php if ($access !== 'Viewer'): ?>
                                <th>Actions</th>
                                <?php endif; ?>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            if ($result->num_rows > 0) {
                                while ($row = $result->fetch_assoc()) {
                                    echo "<tr>";
                                    foreach ($visibleColumns as $column) {
                                        echo "<td>" . htmlspecialchars($row[$column] ?? '') . "</td>";
                                    }
                                    // Hide entire Actions column for Viewer permission
                                    if ($access !== 'Viewer') {
                                        echo "<td>
                                            <a href='editdatabase.php?ID=" . htmlspecialchars($row['ID']) . "' class='btn btn-sm btn-outline-primary'><i class='fas fa-edit'></i> Edit</a>";
                                            // Hide Delete button for Leader permission
                                            if ($access !== 'Leader') {
                                                echo "<button class='btn btn-sm btn-outline-danger delete-btn' data-id='" . htmlspecialchars($row['ID']) . "'><i class='fas fa-trash-alt'></i> Delete</button>";
                                            }
                                        echo "</td>";
                                    }
                                    echo "</tr>";
                                }
                            } else {
                                $colspan = count($visibleColumns) + ($access !== 'Viewer' ? 1 : 0); // Add 1 for Actions column unless Viewer
                                echo '<tr><td colspan="' . $colspan . '">
                                    <div class="empty-state">
                                        <i class="fas fa-database"></i>
                                        <h4>No data found</h4>
                                        <p>There are no records in the database</p>
                                    </div>
                                </td></tr>';
                            }
                            ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Pagination -->
            <div class="card-footer">
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center mb-0">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page - 1; ?>" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                        <?php endif; ?>

                        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                            </li>
                        <?php endfor; ?>

                        <?php if ($page < $totalPages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page + 1; ?>" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- Floating Add Button -->
    <a href="addindatabse.php" class="floating-btn animate__animated animate__bounceInUp">
        <i class="fas fa-plus"></i>
    </a>



    <!-- Column Visibility Modal -->
    <div class="modal fade" id="columnVisibilityModal" tabindex="-1" aria-labelledby="columnVisibilityModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="columnVisibilityModalLabel"><i class="fas fa-columns me-2"></i>Column Visibility</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="d-flex justify-content-between mb-3">
                        <button id="showAllColumns" class="btn btn-sm btn-success">
                            <i class="fas fa-eye me-1"></i>Show All
                        </button>
                        <button id="hideAllColumns" class="btn btn-sm btn-danger">
                            <i class="fas fa-eye-slash me-1"></i>Hide All
                        </button>
                    </div>
                    <div class="row" id="columnVisibilityList">
                        <!-- Columns will be populated by JavaScript -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" id="applyColumnVisibility" class="btn btn-primary">
                        <i class="fas fa-check me-1"></i>Apply
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="deleteModalLabel"><i class="fas fa-trash-alt me-2"></i>Confirm Deletion</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="POST" id="deleteForm">
                    <div class="modal-body">
                        <p>Are you sure you want to delete this record? This action cannot be undone.</p>
                        <input type="hidden" name="record_id" id="recordToDelete">
                        <input type="hidden" name="delete_record" value="1">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-danger">Delete</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- jQuery (Load first) -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script>
        // Verify jQuery loaded successfully
        if (typeof jQuery === 'undefined') {
            console.error('❌ jQuery failed to load from cdnjs.cloudflare.com');
            // Try alternative source
            document.write('<script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"><\/script>');
        } else {
            console.log('✅ jQuery loaded successfully from cdnjs.cloudflare.com - Version:', jQuery.fn.jquery);
        }
    </script>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Datepicker JS -->
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>


    <!-- Custom JavaScript -->
    <script>
        // Wait for all scripts to load
        window.addEventListener('load', function() {
            console.log('=== Database Page Debug Info ===');
            console.log('Page fully loaded');

            // Check if jQuery is available
            if (typeof $ !== 'undefined') {
                console.log('✅ jQuery loaded - Version:', $.fn.jquery);

                $(document).ready(function() {
                    console.log('✅ jQuery document ready fired');

                    // Check Bootstrap
                    console.log('Bootstrap available:', typeof bootstrap !== 'undefined' ? '✅ Yes' : '❌ No');

                    // Test if elements exist
                    console.log('Column Visibility Button exists:', $('#columnVisibilityBtn').length > 0 ? '✅ Yes' : '❌ No');
                    console.log('Delete buttons exist:', $('.delete-btn').length + ' found');
                    console.log('Filter dropdowns exist:', $('.dropdown-toggle-custom').length + ' found');

                    // Initialize functionality
                    initializeDatabaseFunctionality();
                });
            } else {
                console.error('❌ jQuery not loaded!');
                alert('jQuery failed to load. Please refresh the page or check your internet connection.');
            }
        });

        function initializeDatabaseFunctionality() {

            // Initialize dropdown checkbox filters
            initializeDropdownFilters();

            // Open and close group filters
            $('#openGroupFilters').click(function() {
                $('#groupFiltersContainer').slideDown();
            });

            $('#closeGroupFilters').click(function() {
                $('#groupFiltersContainer').slideUp();
            });

            // Apply group filters
            $('#applyGroupFilters').click(function() {
                applyFilters(1); // Start from page 1 when applying filter
                $('#groupFiltersContainer').slideUp();
            });

            // Initialize dropdown checkbox functionality
            function initializeDropdownFilters() {
                console.log('Initializing dropdown filters');

                // Toggle dropdown on click
                $(document).on('click', '.dropdown-toggle-custom', function(e) {
                    e.stopPropagation();
                    console.log('Dropdown clicked:', $(this).data('filter'));
                    const $toggle = $(this);
                    const $menu = $toggle.siblings('.dropdown-menu-custom');
                    const $arrow = $toggle.find('.dropdown-arrow');
                    const $container = $toggle.closest('.dropdown-checkbox');

                    // Close all other dropdowns
                    $('.dropdown-menu-custom').not($menu).removeClass('show');
                    $('.dropdown-arrow').not($arrow).removeClass('rotated');
                    $('.dropdown-checkbox').not($container).removeClass('active');

                    // Toggle current dropdown
                    $menu.toggleClass('show');
                    $arrow.toggleClass('rotated');
                    $container.toggleClass('active');
                });

                // Close dropdown when clicking outside
                $(document).on('click', function(e) {
                    if (!$(e.target).closest('.dropdown-checkbox').length) {
                        $('.dropdown-menu-custom').removeClass('show');
                        $('.dropdown-arrow').removeClass('rotated');
                        $('.dropdown-checkbox').removeClass('active');
                    }
                });

                // Handle checkbox changes with debouncing
                let updateTimeout;
                $(document).on('change', '.dropdown-item-custom input[type="checkbox"]', function() {
                    const filterName = $(this).data('filter');
                    updateFilterDisplay(filterName);

                    // Clear previous timeout
                    clearTimeout(updateTimeout);

                    // Set new timeout to avoid too many requests
                    updateTimeout = setTimeout(() => {
                        updateDependentFilters();
                        applyFilters(1);
                    }, 300); // Wait 300ms after last change
                });

                // Handle clear all button
                $(document).on('click', '.clear-all-btn', function(e) {
                    e.stopPropagation();
                    const filterName = $(this).data('filter');
                    $(`input[data-filter="${filterName}"]`).prop('checked', false);
                    updateFilterDisplay(filterName);

                    // Clear previous timeout
                    clearTimeout(updateTimeout);

                    // Set new timeout
                    updateTimeout = setTimeout(() => {
                        updateDependentFilters();
                        applyFilters(1);
                    }, 300);
                });

                // Prevent dropdown from closing when clicking inside
                $(document).on('click', '.dropdown-menu-custom', function(e) {
                    e.stopPropagation();
                });


            }

            // Update filter display text and count
            function updateFilterDisplay(filterName) {
                const $checkedBoxes = $(`input[data-filter="${filterName}"]:checked`);
                const $toggle = $(`.dropdown-toggle-custom[data-filter="${filterName}"]`);
                const $text = $toggle.find('.filter-text');
                const $count = $toggle.find('.selected-count');

                // Remove existing count badge
                $count.remove();

                if ($checkedBoxes.length === 0) {
                    // No selections
                    const defaultTexts = {
                        'moduleFilter': 'Select Modules',
                        'statusFilter': 'Select Status',
                        'leaderFilter': 'Select Leaders',
                        'dedicationFilter': 'Select Dedications',
                        'languageFilter': 'Select Languages',
                        'startdateFilter': 'Select Dates',
                        'crowdFilter': 'Select Crowd',
                        'legalFilter': 'Select Legal',
                        'certifiedFilter': 'Select Certified',
                        'imsFilter': 'Select IMS',
                        'ttgFilter': 'Select TTG',
                        'mwFilter': 'Select MW',
                        'langoFilter': 'Select Lango',
                        'barFilter': 'Select Bar',
                        'glbFilter': 'Select GlB',
                        'effFilter': 'Select Eff',
                        'focFilter': 'Select Foc',
                        'tusFilter': 'Select TUS',
                        'mavFilter': 'Select Mav',
                        'absFilter': 'Select Abs',
                        'inlinFilter': 'Select Inlin',
                        'lbFilter': 'Select LB',
                        'propioFilter': 'Select Propio'
                    };
                    $text.text(defaultTexts[filterName] || 'Select Options');
                } else if ($checkedBoxes.length === 1) {
                    // Single selection - show the value
                    $text.text($checkedBoxes.first().siblings('span').text());
                } else {
                    // Multiple selections - show count
                    const defaultTexts = {
                        'moduleFilter': 'Modules',
                        'statusFilter': 'Status',
                        'leaderFilter': 'Leaders',
                        'dedicationFilter': 'Dedications',
                        'languageFilter': 'Languages',
                        'startdateFilter': 'Dates',
                        'crowdFilter': 'Crowd',
                        'legalFilter': 'Legal',
                        'certifiedFilter': 'Certified',
                        'imsFilter': 'IMS',
                        'ttgFilter': 'TTG',
                        'mwFilter': 'MW',
                        'langoFilter': 'Lango',
                        'barFilter': 'Bar',
                        'glbFilter': 'GlB',
                        'effFilter': 'Eff',
                        'focFilter': 'Foc',
                        'tusFilter': 'TUS',
                        'mavFilter': 'Mav',
                        'absFilter': 'Abs',
                        'inlinFilter': 'Inlin',
                        'lbFilter': 'LB',
                        'propioFilter': 'Propio'
                    };
                    $text.text(defaultTexts[filterName] || 'Options');
                    $toggle.append(`<span class="selected-count">${$checkedBoxes.length}</span>`);
                }
            }



            // Get selected values for a filter
            function getFilterValues(filterName) {
                const values = [];
                $(`input[data-filter="${filterName}"]:checked`).each(function() {
                    values.push($(this).val());
                });
                return values;
            }

            // Function to update all dependent filters
            function updateDependentFilters() {
                // Show loading indicator
                $('.dropdown-toggle-custom').addClass('loading');

                const filters = {
                    module: getFilterValues('moduleFilter'),
                    status: getFilterValues('statusFilter'),
                    leader: getFilterValues('leaderFilter'),
                    dedication: getFilterValues('dedicationFilter'),
                    language: getFilterValues('languageFilter'),
                    startdate: getFilterValues('startdateFilter'),
                    crowd: getFilterValues('crowdFilter'),
                    legal: getFilterValues('legalFilter'),
                    certified: getFilterValues('certifiedFilter'),
                    ims: getFilterValues('imsFilter'),
                    ttg: getFilterValues('ttgFilter'),
                    mw: getFilterValues('mwFilter'),
                    lango: getFilterValues('langoFilter'),
                    bar: getFilterValues('barFilter'),
                    glb: getFilterValues('glbFilter'),
                    eff: getFilterValues('effFilter'),
                    foc: getFilterValues('focFilter'),
                    tus: getFilterValues('tusFilter'),
                    mav: getFilterValues('mavFilter'),
                    abs: getFilterValues('absFilter'),
                    inlin: getFilterValues('inlinFilter'),
                    lb: getFilterValues('lbFilter'),
                    propio: getFilterValues('propioFilter'),
                    get_filter_options: true
                };

                // Add dynamic dropdown filters
                $('input[type="checkbox"][data-filter*="DynamicFilter"]:checked').each(function() {
                    const filterName = $(this).data('filter');
                    const column = filterName.replace('DynamicFilter', '');
                    const value = $(this).val();

                    if (!filters['dynamic_' + column]) {
                        filters['dynamic_' + column] = [];
                    }
                    filters['dynamic_' + column].push(value);
                });

                // Add dynamic search filters
                if (window.dynamicSearchFilters) {
                    Object.keys(window.dynamicSearchFilters).forEach(column => {
                        filters['search_' + column] = window.dynamicSearchFilters[column];
                    });
                }

                // Add dynamic range filters
                if (window.dynamicRangeFilters) {
                    Object.keys(window.dynamicRangeFilters).forEach(column => {
                        const range = window.dynamicRangeFilters[column];
                        if (range.from) {
                            filters['range_' + column + '_from'] = range.from;
                        }
                        if (range.to) {
                            filters['range_' + column + '_to'] = range.to;
                        }
                    });
                }

                $.ajax({
                    url: '/App/reports-data/Database/database',
                    method: 'GET',
                    data: filters,
                    dataType: 'json',
                    success: function(data) {
                        // Update all filters based on the response
                        updateFilterOptions(data);

                        // Remove loading indicator
                        $('.dropdown-toggle-custom').removeClass('loading');

                        // Show notification if filters were updated
                        showFilterUpdateNotification();
                    },
                    error: function(xhr, status, error) {
                        console.error('Error updating dependent filters:', error);
                        console.error('XHR:', xhr);
                        console.error('Status:', status);
                        $('.dropdown-toggle-custom').removeClass('loading');
                        showToast('Error updating filters: ' + error, 'danger');
                    }
                });
            }

            // Function to update all filter options for dropdown checkboxes
            function updateFilterOptions(data) {
                // Main filters
                updateDropdownOptions('moduleFilter', data.modules || []);
                updateDropdownOptions('statusFilter', data.statuses || []);
                updateDropdownOptions('leaderFilter', data.leaders || []);
                updateDropdownOptions('dedicationFilter', data.dedications || []);
                updateDropdownOptions('languageFilter', data.languages || []);
                updateDropdownOptions('startdateFilter', data.startdates || []);

                // Group filters
                updateDropdownOptions('crowdFilter', data.crowd || []);
                updateDropdownOptions('legalFilter', data.legal || []);
                updateDropdownOptions('certifiedFilter', data.certified || []);
                updateDropdownOptions('imsFilter', data.ims || []);
                updateDropdownOptions('ttgFilter', data.ttg || []);
                updateDropdownOptions('mwFilter', data.mw || []);
                updateDropdownOptions('langoFilter', data.lango || []);
                updateDropdownOptions('barFilter', data.bar || []);
                updateDropdownOptions('glbFilter', data.glb || []);
                updateDropdownOptions('effFilter', data.eff || []);
                updateDropdownOptions('focFilter', data.foc || []);
                updateDropdownOptions('tusFilter', data.tus || []);
                updateDropdownOptions('mavFilter', data.mav || []);
                updateDropdownOptions('absFilter', data.abs || []);
                updateDropdownOptions('inlinFilter', data.inlin || []);
                updateDropdownOptions('lbFilter', data.lb || []);
                updateDropdownOptions('propioFilter', data.propio || []);
            }

            // Helper function to update dropdown checkbox options
            function updateDropdownOptions(filterName, options) {
                const $menu = $(`#${filterName}-menu`);
                const currentValues = getFilterValues(filterName);

                // Store the current state
                const wasOpen = $menu.hasClass('show');

                // Clear existing options (except clear all button)
                $menu.find('.dropdown-item-custom').remove();

                // Add new options
                options.forEach(option => {
                    const value = typeof option === 'object' ? option.value : option;
                    const text = typeof option === 'object' ? option.text : option;
                    const isChecked = currentValues.includes(value) ? 'checked' : '';

                    const $item = $(`
                        <div class="dropdown-item-custom">
                            <input type="checkbox" value="${value}" data-filter="${filterName}" ${isChecked}>
                            <span>${text}</span>
                        </div>
                    `);

                    $menu.find('.clear-all-btn').before($item);
                });

                // Update display
                updateFilterDisplay(filterName);

                // Show visual feedback for updated filters
                const $toggle = $menu.siblings('.dropdown-toggle-custom');
                $toggle.addClass('filter-updated');
                setTimeout(() => {
                    $toggle.removeClass('filter-updated');
                }, 1000);
            }

            // Apply filters with page number
            function applyFilters(page = 1) {
                const filters = {
                    module: getFilterValues('moduleFilter'),
                    status: getFilterValues('statusFilter'),
                    leader: getFilterValues('leaderFilter'),
                    dedication: getFilterValues('dedicationFilter'),
                    language: getFilterValues('languageFilter'),
                    startdate: getFilterValues('startdateFilter'),
                    crowd: getFilterValues('crowdFilter'),
                    legal: getFilterValues('legalFilter'),
                    certified: getFilterValues('certifiedFilter'),
                    ims: getFilterValues('imsFilter'),
                    ttg: getFilterValues('ttgFilter'),
                    mw: getFilterValues('mwFilter'),
                    lango: getFilterValues('langoFilter'),
                    bar: getFilterValues('barFilter'),
                    glb: getFilterValues('glbFilter'),
                    eff: getFilterValues('effFilter'),
                    foc: getFilterValues('focFilter'),
                    tus: getFilterValues('tusFilter'),
                    mav: getFilterValues('mavFilter'),
                    abs: getFilterValues('absFilter'),
                    inlin: getFilterValues('inlinFilter'),
                    lb: getFilterValues('lbFilter'),
                    propio: getFilterValues('propioFilter'),
                    page: page
                };

                // Add dynamic dropdown filters
                $('input[type="checkbox"][data-filter*="DynamicFilter"]:checked').each(function() {
                    const filterName = $(this).data('filter');
                    const column = filterName.replace('DynamicFilter', ''); // Keep original case
                    const value = $(this).val();

                    if (!filters['dynamic_' + column]) {
                        filters['dynamic_' + column] = [];
                    }
                    filters['dynamic_' + column].push(value);
                });

                // Add dynamic search filters
                if (window.dynamicSearchFilters) {
                    Object.keys(window.dynamicSearchFilters).forEach(column => {
                        filters['search_' + column] = window.dynamicSearchFilters[column]; // Keep original case
                    });
                }

                // Add dynamic range filters
                if (window.dynamicRangeFilters) {
                    Object.keys(window.dynamicRangeFilters).forEach(column => {
                        const range = window.dynamicRangeFilters[column];
                        if (range.from) {
                            filters['range_' + column + '_from'] = range.from; // Keep original case
                        }
                        if (range.to) {
                            filters['range_' + column + '_to'] = range.to; // Keep original case
                        }
                    });
                }

                console.log('Applying filters:', filters);

                // Debug: Check for dynamic filters specifically
                Object.keys(filters).forEach(key => {
                    if (key.startsWith('dynamic_') || key.startsWith('search_') || key.startsWith('range_')) {
                        console.log('Dynamic filter found:', key, '=', filters[key]);
                    }
                });

                $.ajax({
                    url: '/App/reports-data/Database/database',
                    method: 'POST',
                    data: {
                        applyFilters: true,
                        ...filters
                    },
                    success: function(response) {
                        const data = typeof response === 'string' ? JSON.parse(response) : response;
                        $('tbody').html(data.table);
                        updatePagination(data.total, data.page);

                        // Update the total records badge
                        $('.card-header .badge').text('Total: ' + data.total + ' records');
                    },
                    error: function() {
                        showToast('Error applying filters', 'danger');
                    }
                });
            }

            // Update pagination
            function updatePagination(totalRecords, currentPage) {
                const limit = 150;
                const totalPages = Math.ceil(totalRecords / limit);

                let paginationHtml = '';

                if (currentPage > 1) {
                    paginationHtml += `
                        <li class="page-item">
                            <a class="page-link" href="#" onclick="applyFilters(${currentPage - 1}); return false;" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>`;
                }

                for (let i = 1; i <= totalPages; i++) {
                    paginationHtml += `
                        <li class="page-item ${i == currentPage ? 'active' : ''}">
                            <a class="page-link" href="#" onclick="applyFilters(${i}); return false;">${i}</a>
                        </li>`;
                }

                if (currentPage < totalPages) {
                    paginationHtml += `
                        <li class="page-item">
                            <a class="page-link" href="#" onclick="applyFilters(${currentPage + 1}); return false;" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>`;
                }

                $('.pagination').html(paginationHtml);
            }

            // Reset filters
            $('#resetFilters').click(function() {
                // Clear all checkbox filters
                $('.dropdown-item-custom input[type="checkbox"]').prop('checked', false);

                // Update all filter displays
                const filterNames = ['moduleFilter', 'statusFilter', 'leaderFilter', 'dedicationFilter',
                                   'languageFilter', 'startdateFilter', 'crowdFilter', 'legalFilter',
                                   'certifiedFilter', 'imsFilter', 'ttgFilter', 'mwFilter', 'langoFilter',
                                   'barFilter', 'glbFilter', 'effFilter', 'focFilter', 'tusFilter',
                                   'mavFilter', 'absFilter', 'inlinFilter', 'lbFilter', 'propioFilter'];

                filterNames.forEach(filterName => {
                    updateFilterDisplay(filterName);
                });

                updateDependentFilters();
                applyFilters(1); // Start from page 1 after reset
            });

            // Search
            $('#searchInput').on('input', function() {
                const query = $(this).val();
                console.log('Search query:', query);

                $.get('/App/reports-data/Database/Database.php?q=' + encodeURIComponent(query), function(data) {
                    console.log('Search response received');
                    $('tbody').html(data);
                }).fail(function(xhr, status, error) {
                    console.error('Search error:', error);
                    showToast('Search error: ' + error, 'danger');
                });
            });

            // User dropdown
            $('#userDropdown').on('click', function(e) {
                e.preventDefault();
                $(this).next('.dropdown-menu').toggleClass('show');
            });

            $(document).on('click', function(e) {
                if (!$(e.target).closest('#userDropdown').length &&
                    !$(e.target).closest('.dropdown-menu').length) {
                    $('.dropdown-menu').removeClass('show');
                }
            });

            // Horizontal scrolling with mouse wheel
            $('.table-container').on('wheel', function(e) {
                if (e.originalEvent.deltaY !== 0) {
                    e.preventDefault();
                    this.scrollLeft += e.originalEvent.deltaY;
                }
            });

            // Column Visibility Functionality
            // Open column visibility modal
            $('#columnVisibilityBtn').click(function() {
                console.log('Column Visibility button clicked');

                // Check if Bootstrap modal is available
                if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
                    const modal = new bootstrap.Modal(document.getElementById('columnVisibilityModal'));
                    modal.show();
                } else if ($.fn.modal) {
                    $('#columnVisibilityModal').modal('show');
                } else {
                    console.error('Bootstrap modal not available');
                    alert('Modal functionality not available. Please refresh the page.');
                    return;
                }

                populateColumnVisibilityList();
            });

            // Populate column visibility list
            function populateColumnVisibilityList() {
                const $list = $('#columnVisibilityList');
                $list.empty();

                const columns = <?php echo json_encode($allColumns); ?>;
                const preferences = <?php echo json_encode($columnPreferences); ?>;

                console.log('Columns:', columns);
                console.log('Preferences:', preferences);

                columns.forEach(column => {
                    const isChecked = column === 'ID' || (preferences[column] == 1);
                    const isDisabled = column === 'ID' ? 'disabled' : '';

                    $list.append(`
                        <div class="col-md-4 mb-2">
                            <div class="form-check">
                                <input class="form-check-input column-checkbox" type="checkbox"
                                       id="col-${column}" data-column="${column}" ${isChecked ? 'checked' : ''} ${isDisabled}>
                                <label class="form-check-label" for="col-${column}">
                                    ${column}
                                </label>
                            </div>
                        </div>
                    `);
                });
            }

            // Show all columns
            $('#showAllColumns').click(function() {
                $('.column-checkbox').prop('checked', true);
            });

            // Hide all columns
            $('#hideAllColumns').click(function() {
                $('.column-checkbox').not('[data-column="ID"]').prop('checked', false);
            });

            // Apply column visibility changes
            $('#applyColumnVisibility').click(function() {
                const updates = [];

                $('.column-checkbox').each(function() {
                    const column = $(this).data('column');
                    const isVisible = $(this).is(':checked');
                    updates.push({column, isVisible});

                    // Update table immediately
                    const columnIndex = $('th').filter(function() {
                        return $(this).text().trim() === column;
                    }).index();

                    if (columnIndex >= 0) {
                        $('th:nth-child(' + (columnIndex + 1) + ')').toggle(isVisible);
                        $('td:nth-child(' + (columnIndex + 1) + ')').toggle(isVisible);
                    }
                });

                // Send all updates to server at once
                $.ajax({
                    url: '/App/reports-data/Database/database',
                    method: 'POST',
                    data: {
                        updateColumnVisibility: true,
                        updates: JSON.stringify(updates)
                    },
                    success: function(response) {
                        // Make sure response is JSON
                        let res = typeof response === 'string' ? JSON.parse(response) : response;

                        if (res.success) {
                            $('#columnVisibilityModal').modal('hide');
                            showToast('Column visibility updated successfully!', 'success');
                        } else {
                            // Show error if operation failed
                            showToast('Error: ' + (res.error || 'Unknown error'), 'danger');
                        }
                    },
                    error: function() {
                        showToast('Error communicating with server', 'danger');
                    }
                });
            });

            // Delete record functionality
            $(document).on('click', '.delete-btn', function() {
                const recordId = $(this).data('id');
                console.log('Delete button clicked for record ID:', recordId);
                $('#recordToDelete').val(recordId);

                // Check if Bootstrap modal is available
                if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
                    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
                    modal.show();
                } else if ($.fn.modal) {
                    $('#deleteModal').modal('show');
                } else {
                    console.error('Bootstrap modal not available');
                    if (confirm('Are you sure you want to delete this record? This action cannot be undone.')) {
                        // Submit form directly if modal not available
                        $('#deleteForm').submit();
                    }
                }
            });

            // Handle delete form submission
            $('#deleteForm').submit(function(e) {
                e.preventDefault();
                const recordId = $('#recordToDelete').val();

                $.ajax({
                    url: '/App/reports-data/Database/database',
                    method: 'POST',
                    data: $(this).serialize(),
                    dataType: 'json',
                    success: function(response) {
                        $('#deleteModal').modal('hide');

                        if (response.success) {
                            // Remove the row from the table
                            $('button[data-id="' + recordId + '"]').closest('tr').fadeOut(400, function() {
                                $(this).remove();
                            });

                            // Show success message
                            showToast(response.message, 'success');
                        } else {
                            // Show error message
                            showToast(response.message, 'danger');
                        }
                    },
                    error: function() {
                        showToast('Error deleting record', 'danger');
                    }
                });
            });

            // Function to show toast notifications
            function showToast(message, type) {
                const toast = $(`
                    <div class="toast align-items-center text-white bg-${type} border-0 position-fixed bottom-0 end-0 m-3" role="alert" aria-live="assertive" aria-atomic="true">
                        <div class="d-flex">
                            <div class="toast-body">
                                ${message}
                            </div>
                            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                        </div>
                    </div>
                `);

                $('body').append(toast);
                const toastInstance = new bootstrap.Toast(toast[0]);
                toastInstance.show();

                toast.on('hidden.bs.toast', function() {
                    toast.remove();
                });
            }

            // Function to show filter update notification
            function showFilterUpdateNotification() {
                // Remove any existing notification
                $('.filter-update-notification').remove();

                // Create and show new notification
                const notification = $(`
                    <div class="filter-update-notification">
                        <i class="fas fa-sync-alt me-2"></i>
                        Filters updated based on current selection
                    </div>
                `);

                $('body').append(notification);

                // Auto-hide after 3 seconds
                setTimeout(() => {
                    notification.fadeOut(300, function() {
                        $(this).remove();
                    });
                }, 3000);
            }

            // Initial load of dependent filters
            updateDependentFilters();
        } // End of initializeDatabaseFunctionality
    </script>

    <!-- Note: jQuery and Bootstrap already loaded at the top of the page -->

    <!-- Dynamic Filters JavaScript -->
    <?= generateFilterJavaScript() ?>
</body>
</html>
