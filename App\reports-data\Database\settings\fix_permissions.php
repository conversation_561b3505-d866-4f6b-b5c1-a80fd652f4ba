<?php
/**
 * Fix Permissions - Comprehensive permission fix
 */

session_start();

// Check if user is admin
if (!isset($_SESSION['username']) || $_SESSION['access_level'] !== 'Admin') {
    die("Access denied. Admin access required.");
}

echo "<!DOCTYPE html>";
echo "<html><head>";
echo "<title>Fix Permissions</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head><body>";

echo "<div class='container mt-4'>";
echo "<h1>🔧 Fix File Permissions</h1>";

$settingsDir = __DIR__;
$configFile = $settingsDir . '/field_config.json';

echo "<div class='alert alert-info'>";
echo "<h4>📁 Directory Information</h4>";
echo "<p><strong>Settings Directory:</strong> $settingsDir</p>";
echo "<p><strong>Config File:</strong> $configFile</p>";
echo "<p><strong>Current User:</strong> " . get_current_user() . "</p>";
echo "<p><strong>PHP User:</strong> " . (function_exists('posix_getpwuid') ? posix_getpwuid(posix_geteuid())['name'] : 'Unknown') . "</p>";
echo "</div>";

// Method 1: Try different permission levels
echo "<h2>🔐 Method 1: Fix File Permissions</h2>";

$permissionLevels = [0777, 0755, 0644, 0666];
$permissionFixed = false;

foreach ($permissionLevels as $perm) {
    echo "<p>Trying permission " . decoct($perm) . "...</p>";
    
    if (file_exists($configFile)) {
        if (chmod($configFile, $perm)) {
            if (is_writable($configFile)) {
                echo "<div class='alert alert-success'>✅ Fixed with permission " . decoct($perm) . "</div>";
                $permissionFixed = true;
                break;
            }
        }
    }
}

// Method 2: Try to fix directory permissions
if (!$permissionFixed) {
    echo "<h2>📁 Method 2: Fix Directory Permissions</h2>";
    
    foreach ($permissionLevels as $perm) {
        echo "<p>Trying directory permission " . decoct($perm) . "...</p>";
        
        if (chmod($settingsDir, $perm)) {
            if (is_writable($settingsDir)) {
                echo "<div class='alert alert-success'>✅ Fixed directory with permission " . decoct($perm) . "</div>";
                
                // Now try to create/fix the file
                if (!file_exists($configFile)) {
                    $defaultConfig = [
                        'basic_info' => [
                            'title' => 'Basic Information',
                            'icon' => 'fa-user',
                            'fields' => []
                        ]
                    ];
                    
                    if (file_put_contents($configFile, json_encode($defaultConfig, JSON_PRETTY_PRINT))) {
                        echo "<div class='alert alert-success'>✅ Created config file</div>";
                        $permissionFixed = true;
                        break;
                    }
                }
            }
        }
    }
}

// Method 3: Use alternative config location
if (!$permissionFixed) {
    echo "<h2>📂 Method 3: Alternative Config Location</h2>";
    
    $altConfigDir = $_SERVER['DOCUMENT_ROOT'] . '/temp';
    $altConfigFile = $altConfigDir . '/field_config.json';
    
    // Create temp directory if it doesn't exist
    if (!is_dir($altConfigDir)) {
        if (mkdir($altConfigDir, 0777, true)) {
            echo "<div class='alert alert-info'>📁 Created temp directory: $altConfigDir</div>";
        }
    }
    
    if (is_dir($altConfigDir) && is_writable($altConfigDir)) {
        $defaultConfig = [
            'basic_info' => [
                'title' => 'Basic Information',
                'icon' => 'fa-user',
                'fields' => []
            ]
        ];
        
        if (file_put_contents($altConfigFile, json_encode($defaultConfig, JSON_PRETTY_PRINT))) {
            echo "<div class='alert alert-success'>✅ Created alternative config file: $altConfigFile</div>";
            
            // Create a symlink or copy
            if (function_exists('symlink')) {
                if (symlink($altConfigFile, $configFile)) {
                    echo "<div class='alert alert-success'>✅ Created symlink to alternative config</div>";
                    $permissionFixed = true;
                }
            } else {
                if (copy($altConfigFile, $configFile)) {
                    echo "<div class='alert alert-success'>✅ Copied alternative config to original location</div>";
                    $permissionFixed = true;
                }
            }
        }
    }
}

// Method 4: Use database storage
if (!$permissionFixed) {
    echo "<h2>🗄️ Method 4: Database Storage Fallback</h2>";
    
    include $_SERVER['DOCUMENT_ROOT'].'/config/Databasehost.php';
    include '../system/database_config.php';
    
    try {
        // Create config table if it doesn't exist
        $createTableSQL = "
            CREATE TABLE IF NOT EXISTS field_config_storage (
                id INT PRIMARY KEY AUTO_INCREMENT,
                config_key VARCHAR(255) UNIQUE,
                config_data TEXT,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ";
        
        if ($conn->query($createTableSQL)) {
            echo "<div class='alert alert-success'>✅ Created/verified field_config_storage table</div>";
            
            // Insert default config
            $defaultConfig = json_encode([
                'basic_info' => [
                    'title' => 'Basic Information',
                    'icon' => 'fa-user',
                    'fields' => []
                ]
            ]);
            
            $insertSQL = "INSERT INTO field_config_storage (config_key, config_data) VALUES ('main_config', ?) 
                         ON DUPLICATE KEY UPDATE config_data = VALUES(config_data)";
            $stmt = $conn->prepare($insertSQL);
            $stmt->bind_param("s", $defaultConfig);
            
            if ($stmt->execute()) {
                echo "<div class='alert alert-success'>✅ Stored config in database as fallback</div>";
                $permissionFixed = true;
            }
        }
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>❌ Database fallback failed: " . $e->getMessage() . "</div>";
    }
}

// Create working functions with database fallback
if ($permissionFixed) {
    echo "<h2>🔧 Creating Working Functions</h2>";
    
    $workingFunctionsCode = '<?php
/**
 * Working Field Functions with Database Fallback
 */

function getConfigFilePath() {
    $configFile = __DIR__ . \'/field_config.json\';
    if (file_exists($configFile) && is_writable($configFile)) {
        return $configFile;
    }
    
    $altConfigFile = $_SERVER[\'DOCUMENT_ROOT\'] . \'/temp/field_config.json\';
    if (file_exists($altConfigFile) && is_writable($altConfigFile)) {
        return $altConfigFile;
    }
    
    return false; // Use database fallback
}

function loadFieldConfiguration() {
    $configFile = getConfigFilePath();
    
    if ($configFile) {
        // Use file storage
        if (file_exists($configFile)) {
            $content = file_get_contents($configFile);
            $config = json_decode($content, true);
            return $config ?: [];
        }
    }
    
    // Use database fallback
    global $conn;
    try {
        $stmt = $conn->prepare("SELECT config_data FROM field_config_storage WHERE config_key = ?");
        $key = "main_config";
        $stmt->bind_param("s", $key);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($row = $result->fetch_assoc()) {
            return json_decode($row[\'config_data\'], true) ?: [];
        }
    } catch (Exception $e) {
        error_log("Database config load error: " . $e->getMessage());
    }
    
    // Return default config
    return [
        \'basic_info\' => [
            \'title\' => \'Basic Information\',
            \'icon\' => \'fa-user\',
            \'fields\' => []
        ]
    ];
}

function saveFieldConfiguration() {
    global $conn, $email;
    
    try {
        // Build config from POST data
        $config = [];
        
        if (isset($_POST[\'sections\'])) {
            foreach ($_POST[\'sections\'] as $sectionKey => $sectionData) {
                $config[$sectionKey] = [
                    \'title\' => $sectionData[\'title\'] ?? ucwords(str_replace(\'_\', \' \', $sectionKey)),
                    \'icon\' => $sectionData[\'icon\'] ?? \'fa-cog\',
                    \'fields\' => []
                ];
                
                if (isset($_POST[\'fields\'][$sectionKey])) {
                    foreach ($_POST[\'fields\'][$sectionKey] as $fieldName => $fieldData) {
                        $config[$sectionKey][\'fields\'][$fieldName] = [
                            \'enabled\' => isset($fieldData[\'enabled\']) ? \'1\' : \'0\',
                            \'type\' => $fieldData[\'type\'] ?? \'text\',
                            \'col\' => $fieldData[\'col\'] ?? \'6\',
                            \'icon\' => $fieldData[\'icon\'] ?? \'fa-edit\',
                            \'source_type\' => $fieldData[\'source_type\'] ?? \'custom\',
                            \'options\' => $fieldData[\'options\'] ?? [],
                            \'label\' => $fieldData[\'label\'] ?? ucwords(str_replace(\'_\', \' \', $fieldName))
                        ];
                    }
                }
            }
        }
        
        $configJson = json_encode($config, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        
        // Try file storage first
        $configFile = getConfigFilePath();
        if ($configFile) {
            if (file_put_contents($configFile, $configJson) !== false) {
                return [\'success\' => true, \'message\' => \'Configuration saved to file successfully\'];
            }
        }
        
        // Use database fallback
        $stmt = $conn->prepare("INSERT INTO field_config_storage (config_key, config_data) VALUES (?, ?) 
                               ON DUPLICATE KEY UPDATE config_data = VALUES(config_data)");
        $key = "main_config";
        $stmt->bind_param("ss", $key, $configJson);
        
        if ($stmt->execute()) {
            return [\'success\' => true, \'message\' => \'Configuration saved to database successfully\'];
        } else {
            return [\'success\' => false, \'message\' => \'Failed to save configuration\'];
        }
        
    } catch (Exception $e) {
        error_log("Save config error: " . $e->getMessage());
        return [\'success\' => false, \'message\' => \'Error saving configuration: \' . $e->getMessage()];
    }
}

function syncWithDatabase() {
    global $conn, $email;
    
    try {
        // Get database columns
        $result = $conn->query("DESCRIBE databasehc");
        if (!$result) {
            return [\'success\' => false, \'message\' => \'Failed to get database structure\'];
        }
        
        $dbColumns = [];
        while ($row = $result->fetch_assoc()) {
            $dbColumns[$row[\'Field\']] = [
                \'type\' => $row[\'Type\'],
                \'null\' => $row[\'Null\'],
                \'key\' => $row[\'Key\'],
                \'default\' => $row[\'Default\'],
                \'extra\' => $row[\'Extra\']
            ];
        }
        
        $config = loadFieldConfiguration();
        
        // Get configured fields
        $configuredFields = [];
        foreach ($config as $section) {
            if (isset($section[\'fields\'])) {
                $configuredFields = array_merge($configuredFields, array_keys($section[\'fields\']));
            }
        }
        
        // Find new fields
        $newFields = array_diff(array_keys($dbColumns), $configuredFields);
        $addedCount = 0;
        
        foreach ($newFields as $fieldName) {
            if (in_array($fieldName, [\'ID\', \'created_at\', \'updated_at\'])) continue;
            
            $section = \'additional\';
            if (!isset($config[$section])) {
                $config[$section] = [
                    \'title\' => \'Additional Fields\',
                    \'icon\' => \'fa-plus\',
                    \'fields\' => []
                ];
            }
            
            $config[$section][\'fields\'][$fieldName] = [
                \'enabled\' => \'1\',
                \'type\' => \'text\',
                \'col\' => \'6\',
                \'icon\' => \'fa-edit\',
                \'source_type\' => \'database\',
                \'options\' => [],
                \'label\' => ucwords(str_replace(\'_\', \' \', $fieldName))
            ];
            
            $addedCount++;
        }
        
        // Save updated config
        $configJson = json_encode($config, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        
        $configFile = getConfigFilePath();
        if ($configFile) {
            file_put_contents($configFile, $configJson);
        } else {
            $stmt = $conn->prepare("INSERT INTO field_config_storage (config_key, config_data) VALUES (?, ?) 
                                   ON DUPLICATE KEY UPDATE config_data = VALUES(config_data)");
            $key = "main_config";
            $stmt->bind_param("ss", $key, $configJson);
            $stmt->execute();
        }
        
        return [\'success\' => true, \'message\' => "Successfully synced. Added $addedCount new fields."];
        
    } catch (Exception $e) {
        return [\'success\' => false, \'message\' => \'Sync error: \' . $e->getMessage()];
    }
}

function bulkDeleteFields() {
    global $conn, $email;
    
    try {
        $selectedFields = $_POST[\'selected_fields\'] ?? [];
        
        if (empty($selectedFields)) {
            return [\'success\' => false, \'message\' => \'No fields selected\'];
        }
        
        $config = loadFieldConfiguration();
        $deletedCount = 0;
        
        foreach ($selectedFields as $fieldIdentifier) {
            $parts = explode(\'|\', $fieldIdentifier);
            if (count($parts) !== 2) continue;
            
            $sectionKey = $parts[0];
            $fieldName = $parts[1];
            
            if (isset($config[$sectionKey][\'fields\'][$fieldName])) {
                unset($config[$sectionKey][\'fields\'][$fieldName]);
                $deletedCount++;
                
                if (empty($config[$sectionKey][\'fields\'])) {
                    unset($config[$sectionKey]);
                }
            }
        }
        
        if ($deletedCount > 0) {
            $configJson = json_encode($config, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            
            $configFile = getConfigFilePath();
            if ($configFile) {
                file_put_contents($configFile, $configJson);
            } else {
                $stmt = $conn->prepare("INSERT INTO field_config_storage (config_key, config_data) VALUES (?, ?) 
                                       ON DUPLICATE KEY UPDATE config_data = VALUES(config_data)");
                $key = "main_config";
                $stmt->bind_param("ss", $key, $configJson);
                $stmt->execute();
            }
            
            return [\'success\' => true, \'message\' => "Successfully deleted $deletedCount fields"];
        }
        
        return [\'success\' => false, \'message\' => \'No fields were deleted\'];
        
    } catch (Exception $e) {
        return [\'success\' => false, \'message\' => \'Delete error: \' . $e->getMessage()];
    }
}
?>';
    
    $workingFunctionsFile = $settingsDir . '/field_functions_working.php';
    if (file_put_contents($workingFunctionsFile, $workingFunctionsCode)) {
        echo "<div class='alert alert-success'>✅ Created working functions file</div>";
    } else {
        echo "<div class='alert alert-warning'>⚠️ Could not create working functions file</div>";
    }
}

// Final status
echo "<h2>📋 Final Status</h2>";

if ($permissionFixed) {
    echo "<div class='alert alert-success'>";
    echo "<h4>✅ Permissions Fixed Successfully!</h4>";
    echo "<p>The system is now ready to use. You can:</p>";
    echo "<ul>";
    echo "<li>Use the field settings page normally</li>";
    echo "<li>Sync with database to add new fields</li>";
    echo "<li>Delete fields using bulk operations</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='mt-3'>";
    echo "<a href='field_settings.php' class='btn btn-primary me-2'>🔙 Go to Field Settings</a>";
    echo "<a href='field_settings.php?debug=1' class='btn btn-info'>🔍 Field Settings (Debug Mode)</a>";
    echo "</div>";
} else {
    echo "<div class='alert alert-danger'>";
    echo "<h4>❌ Could Not Fix Permissions</h4>";
    echo "<p>Please contact your system administrator to:</p>";
    echo "<ul>";
    echo "<li>Set write permissions on: $settingsDir</li>";
    echo "<li>Or create a writable temp directory</li>";
    echo "<li>Or ensure database access for config storage</li>";
    echo "</ul>";
    echo "</div>";
}

echo "</div>"; // container
echo "</body></html>";
?>
