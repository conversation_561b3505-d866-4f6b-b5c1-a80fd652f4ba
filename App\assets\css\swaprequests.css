@import url('root-variables.css');
 
 body {
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            background-color: var(--secondary-color);
            color: var(--text-dark);
            font-size: 0.85rem;
            line-height: 1.4;
            padding-bottom: 3rem;
        }
        
        .header-card {
            background-color: var(--primary-color);
            color: white;
            border-radius: var(--border-radius);
            padding: 0.75rem 1rem;
            margin-bottom: 1rem;
        }
        
        .table-container {
            background: var(--white);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            overflow: hidden;
            border: 1px solid var(--border-color);
        }
        
        .table {
            margin-bottom: 0;
            font-size: 0.8rem;
        }
        
        .table th {
            background-color: var(--primary-color) !important;
            color: var(--white);
            font-weight: 500;
            padding: 0.5rem;
            vertical-align: middle;
        }
        
        .table td {
            padding: 0.5rem;
            vertical-align: middle;
        }
        
        .btn-xs {
            padding: 0.2rem 0.4rem;
            font-size: 0.75rem;
            line-height: 1.2;
            border-radius: var(--border-radius);
        }
        
        .btn-success {
            background-color: var(--success-color);
            border-color: var(--success-color);
        }
        
        .btn-danger {
            background-color: var(--danger-color);
            border-color: var(--danger-color);
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-outline-primary {
            color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-outline-primary:hover {
            background-color: var(--primary-color);
            color: white;
        }
        
        .bi {
            font-size: 0.9rem;
        }
        
        .export-btn {
            position: absolute;
            top: 1rem;
            right: 1rem;
            z-index: 10;
        }
        
        .back-btn {
            position: fixed;
            bottom: 1rem;
            right: 1rem;
            z-index: 10;
        }
        
        .status-approved {
            color: var(--success-color);
            font-weight: 500;
        }
        
        .status-rejected {
            color: var(--danger-color);
            font-weight: 500;
        }
        
        .status-pending {
            color: var(--warning-color);
            font-weight: 500;
        }
        
        .alert {
            padding: 0.5rem 1rem;
            font-size: 0.85rem;
            margin-bottom: 1rem;
        }
        
        @media (max-width: 768px) {
            body {
                font-size: 0.8rem;
            }
            
            .table {
                font-size: 0.75rem;
            }
            
            .btn-xs {
                padding: 0.15rem 0.3rem;
                font-size: 0.7rem;
            }
            
            .header-card {
                padding: 0.5rem;
            }
        }