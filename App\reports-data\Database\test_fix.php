<?php
/**
 * Test script to verify the date fix is working
 */

require_once '../../../config/database.php';

echo "<h2>Testing Date Fix</h2>\n";

try {
    // Connect to database
    $conn = new mysqli($servername, $username, $password, $dbname);
    
    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }
    
    echo "<p>Connected to database successfully.</p>\n";
    
    // Test the original problematic function
    echo "<h3>Testing getFilterOptionsIfExists function</h3>\n";
    
    // Include the fixed function
    require_once 'core/Database.php';
    
    // Get available columns
    $tableStructure = $conn->query("DESCRIBE databasehc");
    $availableColumns = [];
    while ($row = $tableStructure->fetch_assoc()) {
        $availableColumns[] = $row['Field'];
    }
    
    echo "<p>Available columns: " . implode(', ', $availableColumns) . "</p>\n";
    
    // Test the function that was causing the error
    try {
        echo "<p>Testing getFilterOptionsIfExists for Startdate...</p>\n";
        $result = getFilterOptionsIfExists($conn, 'Startdate', $availableColumns);
        
        if ($result) {
            echo "<p style='color: green;'>✓ Function executed successfully!</p>\n";
            echo "<p>Sample Startdate values:</p>\n";
            echo "<ul>\n";
            $count = 0;
            while ($row = $result->fetch_assoc() && $count < 5) {
                echo "<li>" . htmlspecialchars($row['Startdate']) . "</li>\n";
                $count++;
            }
            echo "</ul>\n";
        } else {
            echo "<p style='color: orange;'>⚠ Function returned false (column might not exist)</p>\n";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>\n";
    }
    
    // Test other date columns if they exist
    $dateColumns = ['DateofBirth', 'LastActivitydate', 'AttritionDate'];
    
    foreach ($dateColumns as $column) {
        if (in_array($column, $availableColumns)) {
            echo "<h4>Testing $column</h4>\n";
            try {
                $result = getFilterOptionsIfExists($conn, $column, $availableColumns);
                if ($result) {
                    echo "<p style='color: green;'>✓ $column query executed successfully!</p>\n";
                    $count = $result->num_rows;
                    echo "<p>Found $count distinct values</p>\n";
                } else {
                    echo "<p style='color: orange;'>⚠ $column returned false</p>\n";
                }
            } catch (Exception $e) {
                echo "<p style='color: red;'>✗ Error with $column: " . $e->getMessage() . "</p>\n";
            }
        } else {
            echo "<p style='color: blue;'>ℹ Column $column does not exist in table</p>\n";
        }
    }
    
    // Test direct query to see if there are any problematic values
    echo "<h3>Checking for problematic date values</h3>\n";
    
    $checkSql = "SELECT COUNT(*) as count FROM databasehc WHERE Startdate = '' OR Startdate = '0000-00-00'";
    $checkResult = $conn->query($checkSql);
    
    if ($checkResult) {
        $row = $checkResult->fetch_assoc();
        $problemCount = $row['count'];
        
        if ($problemCount > 0) {
            echo "<p style='color: red;'>⚠ Found $problemCount problematic Startdate values that need fixing</p>\n";
            echo "<p><a href='fix_date_values.php' style='color: blue;'>Click here to run the fix script</a></p>\n";
        } else {
            echo "<p style='color: green;'>✓ No problematic Startdate values found</p>\n";
        }
    }
    
    $conn->close();
    echo "<h3 style='color: green;'>Test completed successfully!</h3>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>\n";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f5f5f5;
}

h2, h3, h4 {
    color: #333;
}

p {
    margin: 10px 0;
    padding: 8px;
    background-color: white;
    border-radius: 4px;
    border-left: 4px solid #ddd;
}

ul {
    background-color: white;
    padding: 15px;
    border-radius: 4px;
    margin: 10px 0;
}

li {
    margin: 5px 0;
}
</style>
