<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('max_execution_time', 300); // 5 minutes
ini_set('memory_limit', '512M');

session_start();
include $_SERVER['DOCUMENT_ROOT'].'/config/Databasehost.php';
require_once __DIR__ . '/../includes/database_functions.php';
include '../system/dynamic_filters.php';

// Check permissions
if (!isset($_SESSION['username'])) {
    header("Location: ../../../Loginpage.php");
    exit();
}

$email = $_SESSION['username'];
$access = $_SESSION['access_level'];

// Get filter values from request (support multiple selections)
$filters = [
    'leader' => isset($_GET['leader']) ? (is_array($_GET['leader']) ? $_GET['leader'] : [$_GET['leader']]) : [],
    'ho_module' => isset($_GET['ho_module']) ? (is_array($_GET['ho_module']) ? $_GET['ho_module'] : [$_GET['ho_module']]) : [],
    'ti_module' => isset($_GET['ti_module']) ? (is_array($_GET['ti_module']) ? $_GET['ti_module'] : [$_GET['ti_module']]) : [],
    'status' => isset($_GET['status']) ? (is_array($_GET['status']) ? $_GET['status'] : [$_GET['status']]) : [],
    'seatlang' => isset($_GET['seatlang']) ? (is_array($_GET['seatlang']) ? $_GET['seatlang'] : [$_GET['seatlang']]) : []
];

// Get dynamic filters
$dynamicFilters = [];
foreach ($_GET as $key => $value) {
    // Dynamic dropdown filters
    if (strpos($key, 'dynamic_') === 0 && !empty($value)) {
        $column = substr($key, 8); // Remove 'dynamic_' prefix
        $dynamicFilters[$column] = is_array($value) ? $value : [$value];
    }

    // Dynamic search filters
    if (strpos($key, 'search_') === 0 && !empty($value)) {
        $column = substr($key, 7); // Remove 'search_' prefix
        $dynamicFilters['search_' . $column] = $value;
    }

    // Dynamic range filters
    if (strpos($key, 'range_') === 0 && !empty($value)) {
        $dynamicFilters[$key] = $value;
    }
}

// Helper function to check if filter is active
function isFilterActive($filterArray, $value) {
    return in_array($value, $filterArray);
}

// Helper function to execute queries
function executeQuery($conn, $query, $params = [], $types = '') {
    try {
        $stmt = $conn->prepare($query);

        if ($stmt === false) {
            error_log("Query preparation error: " . $conn->error . " Query: " . $query);
            throw new Exception("Database query preparation failed: " . $conn->error);
        }

        if (!empty($params)) {
            if (!$stmt->bind_param($types, ...$params)) {
                error_log("Parameter binding error: " . $stmt->error);
                throw new Exception("Parameter binding failed: " . $stmt->error);
            }
        }

        if (!$stmt->execute()) {
            error_log("Query execution error: " . $stmt->error . " Query: " . $query);
            throw new Exception("Query execution failed: " . $stmt->error);
        }

        $result = $stmt->get_result();
        if ($result === false) {
            error_log("Get result error: " . $stmt->error);
            throw new Exception("Failed to get query result: " . $stmt->error);
        }

        return $result;
    } catch (Exception $e) {
        error_log("executeQuery exception: " . $e->getMessage());
        // Return empty result instead of dying
        return new class {
            public function fetch_all($mode = MYSQLI_ASSOC) { return []; }
            public function fetch_assoc() { return null; }
            public function num_rows() { return 0; }
        };
    }
}

// Queries to get filter values
$leaders = executeQuery($conn, "SELECT DISTINCT Supervisor FROM databasehc WHERE Supervisor IS NOT NULL AND Supervisor != '' ORDER BY Supervisor")->fetch_all(MYSQLI_ASSOC);
$ho_modules = executeQuery($conn, "SELECT DISTINCT HO_Payment_Module FROM databasehc WHERE HO_Payment_Module IS NOT NULL AND HO_Payment_Module != '' ORDER BY HO_Payment_Module")->fetch_all(MYSQLI_ASSOC);
$ti_modules = executeQuery($conn, "SELECT DISTINCT TI_Payment_Module FROM databasehc WHERE TI_Payment_Module IS NOT NULL AND TI_Payment_Module != '' ORDER BY TI_Payment_Module")->fetch_all(MYSQLI_ASSOC);
$statuses = executeQuery($conn, "SELECT DISTINCT Status FROM databasehc ORDER BY Status")->fetch_all(MYSQLI_ASSOC);
$seatLangs = executeQuery($conn, "SELECT DISTINCT Language FROM databasehc WHERE Language IS NOT NULL AND Language != '' ORDER BY Language")->fetch_all(MYSQLI_ASSOC);



// Build WHERE clause for queries
$where = "WHERE 1=1";
$params = [];
$types = '';

if (!empty($filters['leader'])) {
    $placeholders = str_repeat('?,', count($filters['leader']) - 1) . '?';
    $where .= " AND Supervisor IN ($placeholders)";
    $params = array_merge($params, $filters['leader']);
    $types .= str_repeat('s', count($filters['leader']));
}

if (!empty($filters['ho_module'])) {
    $placeholders = str_repeat('?,', count($filters['ho_module']) - 1) . '?';
    $where .= " AND HO_Payment_Module IN ($placeholders)";
    $params = array_merge($params, $filters['ho_module']);
    $types .= str_repeat('s', count($filters['ho_module']));
}

if (!empty($filters['ti_module'])) {
    $placeholders = str_repeat('?,', count($filters['ti_module']) - 1) . '?';
    $where .= " AND TI_Payment_Module IN ($placeholders)";
    $params = array_merge($params, $filters['ti_module']);
    $types .= str_repeat('s', count($filters['ti_module']));
}

if (!empty($filters['status'])) {
    $placeholders = str_repeat('?,', count($filters['status']) - 1) . '?';
    $where .= " AND Status IN ($placeholders)";
    $params = array_merge($params, $filters['status']);
    $types .= str_repeat('s', count($filters['status']));
}

if (!empty($filters['seatlang'])) {
    $placeholders = str_repeat('?,', count($filters['seatlang']) - 1) . '?';
    $where .= " AND Language IN ($placeholders)";
    $params = array_merge($params, $filters['seatlang']);
    $types .= str_repeat('s', count($filters['seatlang']));
}

// Handle dynamic filters
foreach ($dynamicFilters as $key => $value) {
    if (strpos($key, 'search_') === 0) {
        // Search filters
        $column = ucfirst(substr($key, 7));
        $where .= " AND `$column` LIKE ?";
        $params[] = '%' . $value . '%';
        $types .= 's';
    } elseif (strpos($key, 'range_') === 0) {
        // Range filters
        if (strpos($key, '_from') !== false) {
            $column = ucfirst(str_replace(['range_', '_from'], '', $key));
            $where .= " AND `$column` >= ?";
            $params[] = $value;
            $types .= 's';
        } elseif (strpos($key, '_to') !== false) {
            $column = ucfirst(str_replace(['range_', '_to'], '', $key));
            $where .= " AND `$column` <= ?";
            $params[] = $value;
            $types .= 's';
        }
    } else {
        // Dropdown filters
        $column = ucfirst($key);
        if (is_array($value) && !empty($value)) {
            $placeholders = str_repeat('?,', count($value) - 1) . '?';
            $where .= " AND `$column` IN ($placeholders)";
            $params = array_merge($params, $value);
            $types .= str_repeat('s', count($value));
        }
    }
}

// Check if any filters are applied
$hasFilters = !empty($filters['leader']) || !empty($filters['ho_module']) || !empty($filters['ti_module']) || !empty($filters['status']) || !empty($filters['seatlang']) || !empty($dynamicFilters);

// Show all data when no filters are applied
$showAllData = !$hasFilters;

// Get current table structure to check which columns exist
$tableStructure = $conn->query("DESCRIBE databasehc");
$availableColumns = [];
while ($row = $tableStructure->fetch_assoc()) {
    $availableColumns[] = $row['Field'];
}

// Function to safely execute query for a column if it exists
function executeQueryIfColumnExists($conn, $columnName, $baseQuery, $where, $params, $types, $availableColumns) {
    if (in_array($columnName, $availableColumns)) {
        return executeQuery($conn, $baseQuery, $params, $types);
    }
    return false;
}

// Main data queries - only execute for existing columns
$leaderCount = executeQueryIfColumnExists($conn, 'Supervisor', "SELECT Supervisor, COUNT(InterpreterName) as count FROM databasehc $where GROUP BY Supervisor", $where, $params, $types, $availableColumns);
$hoModuleCount = executeQueryIfColumnExists($conn, 'HO_Payment_Module', "SELECT HO_Payment_Module, COUNT(InterpreterName) as count FROM databasehc $where GROUP BY HO_Payment_Module", $where, $params, $types, $availableColumns);
$tiModuleCount = executeQueryIfColumnExists($conn, 'TI_Payment_Module', "SELECT TI_Payment_Module, COUNT(InterpreterName) as count FROM databasehc $where GROUP BY TI_Payment_Module", $where, $params, $types, $availableColumns);
$activeHOCount = executeQueryIfColumnExists($conn, 'HO_Payment_Module', "SELECT HO_Payment_Module, COUNT(InterpreterName) as count FROM databasehc $where AND Status = 'In Production' GROUP BY HO_Payment_Module", $where, $params, $types, $availableColumns);
$activeTICount = executeQueryIfColumnExists($conn, 'TI_Payment_Module', "SELECT TI_Payment_Module, COUNT(InterpreterName) as count FROM databasehc $where AND Status = 'In Production' GROUP BY TI_Payment_Module", $where, $params, $types, $availableColumns);
$languageCount = executeQueryIfColumnExists($conn, 'Language', "SELECT Language, COUNT(InterpreterName) as count FROM databasehc $where GROUP BY Language ORDER BY count DESC", $where, $params, $types, $availableColumns);
$shiftTypes = executeQueryIfColumnExists($conn, 'ShiftType', "SELECT ShiftType, COUNT(InterpreterName) as count FROM databasehc $where GROUP BY ShiftType ORDER BY count DESC", $where, $params, $types, $availableColumns);
$statusCount = executeQueryIfColumnExists($conn, 'Status', "SELECT Status, COUNT(InterpreterName) as count FROM databasehc $where GROUP BY Status", $where, $params, $types, $availableColumns);
$subReasons = executeQueryIfColumnExists($conn, 'SubReason', "SELECT SubReason, COUNT(InterpreterName) as count FROM databasehc $where AND SubReason IS NOT NULL AND SubReason != '' GROUP BY SubReason ORDER BY count DESC LIMIT 10", $where, $params, $types, $availableColumns);

// Query for language-HO module distribution data - only if both columns exist
$languageHOModuleDistribution = false;
if (in_array('Language', $availableColumns) && in_array('HO_Payment_Module', $availableColumns)) {
    $languageHOModuleDistribution = executeQuery($conn, "
        SELECT
            Language,
            HO_Payment_Module as Module,
            COUNT(InterpreterName) as count
        FROM databasehc
        $where
        AND Language IS NOT NULL AND Language != ''
        AND HO_Payment_Module IS NOT NULL AND HO_Payment_Module != ''
        GROUP BY Language, HO_Payment_Module
        ORDER BY Language, HO_Payment_Module
    ", $params, $types);
}

// Query for language-TI module distribution data - only if both columns exist
$languageTIModuleDistribution = false;
if (in_array('Language', $availableColumns) && in_array('TI_Payment_Module', $availableColumns)) {
    $languageTIModuleDistribution = executeQuery($conn, "
        SELECT
            Language,
            TI_Payment_Module as Module,
            COUNT(InterpreterName) as count
        FROM databasehc
        $where
        AND Language IS NOT NULL AND Language != ''
        AND TI_Payment_Module IS NOT NULL AND TI_Payment_Module != ''
        GROUP BY Language, TI_Payment_Module
        ORDER BY Language, TI_Payment_Module
    ", $params, $types);
}

// Query for language distribution (removed Monday shift as column doesn't exist)
$languageDistribution = executeQuery($conn, "
    SELECT
        Language,
        COUNT(InterpreterName) as count
    FROM databasehc
    $where
    AND Language IS NOT NULL AND Language != ''
    GROUP BY Language
    HAVING COUNT(InterpreterName) > 0
    ORDER BY count DESC
    LIMIT 15
", $params, $types);

// Prepare data for the language-HO module table
$languageHOModuleData = [];
$uniqueLanguagesHO = [];
$uniqueHOModulesForLanguage = [];

if ($languageHOModuleDistribution) {
    while ($row = $languageHOModuleDistribution->fetch_assoc()) {
        $language = $row['Language'] ?: 'N/A';
        $module = $row['Module'];

        if (!in_array($module, $uniqueHOModulesForLanguage)) {
            $uniqueHOModulesForLanguage[] = $module;
        }

        if (!in_array($language, $uniqueLanguagesHO)) {
            $uniqueLanguagesHO[] = $language;
        }

        $languageHOModuleData[$language][$module] = $row['count'];
    }
}

// Prepare data for the language-TI module table
$languageTIModuleData = [];
$uniqueLanguagesTI = [];
$uniqueTIModulesForLanguage = [];

if ($languageTIModuleDistribution) {
    while ($row = $languageTIModuleDistribution->fetch_assoc()) {
        $language = $row['Language'] ?: 'N/A';
        $module = $row['Module'];

        if (!in_array($module, $uniqueTIModulesForLanguage)) {
            $uniqueTIModulesForLanguage[] = $module;
        }

        if (!in_array($language, $uniqueLanguagesTI)) {
            $uniqueLanguagesTI[] = $language;
        }

        $languageTIModuleData[$language][$module] = $row['count'];
    }
}

// Sort modules and languages
sort($uniqueHOModulesForLanguage);
sort($uniqueLanguagesHO);
sort($uniqueTIModulesForLanguage);
sort($uniqueLanguagesTI);

// Prepare data for the language distribution table
$languageDistributionData = [];
$languageTotals = [];

if ($languageDistribution) {
    while ($row = $languageDistribution->fetch_assoc()) {
        $language = $row['Language'] ?: 'N/A';
        $count = $row['count'];

        $languageDistributionData[$language] = $count;
        $languageTotals[$language] = $count;
    }
}

// Sort languages by count (top 15 already limited in query)
arsort($languageTotals);
$top15Languages = array_keys($languageTotals);

// Query for supervisor-HO module distribution data - only if both columns exist
$supervisorHOModuleDistribution = false;
if (in_array('Supervisor', $availableColumns) && in_array('HO_Payment_Module', $availableColumns)) {
    $supervisorHOModuleDistribution = executeQuery($conn, "
        SELECT
            Supervisor,
            HO_Payment_Module as Module,
            COUNT(InterpreterName) as count
        FROM databasehc
        $where
        AND Supervisor IS NOT NULL AND Supervisor != ''
        AND HO_Payment_Module IS NOT NULL AND HO_Payment_Module != ''
        GROUP BY Supervisor, HO_Payment_Module
        ORDER BY Supervisor, HO_Payment_Module
    ", $params, $types);
}

// Query for supervisor-TI module distribution data - only if both columns exist
$supervisorTIModuleDistribution = false;
if (in_array('Supervisor', $availableColumns) && in_array('TI_Payment_Module', $availableColumns)) {
    $supervisorTIModuleDistribution = executeQuery($conn, "
        SELECT
            Supervisor,
            TI_Payment_Module as Module,
            COUNT(InterpreterName) as count
        FROM databasehc
        $where
        AND Supervisor IS NOT NULL AND Supervisor != ''
        AND TI_Payment_Module IS NOT NULL AND TI_Payment_Module != ''
        GROUP BY Supervisor, TI_Payment_Module
        ORDER BY Supervisor, TI_Payment_Module
    ", $params, $types);
}

// Prepare data for the supervisor-HO module table
$supervisorHOModuleData = [];
$uniqueHOModules = [];
$uniqueSupervisorsHO = [];

if ($supervisorHOModuleDistribution) {
    while ($row = $supervisorHOModuleDistribution->fetch_assoc()) {
        $supervisor = $row['Supervisor'] ?: 'N/A';
        $module = $row['Module'];

        if (!in_array($module, $uniqueHOModules)) {
            $uniqueHOModules[] = $module;
        }

        if (!in_array($supervisor, $uniqueSupervisorsHO)) {
            $uniqueSupervisorsHO[] = $supervisor;
        }

        $supervisorHOModuleData[$supervisor][$module] = $row['count'];
    }
}

// Prepare data for the supervisor-TI module table
$supervisorTIModuleData = [];
$uniqueTIModules = [];
$uniqueSupervisorsTI = [];

if ($supervisorTIModuleDistribution) {
    while ($row = $supervisorTIModuleDistribution->fetch_assoc()) {
        $supervisor = $row['Supervisor'] ?: 'N/A';
        $module = $row['Module'];

        if (!in_array($module, $uniqueTIModules)) {
            $uniqueTIModules[] = $module;
        }

        if (!in_array($supervisor, $uniqueSupervisorsTI)) {
            $uniqueSupervisorsTI[] = $supervisor;
        }

        $supervisorTIModuleData[$supervisor][$module] = $row['count'];
    }
}

// Sort modules and supervisors
sort($uniqueHOModules);
sort($uniqueSupervisorsHO);
sort($uniqueTIModules);
sort($uniqueSupervisorsTI);

// Get navigation tabs
function getNavbarTabs() {
    global $conn;

    $stmt = $conn->prepare("SELECT ni.*, rp.page_title, rp.page_name
                           FROM navigation_items ni
                           LEFT JOIN report_pages rp ON ni.url LIKE CONCAT('%page_id=', rp.page_id, '%')
                           WHERE ni.nav_id LIKE 'navbar_tab_%'
                           AND ni.is_visible = 1
                           ORDER BY ni.order_index, ni.created_at");
    $stmt->execute();
    $result = $stmt->get_result();

    $tabs = [];
    while ($row = $result->fetch_assoc()) {
        // Extract page_id from URL
        preg_match('/page_id=([^&]+)/', $row['url'], $matches);
        $row['page_id'] = $matches[1] ?? '';
        $tabs[] = $row;
    }
    $stmt->close();

    return $tabs;
}

$navbarTabs = getNavbarTabs();

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Chart library functionality will be included inline -->
    <script>
        // Chart Library Functions
        const chartLibrary = {
            createChart: function(type, ctx, data, options = {}) {
                const defaultOptions = {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        title: {
                            display: true,
                            text: options.title || 'Chart'
                        }
                    }
                };

                const mergedOptions = { ...defaultOptions, ...options };

                return new Chart(ctx, {
                    type: type,
                    data: data,
                    options: mergedOptions
                });
            },

            getColors: function(scheme, count, alpha = 1) {
                const colorSchemes = {
                    blue: ['#3498db', '#2980b9', '#1abc9c', '#16a085', '#9b59b6'],
                    green: ['#2ecc71', '#27ae60', '#1abc9c', '#16a085', '#3498db'],
                    purple: ['#9b59b6', '#8e44ad', '#e74c3c', '#c0392b', '#f39c12'],
                    orange: ['#f39c12', '#e67e22', '#e74c3c', '#c0392b', '#9b59b6'],
                    red: ['#e74c3c', '#c0392b', '#f39c12', '#e67e22', '#2ecc71']
                };

                const colors = colorSchemes[scheme] || colorSchemes.blue;
                const result = [];

                for (let i = 0; i < count; i++) {
                    const color = colors[i % colors.length];
                    if (alpha < 1) {
                        // Convert hex to rgba
                        const r = parseInt(color.slice(1, 3), 16);
                        const g = parseInt(color.slice(3, 5), 16);
                        const b = parseInt(color.slice(5, 7), 16);
                        result.push(`rgba(${r}, ${g}, ${b}, ${alpha})`);
                    } else {
                        result.push(color);
                    }
                }

                return result;
            }
        };
    </script>
    <link rel="stylesheet" href="/App/assets/css/Database.css">
    <style>
        .checkbox-dropdown {
            max-height: 300px;
            overflow-y: auto;
            padding: 0;
            min-width: 200px;
        }

        .checkbox-dropdown .dropdown-item {
            padding: 8px 16px;
            display: flex;
            align-items: center;
            cursor: pointer;
        }

        .checkbox-dropdown .dropdown-item:hover {
            background-color: #f8f9fa;
        }

        .checkbox-dropdown input[type="checkbox"] {
            margin-right: 8px;
            margin-left: 0;
        }

        .checkbox-dropdown label {
            margin: 0;
            cursor: pointer;
            flex: 1;
        }

        .filter-text {
            text-align: left;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .dropdown-toggle::after {
            float: right;
            margin-top: 8px;
        }

        /* Enhanced styling for the Monday shift distribution table */
        .monday-shift-table {
            background: linear-gradient(135deg, var(--secondary-color) 0%, var(--light-color) 100%);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-lg);
        }

        .monday-shift-table .card-header {
            background: var(--white);
            color: var(--text-dark);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
            padding: 20px;
            font-weight: 600;
        }

        .monday-shift-table .table th {
            background: var(--white);
            color: var(--text-dark);
            font-weight: 600;
            text-align: center;
            vertical-align: middle;
            border: 1px solid var(--border-color);
            padding: 12px 8px;
        }

        .monday-shift-table .table td {
            text-align: center;
            vertical-align: middle;
            border: 1px solid var(--border-color);
            padding: 10px 8px;
        }

        .monday-shift-table .table-total {
            background: linear-gradient(135deg, var(--success-color) 0%, var(--success-light) 100%);
            color: var(--white);
            font-weight: bold;
        }

        .monday-shift-table .table-info td {
            background: linear-gradient(135deg, var(--info-color) 0%, var(--primary-dark) 100%);
            color: var(--white);
            font-weight: bold;
        }

        .monday-shift-table .table tbody tr:nth-child(odd) {
            background-color: rgba(255,255,255,0.8);
        }

        .monday-shift-table .table tbody tr:nth-child(even) {
            background-color: rgba(158, 144, 215, 0.05);
        }

        .monday-shift-table .table tbody tr:hover {
            background-color: rgba(158, 144, 215, 0.1);
            transform: scale(1.01);
            transition: var(--transition);
        }

        /* Badge styling for percentages */
        .monday-shift-table .badge {
            font-size: 0.9em;
            padding: 6px 10px;
            background-color: var(--primary-color) !important;
        }

        /* Medal icons styling */
        .monday-shift-table .fa-medal {
            margin-right: 5px;
        }

        /* Success text for counts */
        .monday-shift-table .text-success {
            color: var(--success-color) !important;
        }

        /* Muted text for empty cells */
        .monday-shift-table .text-muted {
            color: var(--text-light) !important;
        }

        /* Card title enhancement */
        .monday-shift-table .card-header i {
            margin-right: 10px;
            font-size: 1.2em;
        }

        /* Card footer styling */
        .monday-shift-table .card-footer {
            background: var(--white);
            border-top: 1px solid var(--border-color);
            border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
        }

        /* Medal colors using CSS variables */
        .monday-shift-table .medal-gold {
            color: var(--warning-color);
        }

        .monday-shift-table .medal-silver {
            color: var(--gray-color);
        }

        .monday-shift-table .medal-bronze {
            color: #CD7F32;
        }

        /* Enhanced hover effects */
        .monday-shift-table:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(158, 144, 215, 0.2);
        }

        /* Dynamic Filters CSS */
        .dropdown-checkbox {
            position: relative;
            display: inline-block;
            width: 100%;
        }

        .dropdown-toggle-custom {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0.5rem 1rem;
            background-color: #fff;
            border: 1px solid #ced4da;
            border-radius: 0.375rem;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }

        .dropdown-toggle-custom:hover {
            border-color: var(--primary-dark);
            box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
        }

        .dropdown-arrow {
            transition: transform 0.3s ease;
        }

        .dropdown-toggle-custom.active .dropdown-arrow {
            transform: rotate(180deg);
        }

        .dropdown-menu-custom {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            z-index: 1000;
            display: none;
            max-height: 300px;
            overflow-y: auto;
            background-color: #fff;
            border: 1px solid #ced4da;
            border-radius: 0.375rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            margin-top: 0.125rem;
        }

        .dropdown-menu-custom.show {
            display: block;
        }

        .dropdown-item-custom {
            display: flex;
            align-items: center;
            padding: 0.5rem 1rem;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .dropdown-item-custom:hover {
            background-color: rgba(var(--primary-rgb), 0.1);
        }

        .dropdown-item-custom input[type="checkbox"] {
            margin-right: 0.5rem;
        }

        .clear-all-btn {
            padding: 0.5rem 1rem;
            text-align: center;
            background-color: #f8f9fa;
            border-top: 1px solid #dee2e6;
            cursor: pointer;
            font-size: 0.875rem;
            color: #6c757d;
            transition: all 0.3s ease;
        }

        .clear-all-btn:hover {
            background-color: #e9ecef;
            color: #495057;
        }

        /* Enhanced Chart Styling */
        .chart-container {
            position: relative;
            height: 400px;
            margin: 1rem 0;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 10px;
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .chart-wrapper {
            position: relative;
            width: 100%;
            height: 100%;
        }

        .chart-wrapper canvas {
            max-width: 100% !important;
            max-height: 100% !important;
        }

        .chart-container:hover {
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .chart-loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 10;
            display: none;
        }

        .chart-error {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #dc3545;
            z-index: 10;
            display: none;
        }

        .chart-controls {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 5;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .chart-container:hover .chart-controls {
            opacity: 1;
        }

        .chart-control-btn {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 4px 8px;
            margin-left: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s ease;
        }

        .chart-control-btn:hover {
            background: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        /* Responsive chart adjustments */
        @media (max-width: 768px) {
            .chart-container {
                height: 350px;
                margin: 0.5rem 0;
            }

            .chart-controls {
                position: relative;
                top: auto;
                right: auto;
                opacity: 1;
                margin-bottom: 10px;
                text-align: center;
            }
        }

        @media (max-width: 576px) {
            .chart-container {
                height: 300px;
            }
        }

        /* Navbar tabs styling */
        .navbar-tabs .nav-link {
            background: rgba(255, 255, 255, 0.1);
            color: white !important;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 20px;
            padding: 8px 16px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .navbar-tabs .nav-link:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .navbar-tabs .nav-link:active {
            transform: translateY(0);
        }

        .navbar-tabs .nav-item {
            margin-bottom: 0;
        }

        /* Dropdown styling */
        .navbar-tabs .dropdown-menu {
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(10px);
            margin-top: 8px;
        }

        .navbar-tabs .dropdown-item {
            color: #333;
            padding: 10px 16px;
            border-radius: 6px;
            margin: 2px 4px;
            transition: all 0.3s ease;
        }

        .navbar-tabs .dropdown-item:hover {
            background: rgba(158, 144, 215, 0.1);
            color: #6f42c1;
            transform: translateX(5px);
        }

        .navbar-tabs .dropdown-item i {
            width: 20px;
            text-align: center;
        }

        /* Responsive navbar tabs */
        @media (max-width: 768px) {
            .navbar-tabs .nav-link {
                font-size: 0.8rem;
                padding: 6px 12px;
            }

            .navbar-tabs .dropdown-item {
                padding: 8px 12px;
                font-size: 0.9rem;
            }
        }

        @media (max-width: 576px) {
            .navbar-tabs {
                display: none; /* Hide on very small screens */
            }
        }
    </style>
</head>
<body class="database-analysis">
    <header class="dashboard-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="d-flex align-items-center logo-text">
                        <img src="/Logos/kalam.png" alt="Logo" class="img-fluid me-3" style="max-height: 45px;">
                        <h1 class="mb-0">Database Dashboard</h1>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="d-flex justify-content-end align-items-center">
                        <?php if (!empty($navbarTabs)): ?>
                            <nav class="navbar-tabs">
                                <ul class="nav nav-pills">
                                    <?php
                                    $maxVisibleTabs = 5;
                                    $visibleTabs = array_slice($navbarTabs, 0, $maxVisibleTabs);
                                    $dropdownTabs = array_slice($navbarTabs, $maxVisibleTabs);
                                    ?>

                                    <!-- Display first 5 tabs normally -->
                                    <?php foreach ($visibleTabs as $tab): ?>
                                        <li class="nav-item me-2">
                                            <a class="nav-link text-white border border-white-50"
                                               href="<?= htmlspecialchars($tab['url']) ?>"
                                               target="_blank"
                                               title="<?= htmlspecialchars($tab['description'] ?? '') ?>">
                                                <i class="<?= htmlspecialchars($tab['icon']) ?> me-1"></i>
                                                <?= htmlspecialchars($tab['title']) ?>
                                            </a>
                                        </li>
                                    <?php endforeach; ?>

                                    <!-- Dropdown for remaining tabs if more than 5 -->
                                    <?php if (!empty($dropdownTabs)): ?>
                                        <li class="nav-item dropdown me-2">
                                            <a class="nav-link dropdown-toggle text-white border border-white-50"
                                               href="#"
                                               id="moreTabsDropdown"
                                               role="button"
                                               data-bs-toggle="dropdown"
                                               aria-expanded="false"
                                               title="More tabs">
                                                <i class="fas fa-ellipsis-h me-1"></i>
                                                More
                                            </a>
                                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="moreTabsDropdown">
                                                <?php foreach ($dropdownTabs as $tab): ?>
                                                    <li>
                                                        <a class="dropdown-item"
                                                           href="<?= htmlspecialchars($tab['url']) ?>"
                                                           target="_blank"
                                                           title="<?= htmlspecialchars($tab['description'] ?? '') ?>">
                                                            <i class="<?= htmlspecialchars($tab['icon']) ?> me-2"></i>
                                                            <?= htmlspecialchars($tab['title']) ?>
                                                        </a>
                                                    </li>
                                                <?php endforeach; ?>
                                            </ul>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </header>



    <div class="container">
        <!-- Data Filters -->
        <div class="filter-container">
            <form method="get" id="dashboardFilters">
                <div class="row">
                    <div class="col-md-2">
                        <div class="filter-group">
                            <label class="form-label">Leader</label>
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary dropdown-toggle w-100" type="button" data-bs-toggle="dropdown">
                                    <span class="filter-text">Leaders</span>
                                </button>
                                <div class="dropdown-menu checkbox-dropdown">
                                    <?php foreach ($leaders as $index => $leader): ?>
                                    <div class="dropdown-item">
                                        <input type="checkbox" id="leader_<?= $index ?>" name="leader[]"
                                            value="<?= htmlspecialchars($leader['Leader']) ?>"
                                            <?= isFilterActive($filters['leader'], $leader['Leader']) ? 'checked' : '' ?>>
                                        <label for="leader_<?= $index ?>"><?= htmlspecialchars($leader['Leader']) ?></label>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2">
                        <div class="filter-group">
                            <label class="form-label">HO Module</label>
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary dropdown-toggle w-100" type="button" data-bs-toggle="dropdown">
                                    <span class="filter-text">HO Modules</span>
                                </button>
                                <div class="dropdown-menu checkbox-dropdown">
                                    <?php foreach ($ho_modules as $index => $module): ?>
                                    <div class="dropdown-item">
                                        <input type="checkbox" id="ho_module_<?= $index ?>" name="ho_module[]"
                                            value="<?= htmlspecialchars($module['HO_Payment_Module']) ?>"
                                            <?= isFilterActive($filters['ho_module'], $module['HO_Payment_Module']) ? 'checked' : '' ?>>
                                        <label for="ho_module_<?= $index ?>"><?= htmlspecialchars($module['HO_Payment_Module']) ?></label>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2">
                        <div class="filter-group">
                            <label class="form-label">TI Module</label>
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary dropdown-toggle w-100" type="button" data-bs-toggle="dropdown">
                                    <span class="filter-text">TI Modules</span>
                                </button>
                                <div class="dropdown-menu checkbox-dropdown">
                                    <?php foreach ($ti_modules as $index => $module): ?>
                                    <div class="dropdown-item">
                                        <input type="checkbox" id="ti_module_<?= $index ?>" name="ti_module[]"
                                            value="<?= htmlspecialchars($module['TI_Payment_Module']) ?>"
                                            <?= isFilterActive($filters['ti_module'], $module['TI_Payment_Module']) ? 'checked' : '' ?>>
                                        <label for="ti_module_<?= $index ?>"><?= htmlspecialchars($module['TI_Payment_Module']) ?></label>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2">
                        <div class="filter-group">
                            <label class="form-label">Status</label>
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary dropdown-toggle w-100" type="button" data-bs-toggle="dropdown">
                                    <span class="filter-text">Statuses</span>
                                </button>
                                <div class="dropdown-menu checkbox-dropdown">
                                    <?php foreach ($statuses as $index => $status): ?>
                                    <div class="dropdown-item">
                                        <input type="checkbox" id="status_<?= $index ?>" name="status[]"
                                            value="<?= htmlspecialchars($status['Status']) ?>"
                                            <?= isFilterActive($filters['status'], $status['Status']) ? 'checked' : '' ?>>
                                        <label for="status_<?= $index ?>"><?= htmlspecialchars($status['Status']) ?></label>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2">
                        <div class="filter-group">
                            <label class="form-label">Language</label>
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary dropdown-toggle w-100" type="button" data-bs-toggle="dropdown">
                                    <span class="filter-text">Languages</span>
                                </button>
                                <div class="dropdown-menu checkbox-dropdown">
                                    <?php foreach ($seatLangs as $index => $lang): ?>
                                    <div class="dropdown-item">
                                        <input type="checkbox" id="seatlang_<?= $index ?>" name="seatlang[]"
                                            value="<?= htmlspecialchars($lang['Language']) ?>"
                                            <?= isFilterActive($filters['seatlang'], $lang['Language']) ? 'checked' : '' ?>>
                                        <label for="seatlang_<?= $index ?>"><?= htmlspecialchars($lang['Language']) ?></label>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>



                    <!-- Additional Dynamic Filters -->
                    <?= generateAdditionalFilters('DatabaseAnalysis') ?>

                    <div class="col-12 mt-3">
                        <div class="row g-2">
                            <div class="col-md-8">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-filter"></i> Apply Filters
                                </button>
                            </div>
                            <div class="col-md-4">
                                <a href="<?= strtok($_SERVER['REQUEST_URI'], '?') ?>" class="btn btn-outline-secondary w-100" title="Clear all filters">
                                    <i class="fas fa-times"></i> Reset Filters
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>



        <!-- Statistics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="stat-value"><?= $leaderCount ? $leaderCount->num_rows : 0 ?></div>
                    <div class="stat-label">Supervisors</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="stat-value"><?= $hoModuleCount ? $hoModuleCount->num_rows : 0 ?></div>
                    <div class="stat-label">HO Modules</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="stat-value"><?= $tiModuleCount ? $tiModuleCount->num_rows : 0 ?></div>
                    <div class="stat-label">TI Modules</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="stat-value"><?= $languageCount ? $languageCount->num_rows : 0 ?></div>
                    <div class="stat-label">Languages</div>
                </div>
            </div>
        </div>



        <!-- Tables and Charts -->
        <div class="row">
            <!-- Interpreter Count by Supervisor table -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-users me-2"></i> Interpreter Count by Supervisor
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Supervisor</th>
                                        <th>Count</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if ($leaderCount): ?>
                                        <?php while ($row = $leaderCount->fetch_assoc()): ?>
                                        <tr>
                                            <td><?= htmlspecialchars($row['Supervisor'] ?: 'N/A') ?></td>
                                            <td><?= htmlspecialchars($row['count']) ?></td>
                                        </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr><td colspan="2">No data available (Supervisor column not found)</td></tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- HO Module Distribution chart -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-chart-pie me-2"></i> HO Module Distribution
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <div class="chart-loading">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                            <div class="chart-error">
                                <i class="fas fa-exclamation-triangle"></i>
                                <p>Error loading chart</p>
                            </div>
                            <div class="chart-controls">
                                <button class="chart-control-btn" onclick="exportChart('hoModuleChart', 'HO_Module_Distribution')" title="Export Chart">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button class="chart-control-btn" onclick="refreshChart('hoModuleChart')" title="Refresh Chart">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                                <button class="chart-control-btn" onclick="toggleChartType('hoModuleChart')" title="Change Chart Type">
                                    <i class="fas fa-chart-bar"></i>
                                </button>
                            </div>
                            <div class="chart-wrapper">
                                <canvas id="hoModuleChart" style="max-height: 400px;"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Second Row for TI Module Chart -->
        <div class="row mt-4">
            <!-- TI Module Distribution chart -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-chart-pie me-2"></i> TI Module Distribution
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <div class="chart-loading">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                            <div class="chart-error">
                                <i class="fas fa-exclamation-triangle"></i>
                                <p>Error loading chart</p>
                            </div>
                            <div class="chart-controls">
                                <button class="chart-control-btn" onclick="exportChart('tiModuleChart', 'TI_Module_Distribution')" title="Export Chart">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button class="chart-control-btn" onclick="refreshChart('tiModuleChart')" title="Refresh Chart">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                                <button class="chart-control-btn" onclick="toggleChartType('tiModuleChart')" title="Change Chart Type">
                                    <i class="fas fa-chart-bar"></i>
                                </button>
                            </div>
                            <div class="chart-wrapper">
                                <canvas id="tiModuleChart" style="max-height: 400px;"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Supervisor-HO Module Distribution Table -->
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-table me-2"></i> Interpreter Distribution by Supervisor and HO Module
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped distribution-table">
                                <thead>
                                    <tr>
                                        <th>Supervisor \ HO Module</th>
                                        <?php foreach ($uniqueHOModules as $module): ?>
                                            <th><?= htmlspecialchars($module) ?></th>
                                        <?php endforeach; ?>
                                        <th>Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($uniqueSupervisorsHO as $supervisor): ?>
                                        <tr>
                                            <td><strong><?= htmlspecialchars($supervisor) ?></strong></td>
                                            <?php
                                            $supervisorTotal = 0;
                                            foreach ($uniqueHOModules as $module):
                                                $count = $supervisorHOModuleData[$supervisor][$module] ?? 0;
                                                $supervisorTotal += $count;
                                            ?>
                                                <td><?= $count > 0 ? $count : '-' ?></td>
                                            <?php endforeach; ?>
                                            <td class="table-total"><?= $supervisorTotal ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                    <tr class="table-info">
                                        <td><strong>Total</strong></td>
                                        <?php
                                        $grandTotalHO = 0;
                                        foreach ($uniqueHOModules as $module):
                                            $moduleTotal = 0;
                                            foreach ($uniqueSupervisorsHO as $supervisor) {
                                                $moduleTotal += $supervisorHOModuleData[$supervisor][$module] ?? 0;
                                            }
                                            $grandTotalHO += $moduleTotal;
                                        ?>
                                            <td class="table-total"><?= $moduleTotal ?></td>
                                        <?php endforeach; ?>
                                        <td class="table-total"><?= $grandTotalHO ?></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Supervisor-TI Module Distribution Table -->
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-table me-2"></i> Interpreter Distribution by Supervisor and TI Module
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped distribution-table">
                                <thead>
                                    <tr>
                                        <th>Supervisor \ TI Module</th>
                                        <?php foreach ($uniqueTIModules as $module): ?>
                                            <th><?= htmlspecialchars($module) ?></th>
                                        <?php endforeach; ?>
                                        <th>Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($uniqueSupervisorsTI as $supervisor): ?>
                                        <tr>
                                            <td><strong><?= htmlspecialchars($supervisor) ?></strong></td>
                                            <?php
                                            $supervisorTotal = 0;
                                            foreach ($uniqueTIModules as $module):
                                                $count = $supervisorTIModuleData[$supervisor][$module] ?? 0;
                                                $supervisorTotal += $count;
                                            ?>
                                                <td><?= $count > 0 ? $count : '-' ?></td>
                                            <?php endforeach; ?>
                                            <td class="table-total"><?= $supervisorTotal ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                    <tr class="table-info">
                                        <td><strong>Total</strong></td>
                                        <?php
                                        $grandTotalTI = 0;
                                        foreach ($uniqueTIModules as $module):
                                            $moduleTotal = 0;
                                            foreach ($uniqueSupervisorsTI as $supervisor) {
                                                $moduleTotal += $supervisorTIModuleData[$supervisor][$module] ?? 0;
                                            }
                                            $grandTotalTI += $moduleTotal;
                                        ?>
                                            <td class="table-total"><?= $moduleTotal ?></td>
                                        <?php endforeach; ?>
                                        <td class="table-total"><?= $grandTotalTI ?></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Third Row -->
        <div class="row mt-4">
            <!-- Active Headcount by HO Module table -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-user-check me-2"></i> Active Headcount by HO Module
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>HO Module</th>
                                        <th>Count</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if ($activeHOCount): ?>
                                        <?php while ($row = $activeHOCount->fetch_assoc()): ?>
                                        <tr>
                                            <td><?= htmlspecialchars($row['HO_Payment_Module']) ?></td>
                                            <td><?= htmlspecialchars($row['count']) ?></td>
                                        </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr><td colspan="2">No data available (HO_Payment_Module column not found)</td></tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Active Headcount by TI Module table -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-user-check me-2"></i> Active Headcount by TI Module
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>TI Module</th>
                                        <th>Count</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if ($activeTICount): ?>
                                        <?php while ($row = $activeTICount->fetch_assoc()): ?>
                                        <tr>
                                            <td><?= htmlspecialchars($row['TI_Payment_Module']) ?></td>
                                            <td><?= htmlspecialchars($row['count']) ?></td>
                                        </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr><td colspan="2">No data available (TI_Payment_Module column not found)</td></tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Fourth Row -->
        <div class="row mt-4">

            <!-- Status Distribution chart -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-chart-bar me-2"></i> Status Distribution
                    </div>
                    <div class="card-body">
                        <div class="chart-container" style="position: relative; height: 400px;">
                            <div class="chart-loading">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                            <div class="chart-error">
                                <i class="fas fa-exclamation-triangle"></i>
                                <p>Error loading chart</p>
                            </div>
                            <div class="chart-controls">
                                <button class="chart-control-btn" onclick="exportChart('statusChart', 'Status_Distribution')" title="Export Chart">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button class="chart-control-btn" onclick="refreshChart('statusChart')" title="Refresh Chart">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                                <button class="chart-control-btn" onclick="toggleChartType('statusChart')" title="Change Chart Type">
                                    <i class="fas fa-chart-line"></i>
                                </button>
                            </div>
                            <div class="chart-wrapper" style="position: relative; height: 350px; width: 100%;">
                                <canvas id="statusChart" style="width: 100% !important; height: 100% !important;"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <!-- Third Row -->
        <div class="row mt-4">
            <!-- Top 10 Sub Reasons table -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-exclamation-circle me-2"></i> Top 10 Sub Reasons
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Sub Reason</th>
                                        <th>Count</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if ($subReasons): ?>
                                        <?php $rank = 1; while ($row = $subReasons->fetch_assoc()): ?>
                                        <tr>
                                            <td><?= $rank++ ?></td>
                                            <td><?= htmlspecialchars($row['SubReason'] ?: 'N/A') ?></td>
                                            <td><?= htmlspecialchars($row['count']) ?></td>
                                        </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr><td colspan="3">No data available (SubReason column not found)</td></tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Shift Type Distribution table -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-clock me-2"></i> Shift Type Distribution
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Shift Type</th>
                                        <th>Count</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if ($shiftTypes): ?>
                                        <?php while ($row = $shiftTypes->fetch_assoc()): ?>
                                        <tr>
                                            <td><?= htmlspecialchars($row['ShiftType'] ?: 'N/A') ?></td>
                                            <td><?= htmlspecialchars($row['count']) ?></td>
                                        </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr><td colspan="2">No data available (ShiftType column not found)</td></tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>



        <!-- Fourth Row - Top 15 Languages Distribution -->
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-language me-2"></i> Top 15 Languages Distribution
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped distribution-table">
                                <thead>
                                    <tr>
                                        <th><i class="fas fa-hashtag"></i> Rank</th>
                                        <th><i class="fas fa-language"></i> Language</th>
                                        <th><i class="fas fa-calculator"></i> Count</th>
                                        <th><i class="fas fa-percentage"></i> Percentage</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $rank = 1;
                                    $grandTotalLanguages = array_sum($languageTotals);

                                    foreach ($top15Languages as $language):
                                        $languageTotal = $languageTotals[$language];
                                        $percentage = $grandTotalLanguages > 0 ? round(($languageTotal / $grandTotalLanguages) * 100, 2) : 0;

                                        // Add medal icons for top 3
                                        $rankDisplay = $rank;
                                        if ($rank == 1) $rankDisplay = '<i class="fas fa-medal medal-gold"></i> 1';
                                        elseif ($rank == 2) $rankDisplay = '<i class="fas fa-medal medal-silver"></i> 2';
                                        elseif ($rank == 3) $rankDisplay = '<i class="fas fa-medal medal-bronze"></i> 3';
                                    ?>
                                    <tr>
                                        <td><?= $rankDisplay ?></td>
                                        <td><strong><i class="fas fa-flag"></i> <?= htmlspecialchars($language) ?></strong></td>
                                        <td class="table-total"><?= number_format($languageTotal) ?></td>
                                        <td><span class="badge"><?= $percentage ?>%</span></td>
                                    </tr>
                                    <?php $rank++; endforeach; ?>
                                    <tr class="table-info">
                                        <td colspan="2"><strong><i class="fas fa-calculator"></i> Grand Total</strong></td>
                                        <td class="table-total"><?= number_format($grandTotalLanguages) ?></td>
                                        <td><strong><i class="fas fa-percentage"></i> 100%</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="card-footer">
                            <div class="row">
                                <div class="col-md-6">

                                </div>
                                <div class="col-md-6 text-end">
                                    <small class="text-muted">
                                        <i class="fas fa-users"></i>
                                        Total Records: <?= number_format($grandTotalLanguages) ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Headcount Per Language section at the bottom -->
    <div class="full-width-section">
        <div class="container">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-language me-2"></i> Headcount Per Language by HO Module
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped distribution-table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Language</th>
                                    <?php foreach ($uniqueHOModulesForLanguage as $module): ?>
                                        <th><?= htmlspecialchars($module) ?></th>
                                    <?php endforeach; ?>
                                    <th>Total</th>
                                    <th>Percentage</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $rank = 1;
                                $grandTotalLanguageHO = 0;

                                // Calculate grand total first and prepare data for sorting
                                $languageTotalsHO = [];
                                foreach ($uniqueLanguagesHO as $language) {
                                    $languageTotal = 0;
                                    foreach ($uniqueHOModulesForLanguage as $module) {
                                        $count = $languageHOModuleData[$language][$module] ?? 0;
                                        $languageTotal += $count;
                                        $grandTotalLanguageHO += $count;
                                    }
                                    if ($languageTotal > 0) {
                                        $languageTotalsHO[$language] = $languageTotal;
                                    }
                                }

                                // Sort languages by total count (descending)
                                arsort($languageTotalsHO);

                                // Display data with module breakdown (sorted by total)
                                foreach ($languageTotalsHO as $language => $languageTotal):
                                    $percentage = $grandTotalLanguageHO > 0 ? round(($languageTotal / $grandTotalLanguageHO) * 100, 2) : 0;
                                ?>
                                <tr>
                                    <td><?= $rank++ ?></td>
                                    <td><strong><?= htmlspecialchars($language) ?></strong></td>
                                    <?php foreach ($uniqueHOModulesForLanguage as $module):
                                        $count = $languageHOModuleData[$language][$module] ?? 0;
                                    ?>
                                        <td><?= $count > 0 ? $count : '-' ?></td>
                                    <?php endforeach; ?>
                                    <td class="table-total"><?= $languageTotal ?></td>
                                    <td><?= $percentage ?>%</td>
                                </tr>
                                <?php endforeach; ?>
                                <tr class="table-info">
                                    <td colspan="2"><strong>Total</strong></td>
                                    <?php
                                    foreach ($uniqueHOModulesForLanguage as $module):
                                        $moduleTotal = 0;
                                        foreach ($uniqueLanguagesHO as $language) {
                                            $moduleTotal += $languageHOModuleData[$language][$module] ?? 0;
                                        }
                                    ?>
                                        <td class="table-total"><?= $moduleTotal ?></td>
                                    <?php endforeach; ?>
                                    <td class="table-total"><?= $grandTotalLanguageHO ?></td>
                                    <td><strong>100%</strong></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script>
        $(document).ready(function() {
            // Prevent dropdown from closing when clicking on checkboxes
            $('.checkbox-dropdown').on('click', function(e) {
                e.stopPropagation();
            });

            // Handle checkbox behavior
            $('input[type="checkbox"]').change(function() {
                const dropdown = $(this).closest('.checkbox-dropdown');
                const filterName = $(this).attr('name').replace('[]', '');
                updateFilterText(dropdown, filterName);
            });

            // Update filter button text
            function updateFilterText(dropdown, filterName) {
                const button = dropdown.closest('.filter-group').find('.dropdown-toggle .filter-text');
                const checkedItems = dropdown.find('input[type="checkbox"]:checked');

                if (checkedItems.length === 0) {
                    button.text(getDefaultText(filterName));
                } else if (checkedItems.length === 1) {
                    button.text(checkedItems.first().next('label').text());
                } else {
                    button.text(checkedItems.length + ' selected');
                }
            }

            function getDefaultText(filterName) {
                const defaults = {
                    'leader': 'Supervisors',
                    'ho_module': 'HO Modules',
                    'ti_module': 'TI Modules',
                    'status': 'Statuses',
                    'seatlang': 'Languages'
                };
                return defaults[filterName] || filterName;
            }

            // Initialize filter text on page load
            $('.checkbox-dropdown').each(function() {
                const filterName = $(this).find('input[type="checkbox"]').first().attr('name').replace('[]', '');
                updateFilterText($(this), filterName);
            });


            // Enhanced chart creation with error handling and loading states
            createEnhancedChart('hoModuleChart', 'pie', {
                labels: [
                    <?php
                    if ($hoModuleCount) {
                        $hoModuleCount->data_seek(0);
                        while ($row = $hoModuleCount->fetch_assoc()) {
                            echo "'" . addslashes($row['HO_Payment_Module']) . "',";
                        }
                    }
                    ?>
                ],
                values: [
                    <?php
                    if ($hoModuleCount) {
                        $hoModuleCount->data_seek(0);
                        while ($row = $hoModuleCount->fetch_assoc()) {
                            echo $row['count'] . ",";
                        }
                    }
                    ?>
                ]
            }, {
                title: 'HO Module Distribution',
                colorScheme: 'purple',
                legendPosition: 'right'
            });

            createEnhancedChart('tiModuleChart', 'pie', {
                labels: [
                    <?php
                    if ($tiModuleCount) {
                        $tiModuleCount->data_seek(0);
                        while ($row = $tiModuleCount->fetch_assoc()) {
                            echo "'" . addslashes($row['TI_Payment_Module']) . "',";
                        }
                    }
                    ?>
                ],
                values: [
                    <?php
                    if ($tiModuleCount) {
                        $tiModuleCount->data_seek(0);
                        while ($row = $tiModuleCount->fetch_assoc()) {
                            echo $row['count'] . ",";
                        }
                    }
                    ?>
                ]
            }, {
                title: 'TI Module Distribution',
                colorScheme: 'blue',
                legendPosition: 'right'
            });

            createEnhancedChart('statusChart', 'bar', {
                labels: [
                    <?php
                    if ($statusCount) {
                        $statusCount->data_seek(0);
                        while ($row = $statusCount->fetch_assoc()) {
                            echo "'" . addslashes($row['Status']) . "',";
                        }
                    }
                    ?>
                ],
                values: [
                    <?php
                    if ($statusCount) {
                        $statusCount->data_seek(0);
                        while ($row = $statusCount->fetch_assoc()) {
                            echo $row['count'] . ",";
                        }
                    }
                    ?>
                ]
            }, {
                title: 'Status Distribution',
                label: 'Count',
                colorScheme: 'purple',
                responsive: true,
                maintainAspectRatio: false
            });



            // Remove specific filter when clicking the remove icon
            $('.applied-filters a').click(function(e) {
                e.preventDefault();
                const filterName = $(this).closest('.badge').text().split(':')[0].trim().toLowerCase();
                window.location.href = window.location.pathname + '?' + $.param({
                    ...<?= json_encode($filters) ?>,
                    [filterName]: 'all'
                });
            });
        });

        // Enhanced Chart Creation Function
        function createEnhancedChart(chartId, type, data, options = {}) {
            console.log('Creating chart:', chartId, type, data, options);

            const canvas = document.getElementById(chartId);
            if (!canvas) {
                console.error('Canvas not found:', chartId);
                return;
            }

            try {
                // Convert data format to Chart.js format
                const chartData = {
                    labels: data.labels || [],
                    datasets: [{
                        label: options.label || 'Data',
                        data: data.values || [],
                        backgroundColor: chartLibrary.getColors(options.colorScheme || 'blue', data.labels?.length || 1, 0.8),
                        borderColor: chartLibrary.getColors(options.colorScheme || 'blue', data.labels?.length || 1, 1),
                        borderWidth: 2
                    }]
                };

                const chartOptions = {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: options.title || 'Chart',
                            font: {
                                size: 16,
                                weight: 'bold'
                            }
                        },
                        legend: {
                            position: options.legendPosition || 'top'
                        }
                    },
                    layout: {
                        padding: {
                            top: 10,
                            bottom: 10,
                            left: 10,
                            right: 10
                        }
                    }
                };

                // Add specific options for different chart types
                if (type === 'pie' || type === 'doughnut') {
                    chartOptions.plugins.legend.position = 'right';
                } else if (type === 'bar') {
                    chartOptions.scales = {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                font: {
                                    size: 12
                                }
                            }
                        },
                        x: {
                            ticks: {
                                font: {
                                    size: 12
                                },
                                maxRotation: 45,
                                minRotation: 0
                            }
                        }
                    };
                }

                // Set canvas size explicitly
                const container = canvas.closest('.chart-wrapper');
                if (container) {
                    canvas.width = container.offsetWidth;
                    canvas.height = container.offsetHeight;
                }

                const ctx = canvas.getContext('2d');
                const chart = chartLibrary.createChart(type, ctx, chartData, chartOptions);

                // Store chart instance for management
                window.chartInstances = window.chartInstances || {};
                window.chartInstances[chartId] = chart;

                // Resize chart when window resizes
                const resizeObserver = new ResizeObserver(entries => {
                    if (chart) {
                        chart.resize();
                    }
                });
                resizeObserver.observe(container || canvas);

                console.log('Chart created successfully:', chartId);

            } catch (error) {
                console.error('Error creating chart:', chartId, error);
                showChartError(chartId, error.message);
            }
        }

        function showChartError(chartId, message) {
            const container = document.getElementById(chartId).closest('.chart-container');
            const loadingElement = container.querySelector('.chart-loading');
            const errorElement = container.querySelector('.chart-error');
            const canvas = document.getElementById(chartId);

            loadingElement.style.display = 'none';
            errorElement.style.display = 'block';
            canvas.style.opacity = '0.3';

            errorElement.querySelector('p').textContent = message || 'Error loading chart';
        }

        // Chart Management Functions
        function addNewChart() {
            // Open chart creation modal or redirect to dashboard
            window.open('database_dashboard.php#chart-manager', '_blank');
        }

        function exportAllCharts() {
            window.open('chart_export.php', '_blank');
        }

        function refreshCharts() {
            location.reload();
        }

        function toggleChartSettings() {
            const panel = document.getElementById('chartSettingsPanel');
            panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
        }

        function updateAllChartColors() {
            const colorScheme = document.getElementById('globalColorScheme').value;

            // Update all chart instances with new color scheme
            Object.keys(window.chartInstances || {}).forEach(chartKey => {
                const chart = window.chartInstances[chartKey];
                if (chart) {
                    const newColors = chartLibrary.getColors(colorScheme, chart.data.labels.length, 0.8);
                    chart.data.datasets[0].backgroundColor = newColors;
                    chart.data.datasets[0].borderColor = chartLibrary.getColors(colorScheme, chart.data.labels.length, 1);
                    chart.update();
                }
            });
        }

        function updateChartAnimation() {
            const duration = parseInt(document.getElementById('chartAnimation').value);

            Object.keys(window.chartInstances || {}).forEach(chartKey => {
                const chart = window.chartInstances[chartKey];
                if (chart) {
                    chart.options.animation.duration = duration;
                    chart.update();
                }
            });
        }

        function toggleLegends() {
            const showLegends = document.getElementById('showLegends').checked;

            Object.keys(window.chartInstances || {}).forEach(chartKey => {
                const chart = window.chartInstances[chartKey];
                if (chart) {
                    chart.options.plugins.legend.display = showLegends;
                    chart.update();
                }
            });
        }

        function toggleTooltips() {
            const showTooltips = document.getElementById('showTooltips').checked;

            Object.keys(window.chartInstances || {}).forEach(chartKey => {
                const chart = window.chartInstances[chartKey];
                if (chart) {
                    chart.options.plugins.tooltip.enabled = showTooltips;
                    chart.update();
                }
            });
        }

        // Individual Chart Control Functions
        function exportChart(chartId, filename) {
            const chart = window.chartInstances[chartId];
            if (chart) {
                const url = chart.toBase64Image('image/png', 1.0);
                const link = document.createElement('a');
                link.download = filename + '_' + new Date().toISOString().split('T')[0] + '.png';
                link.href = url;
                link.click();
            }
        }

        function refreshChart(chartId) {
            const chart = window.chartInstances[chartId];
            if (chart) {
                chart.update('active');
            }
        }

        function toggleChartType(chartId) {
            const chart = window.chartInstances[chartId];
            if (!chart) return;

            const currentType = chart.config.type;
            let newType;

            // Cycle through chart types
            switch (currentType) {
                case 'pie':
                    newType = 'doughnut';
                    break;
                case 'doughnut':
                    newType = 'bar';
                    break;
                case 'bar':
                    newType = 'line';
                    break;
                case 'line':
                    newType = 'pie';
                    break;
                default:
                    newType = 'bar';
            }

            // Update chart type
            chart.config.type = newType;
            chart.update();
        }

        // Performance optimization: Debounce resize events
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // Optimize chart updates on window resize
        window.addEventListener('resize', debounce(() => {
            Object.keys(window.chartInstances || {}).forEach(chartKey => {
                const chart = window.chartInstances[chartKey];
                if (chart) {
                    chart.resize();
                }
            });
        }, 250));
    </script>

    <!-- Dynamic Filters JavaScript -->
    <?= generateAnalysisFilterJavaScript() ?>

    <!-- Navigation JavaScript -->
    <script>
        // Function to show specific sections
        function showSection(sectionType) {
            // Hide all sections first
            const sections = ['charts', 'tables', 'statistics'];
            sections.forEach(section => {
                const elements = document.querySelectorAll(`[data-section="${section}"]`);
                elements.forEach(el => {
                    if (section === sectionType) {
                        el.style.display = 'block';
                        el.classList.add('animate__animated', 'animate__fadeIn');
                    } else {
                        el.style.display = 'none';
                    }
                });
            });

            // If no specific section elements exist, scroll to relevant content
            if (sectionType === 'charts') {
                const chartElements = document.querySelectorAll('.chart-container');
                if (chartElements.length > 0) {
                    chartElements[0].scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
            } else if (sectionType === 'tables') {
                const tableElements = document.querySelectorAll('.table-responsive');
                if (tableElements.length > 0) {
                    tableElements[0].scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
            } else if (sectionType === 'statistics') {
                const statsElements = document.querySelectorAll('.stat-card');
                if (statsElements.length > 0) {
                    statsElements[0].scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
            }

            // Update active nav item
            document.querySelectorAll('#reportsDropdown + .dropdown-menu .dropdown-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');
        }

        // Function to export all data
        function exportAllData() {
            // Show loading indicator
            const loadingToast = document.createElement('div');
            loadingToast.className = 'toast align-items-center text-white bg-primary border-0';
            loadingToast.style.position = 'fixed';
            loadingToast.style.top = '20px';
            loadingToast.style.right = '20px';
            loadingToast.style.zIndex = '9999';
            loadingToast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="fas fa-spinner fa-spin me-2"></i>Preparing export...
                    </div>
                </div>
            `;
            document.body.appendChild(loadingToast);

            // Simulate export process (replace with actual export logic)
            setTimeout(() => {
                loadingToast.remove();

                // Success toast
                const successToast = document.createElement('div');
                successToast.className = 'toast align-items-center text-white bg-success border-0';
                successToast.style.position = 'fixed';
                successToast.style.top = '20px';
                successToast.style.right = '20px';
                successToast.style.zIndex = '9999';
                successToast.innerHTML = `
                    <div class="d-flex">
                        <div class="toast-body">
                            <i class="fas fa-check me-2"></i>Export completed successfully!
                        </div>
                    </div>
                `;
                document.body.appendChild(successToast);

                setTimeout(() => successToast.remove(), 3000);
            }, 2000);
        }

        // Function to show user profile
        function showUserProfile() {
            alert('User Profile functionality will be implemented in the next update.');
        }

        // Function to show settings
        function showSettings() {
            alert('Settings functionality will be implemented in the next update.');
        }

        // Add smooth scrolling to navbar links
        document.addEventListener('DOMContentLoaded', function() {
            // Add active class to current page
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.navbar-nav .nav-link');

            navLinks.forEach(link => {
                if (link.getAttribute('href') && currentPath.includes(link.getAttribute('href'))) {
                    link.classList.add('active');
                }
            });

            // Add hover effects to dropdown items
            const dropdownItems = document.querySelectorAll('.dropdown-item');
            dropdownItems.forEach(item => {
                item.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateX(5px)';
                    this.style.transition = 'transform 0.2s ease';
                });

                item.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateX(0)';
                });
            });
        });
    </script>
</body>
</html>
