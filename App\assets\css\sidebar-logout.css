/* Sidebar Logout Modal Styles */
.logout-modal {
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border: none;
    overflow: hidden;
}

.logout-modal .modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom: none;
    padding: 20px 25px;
}

.logout-modal .modal-title {
    font-weight: 600;
    font-size: 1.2rem;
}

.logout-modal .btn-close {
    filter: brightness(0) invert(1);
    opacity: 0.8;
}

.logout-modal .btn-close:hover {
    opacity: 1;
}

.logout-modal .modal-body {
    padding: 30px 25px;
    text-align: center;
    background: #f8f9fa;
}

.logout-modal .logout-icon {
    margin-bottom: 20px;
}

.logout-modal .logout-icon i {
    font-size: 3rem;
    color: #ffc107;
    animation: pulse 2s infinite;
}

.logout-modal .modal-body h6 {
    color: #333;
    font-weight: 600;
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.logout-modal .modal-body p {
    color: #666;
    margin-bottom: 0;
    font-size: 0.95rem;
}

.logout-modal .modal-footer {
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    padding: 20px 25px;
    justify-content: space-between;
}

.logout-modal .cancel-btn {
    background: #6c757d;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.logout-modal .cancel-btn:hover {
    background: #5a6268;
    color: white;
    transform: translateY(-1px);
}

.logout-modal .logout-btn {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--text-white);
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 500;
    transition: var(--transition-normal);
}

.logout-modal .logout-btn:hover {
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-darker) 100%);
    color: var(--text-white);
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(200, 156, 196, 0.4);
}

/* Animation for warning icon */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

/* Loading modal styles */
.modal.show {
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Sidebar footer button styles - unified with collapse button */
.sidebar-footer .logout-btn {
    transition: var(--transition-normal);
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: var(--white) !important;
}

.sidebar-footer .logout-btn:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 255, 255, 0.1);
}

.sidebar-footer .logout-btn:active {
    transform: translateY(0);
}

/* Responsive adjustments */
@media (max-width: 576px) {
    .logout-modal .modal-dialog {
        margin: 1rem;
    }
    
    .logout-modal .modal-body {
        padding: 20px 15px;
    }
    
    .logout-modal .modal-footer {
        padding: 15px;
        flex-direction: column;
        gap: 10px;
    }
    
    .logout-modal .modal-footer .btn {
        width: 100%;
    }
}
