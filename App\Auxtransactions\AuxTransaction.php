<?php
session_start();
if (!isset($_SESSION['username'])) {
    header("Location: ../../Loginpage.php");
    exit();
}

// Include the database connection file
include $_SERVER['DOCUMENT_ROOT'].'/config/Databasehost.php';

// تضمين نظام الصلاحيات
include_once $_SERVER['DOCUMENT_ROOT'].'/App/includes/permissions.php';
include_once $_SERVER['DOCUMENT_ROOT'].'/App/includes/leader_filter.php';

// التحقق من صلاحية الوصول للصفحة
checkPagePermission('transactions', $_SESSION['access_level'] ?? 'Viewer');

// الحصول على معلومات المستخدم
$user_email = $_SESSION['username'];
$user_permission = $_SESSION['access_level'] ?? 'Viewer';

// Number of rows to display per page
$limit = 1000;

// Fetch unique AuxName values
$auxNames = [];
$auxQuery = "SELECT DISTINCT AuxName FROM interpretersaux";
$auxResult = $conn->query($auxQuery);
if ($auxResult->num_rows > 0) {
    while ($row = $auxResult->fetch_assoc()) {
        $auxNames[] = $row['AuxName'];
    }
}

// Fetch unique Leader values from usersprofile table
$leaders = [];
// Get leaders from usersprofile table using Firstname
$leaderQuery = "SELECT DISTINCT up.leader as Leader
                FROM usersprofile up
                WHERE up.leader IS NOT NULL AND up.leader != '' AND up.leader != 'N/A'";

$leaderResult = $conn->query($leaderQuery);
if ($leaderResult && $leaderResult->num_rows > 0) {
    while ($row = $leaderResult->fetch_assoc()) {
        $leaders[] = $row['Leader'];
    }
}

// Default filter values
$startDate = $_GET['start_date'] ?? '';
$endDate = $_GET['end_date'] ?? '';
$selectedAuxName = $_GET['aux_name'] ?? '';
$selectedLeader = $_GET['leader'] ?? '';
$searchQuery = $_GET['search'] ?? '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$offset = ($page - 1) * $limit;

// SQL query to fetch the required data with filters and leader information
// Get Interpreter Name and Leader from usersprofile table
$sql = "SELECT ia.Email, ia.AuxName, ia.Duration, ia.Starttime, ia.Endtime,
              COALESCE(up.leader, 'N/A') as Leader,
              COALESCE(CONCAT(up.Firstname, ' ', up.Lastname), 'N/A') as InterpreterName
         FROM interpretersaux ia
         LEFT JOIN usersprofile up ON ia.Email = up.Email
         WHERE 1=1";

// Add date filter if dates are provided
if (!empty($startDate)) {
    $sql .= " AND ia.Starttime >= '$startDate'";
}
if (!empty($endDate)) {
    $sql .= " AND ia.Endtime <= '$endDate'";
}

// Add filter for AuxName if provided
if (!empty($selectedAuxName)) {
    $sql .= " AND ia.AuxName = '" . $conn->real_escape_string($selectedAuxName) . "'";
}

// Add filter for Leader if provided
if (!empty($selectedLeader)) {
    $sql .= " AND up.leader = '" . $conn->real_escape_string($selectedLeader) . "'";
}

// Add search filter if search query is provided
if (!empty($searchQuery)) {
    $safeSearch = $conn->real_escape_string($searchQuery);
    $sql .= " AND (
        ia.Email LIKE '%$safeSearch%' OR
        ia.AuxName LIKE '%$safeSearch%' OR
        ia.Duration LIKE '%$safeSearch%' OR
        ia.Starttime LIKE '%$safeSearch%' OR
        ia.Endtime LIKE '%$safeSearch%' OR
        up.leader LIKE '%$safeSearch%' OR
        CONCAT(up.Firstname, ' ', up.Lastname) LIKE '%$safeSearch%'
    )";
}

// Order the results
$sql .= " ORDER BY ia.Starttime DESC LIMIT $limit OFFSET $offset";

// Execute the query
$result = $conn->query($sql);

// Create an array to store the results
$data = [];
if ($result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $data[] = $row;
    }
}

// Calculate the total number of records for pagination
$totalQuery = "SELECT COUNT(*) as total FROM interpretersaux ia
               LEFT JOIN usersprofile up ON ia.Email = up.Email
               WHERE 1=1";

if (!empty($startDate)) {
    $totalQuery .= " AND ia.Starttime >= '$startDate'";
}
if (!empty($endDate)) {
    $totalQuery .= " AND ia.Endtime <= '$endDate'";
}
if (!empty($selectedAuxName)) {
    $totalQuery .= " AND ia.AuxName = '" . $conn->real_escape_string($selectedAuxName) . "'";
}
if (!empty($selectedLeader)) {
    $totalQuery .= " AND up.leader = '" . $conn->real_escape_string($selectedLeader) . "'";
}
if (!empty($searchQuery)) {
    $totalQuery .= " AND (
        ia.Email LIKE '%$safeSearch%' OR
        ia.AuxName LIKE '%$safeSearch%' OR
        ia.Duration LIKE '%$safeSearch%' OR
        ia.Starttime LIKE '%$safeSearch%' OR
        ia.Endtime LIKE '%$safeSearch%' OR
        up.leader LIKE '%$safeSearch%' OR
        CONCAT(up.Firstname, ' ', up.Lastname) LIKE '%$safeSearch%'
    )";
}

$totalResult = $conn->query($totalQuery);
$totalRow = $totalResult->fetch_assoc();
$totalRecords = $totalRow['total'];
$totalPages = ceil($totalRecords / $limit);

// Export data to CSV when the export button is clicked
if (isset($_POST['export_csv'])) {
    // Get Interpreter Name and Leader from usersprofile table
    $export_sql = "SELECT ia.Email, ia.AuxName, ia.Duration, ia.Starttime, ia.Endtime,
                          COALESCE(up.leader, 'N/A') as Leader,
                          COALESCE(CONCAT(up.Firstname, ' ', up.Lastname), 'N/A') as InterpreterName
                   FROM interpretersaux ia
                   LEFT JOIN usersprofile up ON ia.Email = up.Email
                   WHERE 1=1";

    if (!empty($startDate)) {
        $export_sql .= " AND ia.Starttime >= '$startDate'";
    }
    if (!empty($endDate)) {
        $export_sql .= " AND ia.Endtime <= '$endDate'";
    }
    if (!empty($selectedAuxName)) {
        $export_sql .= " AND ia.AuxName = '" . $conn->real_escape_string($selectedAuxName) . "'";
    }
    if (!empty($selectedLeader)) {
        $export_sql .= " AND up.leader = '" . $conn->real_escape_string($selectedLeader) . "'";
    }
    if (!empty($searchQuery)) {
        $safeSearch = $conn->real_escape_string($searchQuery);
        $export_sql .= " AND (
            ia.Email LIKE '%$safeSearch%' OR
            ia.AuxName LIKE '%$safeSearch%' OR
            ia.Duration LIKE '%$safeSearch%' OR
            ia.Starttime LIKE '%$safeSearch%' OR
            ia.Endtime LIKE '%$safeSearch%' OR
            up.leader LIKE '%$safeSearch%' OR
            CONCAT(up.Firstname, ' ', up.Lastname) LIKE '%$safeSearch%'
        )";
    }

    $export_result = $conn->query($export_sql);

    if ($export_result->num_rows > 0) {
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="Aux_Transaction.csv"');
        $output = fopen('php://output', 'w');
        fputcsv($output, ['Email', 'AuxName', 'Duration', 'Starttime', 'Endtime', 'Leader', 'InterpreterName']);
        while ($row = $export_result->fetch_assoc()) {
            fputcsv($output, $row);
        }
        fclose($output);
        exit;
    } else {
        echo "<script>alert('No matching data found for the selected filters!');</script>";
    }
}
// Close the database connection
$conn->close();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aux Transaction</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link href="<!-- Google Fonts replaced with system fonts for CSP compliance -->" rel="stylesheet">
    <link rel="stylesheet" href="../assets/css/aux-transactions.css">

    <!-- Additional styles for leader filtering -->
    <style>
        .leader-text {
            font-size: 0.9em;
            font-weight: 400;
            color: #495057;
        }
        .leader-text:empty::after {
            content: 'N/A';
            color: #6c757d;
            font-style: italic;
        }
        .alert {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            font-weight: 500;
        }
        .alert-info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .table-container {
            overflow-x: auto;
        }
        table th:nth-child(2),
        table td:nth-child(2) {
            min-width: 150px;
        }
        table th:nth-child(3),
        table td:nth-child(3) {
            min-width: 100px;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="page-header">
            <h1><i class="fas fa-exchange-alt"></i> Aux Transaction Records</h1>
            <div class="sidebar-header">
                <img src="../../Logos/kalam.png" alt="Company Logo">
            </div>
        </div>

        <?php
        // عرض تنبيه للليدر بأن البيانات مفلترة
        if ($user_permission === 'Leader') {
            $leader_info = getLeaderFilterInfo($user_permission, $user_email);
            if ($leader_info['should_filter']) {
                echo '<div class="alert alert-info" style="margin: 20px 0; padding: 15px; background-color: #d1ecf1; border: 1px solid #bee5eb; border-radius: 5px; color: #0c5460;">';
                echo '<i class="fas fa-info-circle"></i> ';
                echo 'Displaying data for your team: <strong>' . htmlspecialchars($leader_info['leader_name']) . '</strong>';
                echo '</div>';
            }
        }
        ?>

        <!-- Filter section -->
        <div class="filter-section">
            <form method="GET" class="filter-form">
                <div class="form-group">
                    <label for="start_date"><i class="far fa-calendar-alt"></i> From Date</label>
                    <input type="date" id="start_date" name="start_date" class="form-control" value="<?php echo htmlspecialchars($startDate); ?>">
                </div>

                <div class="form-group">
                    <label for="end_date"><i class="far fa-calendar-alt"></i> To Date</label>
                    <input type="date" id="end_date" name="end_date" class="form-control" value="<?php echo htmlspecialchars($endDate); ?>">
                </div>

                <div class="form-group">
                    <label for="aux_name"><i class="fas fa-user-tag"></i> Aux Name</label>
                    <select id="aux_name" name="aux_name" class="form-control">
                        <option value="">-- All Aux Names --</option>
                        <?php foreach ($auxNames as $auxName): ?>
                            <option value="<?php echo htmlspecialchars($auxName); ?>" <?php echo ($auxName == $selectedAuxName) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($auxName); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <?php if ($user_permission !== 'Leader' || count($leaders) > 1): ?>
                <div class="form-group">
                    <label for="leader"><i class="fas fa-user-tie"></i> Leader</label>
                    <select id="leader" name="leader" class="form-control">
                        <option value="">-- All Leaders --</option>
                        <?php foreach ($leaders as $leader): ?>
                            <option value="<?php echo htmlspecialchars($leader); ?>" <?php echo ($leader == $selectedLeader) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($leader); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <?php endif; ?>

                <div class="form-group">
                    <label for="search"><i class="fas fa-search"></i> Search</label>
                    <input type="text" id="search" name="search" class="form-control" value="<?php echo htmlspecialchars($searchQuery); ?>" placeholder="Search records...">
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter"></i> Apply Filters
                    </button>
                </div>
            </form>

            <form method="POST" style="margin-top: 15px;">
                <button type="submit" name="export_csv" class="btn btn-export">
                    <i class="fas fa-file-export"></i> Export to CSV
                </button>
            </form>
        </div>

        <!-- Data Table -->
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th><i class="fas fa-envelope"></i> Email</th>
                        <th><i class="fas fa-user"></i> Interpreter Name</th>
                        <th><i class="fas fa-user-tie"></i> Leader</th>
                        <th><i class="fas fa-user-tag"></i> AuxName</th>
                        <th><i class="fas fa-clock"></i> Duration</th>
                        <th><i class="fas fa-play"></i> Start Time</th>
                        <th><i class="fas fa-stop"></i> End Time</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($data)): ?>
                        <?php foreach ($data as $row): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($row['Email']); ?></td>
                                <td><?php echo htmlspecialchars($row['InterpreterName']); ?></td>
                                <td>
                                    <span class="leader-text">
                                        <?php echo htmlspecialchars($row['Leader']); ?>
                                    </span>
                                </td>
                                <td><?php echo htmlspecialchars($row['AuxName']); ?></td>
                                <td><?php echo htmlspecialchars($row['Duration']); ?></td>
                                <td><?php echo htmlspecialchars($row['Starttime']); ?></td>
                                <td><?php echo htmlspecialchars($row['Endtime']); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="7" class="no-data">
                                <i class="fas fa-database" style="font-size: 24px; margin-bottom: 10px;"></i><br>
                                No transaction records found matching your criteria.
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <?php if ($totalPages > 1): ?>
            <div class="pagination">
                <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                    <a href="?page=<?php echo $i; ?>&start_date=<?php echo htmlspecialchars($startDate); ?>&end_date=<?php echo htmlspecialchars($endDate); ?>&aux_name=<?php echo htmlspecialchars($selectedAuxName); ?>&leader=<?php echo htmlspecialchars($selectedLeader); ?>&search=<?php echo htmlspecialchars($searchQuery); ?>"
                       class="page-link <?php echo ($i == $page) ? 'active' : ''; ?>">
                        <?php echo $i; ?>
                    </a>
                <?php endfor; ?>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>
