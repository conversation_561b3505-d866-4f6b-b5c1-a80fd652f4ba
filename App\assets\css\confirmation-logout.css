@import url('root-variables.css');

/* Custom backdrop with blur effect on entire screen */
#logoutModal.modal {
    background-color: rgba(var(--gray-500), 0.4) !important; /* Gray semi-transparent background using root variables */
    backdrop-filter: blur(5px) !important; /* Blur effect on entire background */
    -webkit-backdrop-filter: blur(5px) !important; /* Safari support */
}

#logoutModal .modal-dialog {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    z-index: 1055 !important;
    margin: 0 !important;
    filter: none !important; /* Ensure modal itself has no blur */
    backdrop-filter: none !important; /* Remove any blur from modal content */
    -webkit-backdrop-filter: none !important;
}

/* Remove Bootstrap's default backdrop */
.modal-backdrop {
    display: none !important;
}

/* Force blur effect when modal is shown */
#logoutModal.show,
#logoutModal.modal.show,
.modal.show#logoutModal {
    display: flex !important;
    background-color: rgba(var(--gray-500), 0.4) !important;
    backdrop-filter: blur(5px) !important;
    -webkit-backdrop-filter: blur(5px) !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 1050 !important;
}

/* Fallback for browsers that don't support backdrop-filter */
@supports not (backdrop-filter: blur(5px)) {
    #logoutModal.modal {
        background-color: rgba(var(--gray-600), 0.6) !important;
    }
}

/* Modal Content Styling */
.logout-modal .modal-content {
    background: var(--bg-white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    border: none;
    overflow: hidden;
    animation: modalSlideIn 0.3s ease-out;
}

.logout-modal .modal-header {
    background: var(--gradient-primary);
    color: var(--text-white);
    border-bottom: none;
    padding: var(--spacing-lg);
    text-align: center;
}

.logout-modal .modal-title {
    font-weight: 600;
    font-size: 1.25rem;
    margin: 0;
}

.logout-modal .modal-title i {
    margin-right: var(--spacing-sm);
}

.logout-modal .btn-close {
    background: none;
    border: none;
    color: var(--text-white);
    opacity: 0.8;
    font-size: 1.2rem;
}

.logout-modal .btn-close:hover {
    opacity: 1;
    color: var(--text-white);
}

.logout-modal .modal-body {
    padding: var(--spacing-lg);
    text-align: center;
    background: var(--bg-light);
}

.logout-modal .logout-icon {
    font-size: 3rem;
    color: var(--warning-500);
    margin-bottom: var(--spacing-md);
}

.logout-modal .modal-body h6 {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    font-size: 1.1rem;
}

.logout-modal .modal-body p {
    color: var(--text-secondary);
    margin-bottom: 0;
    font-size: 0.95rem;
}

.logout-modal .modal-footer {
    padding: var(--spacing-md) var(--spacing-lg);
    border-top: 1px solid var(--border-light);
    background: var(--bg-light);
    justify-content: center;
    gap: var(--spacing-md);
}

/* Button styling for Bootstrap modal */
#logoutModal .cancel-btn,
.logout-modal .cancel-btn {
    background-color: var(--gray-600) !important;
    color: var(--text-white) !important;
    border: none !important;
    border-radius: var(--radius-md) !important;
    padding: var(--spacing-sm) var(--spacing-md) !important;
    font-weight: 500 !important;
    transition: var(--transition-normal) !important;
    cursor: pointer !important;
}

#logoutModal .cancel-btn:hover,
.logout-modal .cancel-btn:hover {
    background-color: var(--gray-700) !important;
    color: var(--text-white) !important;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

#logoutModal .logout-btn,
.logout-modal .logout-btn {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%) !important;
    color: var(--text-white) !important;
    border: none !important;
    border-radius: var(--radius-md) !important;
    padding: var(--spacing-sm) var(--spacing-md) !important;
    font-weight: 500 !important;
    margin-left: var(--spacing-sm) !important;
    transition: var(--transition-normal) !important;
    cursor: pointer !important;
}

#logoutModal .logout-btn:hover,
.logout-modal .logout-btn:hover {
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-darker) 100%) !important;
    color: var(--text-white) !important;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Transition effect for smooth appearance */
#logoutModal {
    transition: var(--transition-normal) !important;
}

/* Animation for smooth appearance */
#logoutModal.show .modal-dialog {
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translate(-50%, -60%) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

/* Enhanced blur effect for different browsers */
@supports (backdrop-filter: blur(5px)) {
    #logoutModal.modal.show {
        backdrop-filter: blur(5px) saturate(120%) !important;
        -webkit-backdrop-filter: blur(5px) saturate(120%) !important;
    }
}

/* Ensure modal is open properly */
body.modal-open {
    overflow: hidden;
}

/* Ensure modal content is crystal clear */
#logoutModal .modal-content,
#logoutModal .modal-dialog,
#logoutModal .modal-header,
#logoutModal .modal-body,
#logoutModal .modal-footer,
#logoutModal .modal-content *,
#logoutModal .modal-dialog *,
#logoutModal .modal-header *,
#logoutModal .modal-body *,
#logoutModal .modal-footer * {
    filter: none !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
}

/* Ensure modal has proper background and visibility */
#logoutModal .modal-content {
    background: var(--bg-white) !important;
    border-radius: var(--radius-xl) !important;
    box-shadow: var(--shadow-xl) !important;
    border: none !important;
    overflow: hidden !important;
    opacity: 1 !important;
    pointer-events: auto !important;
}

/* Ensure buttons are clickable */
#logoutModal .modal-footer button,
#logoutModal .cancel-btn,
#logoutModal .logout-btn {
    pointer-events: auto !important;
    z-index: 1060 !important;
    position: relative !important;
}

/* Fix button focus and active states */
#logoutModal .cancel-btn:focus,
#logoutModal .logout-btn:focus {
    outline: 2px solid var(--primary-500) !important;
    outline-offset: 2px !important;
}

#logoutModal .cancel-btn:active,
#logoutModal .logout-btn:active {
    transform: translateY(1px) !important;
}

/* Responsive Design */
@media (max-width: 576px) {
    .logout-modal .modal-dialog {
        margin: var(--spacing-md);
        max-width: calc(100% - 2rem);
    }
    
    .logout-modal .modal-header,
    .logout-modal .modal-body,
    .logout-modal .modal-footer {
        padding: var(--spacing-md);
    }
    
    .logout-modal .logout-icon {
        font-size: 2.5rem;
    }
}
