<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Add Database Styles</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome 6 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="addindatabse-styles.css">
</head>
<body class="add-database">
    <div class="container py-5">
        <!-- Logo Header -->
        <div class="logo-container animate__animated animate__fadeInDown">
            <img src="/Logos/kalam.png" alt="Kalam Logo" class="img-fluid">
            <h1 class="mt-3">Data Management System</h1>
            <div class="user-info mt-2">
                <span class="badge bg-light text-dark">
                    <i class="fas fa-user-circle me-1"></i> <EMAIL>
                </span>
            </div>
        </div>

        <!-- Success Message -->
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i> This is a success message test
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <!-- Test Form -->
        <form method="POST" class="animate__animated animate__fadeIn" id="dataForm">
            <div class="card mb-4 fade-in">
                <div class="card-header d-flex align-items-center">
                    <i class="fas fa-user fa-2x me-3 icon-3d"></i>
                    <h3 class="mb-0">Basic Information</h3>
                    <span class="badge bg-light text-dark ms-auto">
                        3 fields
                    </span>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="floating-label">
                                <label for="InterpreterName">
                                    <i class="fas fa-user me-2"></i>
                                    Interpreter Name
                                    <span class="text-danger">*</span>
                                </label>
                                <input type="text" id="InterpreterName" name="InterpreterName" class="form-control" placeholder="Enter interpreter name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="floating-label">
                                <label for="FGEmails">
                                    <i class="fas fa-envelope me-2"></i>
                                    Email Address
                                </label>
                                <input type="email" id="FGEmails" name="FGEmails" class="form-control" placeholder="Enter email address">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="floating-label">
                                <label for="Status">
                                    <i class="fas fa-info-circle me-2"></i>
                                    Status
                                </label>
                                <select id="Status" name="Status" class="form-control">
                                    <option value="">Select Status</option>
                                    <option value="Active">Active</option>
                                    <option value="Inactive">Inactive</option>
                                    <option value="Pending">Pending</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="floating-label">
                                <label for="Language">
                                    <i class="fas fa-globe me-2"></i>
                                    Language
                                </label>
                                <textarea id="Language" name="Language" class="form-control" rows="3" placeholder="Enter language"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-4 fade-in">
                <div class="card-header d-flex align-items-center">
                    <i class="fas fa-briefcase fa-2x me-3 icon-3d"></i>
                    <h3 class="mb-0">Work Details</h3>
                    <span class="badge bg-light text-dark ms-auto">
                        2 fields
                    </span>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="floating-label">
                                <label for="Module">
                                    <i class="fas fa-cube me-2"></i>
                                    Module
                                </label>
                                <select id="Module" name="Module" class="form-control">
                                    <option value="">Select Module</option>
                                    <option value="Healthcare">Healthcare</option>
                                    <option value="Legal">Legal</option>
                                    <option value="Education">Education</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="floating-label">
                                <label for="Startdate">
                                    <i class="fas fa-calendar me-2"></i>
                                    Start Date
                                </label>
                                <input type="date" id="Startdate" name="Startdate" class="form-control">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Submit Buttons -->
            <div class="text-center mt-4">
                <button type="submit" class="btn btn-primary btn-lg">
                    <i class="fas fa-save me-2"></i>Save Data
                </button>
                <button type="reset" class="btn btn-outline-secondary btn-lg ms-3">
                    <i class="fas fa-undo me-2"></i>Reset Form
                </button>
                <a href="../index.php" class="btn btn-outline-info btn-lg ms-3">
                    <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                </a>
            </div>
        </form>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Animate form groups sequentially
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.fade-in');

            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = 1;
                    card.classList.add('show');
                }, index * 100);
            });
        });
    </script>
</body>
</html>
