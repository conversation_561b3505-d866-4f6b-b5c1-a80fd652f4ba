@import url('root-variables.css');

/* ==================== BASE STYLES ==================== */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', sans-serif;
}

body {
    background-color: var(--light-color);
    color: var(--dark-color);
    display: flex;
    min-height: 100vh;
    transition: var(--transition);
}

/* ==================== COMMON COMPONENTS ==================== */
/* Sidebar styles moved to sidebar.css */

/* All sidebar styles moved to sidebar.css */

/* Main Content - Shared across all pages */
.main-content {
    flex: 1;
    margin-left: 250px;
    transition: var(--transition);
    padding: 20px;
}

/* Sidebar margin adjustments moved to unified.css */

/* Page Header - Shared across all pages */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    animation: fadeIn 0.5s ease;
}

/* Common Button Styles */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 500;
    transition: var(--transition);
}

.btn-primary {
    background-color: var(--primary-dark);
    color: var(--white);
}

.btn-primary:hover {
    background-color: var(--primary-color);
    transform: translateY(-2px);
}

/* Common Form Elements */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.form-control {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-dark);
    outline: none;
    box-shadow: 0 0 0 2px rgba(208, 164, 208, 0.3);
}

/* Common Table Styles */
.table-responsive {
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
    font-size: 12px;
}

th, td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

th {
    background-color: var(--primary-color);
    color: var(--white);
    font-weight: 500;
    position: sticky;
    top: 0;
}

tr:nth-child(even) {
    background-color: #f9f9f9;
}

tr:hover {
    background-color: #f5f0f5;
}

/* Common Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    justify-content: center;
    align-items: center;
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 30px;
    width: 400px;
    max-width: 90%;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    transform: translateY(0);
    animation: modalSlideUp 0.3s ease;
}

/* Common Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes modalSlideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Common Responsive Design */
@media (max-width: 992px) {
    .main-content {
        margin-left: 0;
    }
    /* Sidebar responsive styles moved to sidebar.css */
}

@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .welcome-section {
        flex-direction: column;
        align-items: flex-start;
        margin-bottom: 15px;
    }
}

/* ==================== ADMIN HOME PAGE ==================== */

/* Stats Cards */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    animation: slideUp 0.5s ease;
    z-index: 1;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 100%;
    background: var(--primary-color);
}

.stat-card h3 {
    font-size: 14px;
    color: var(--gray-color);
    margin-bottom: 10px;
}

.stat-card p {
    font-size: 28px;
    font-weight: 600;
    color: var(--primary-dark);
    margin: 0;
}

/* Active Headcount Table */
.hc-table-container {
    margin-bottom: 30px;
    width: 100%;
    max-width: 400px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.hc-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.hc-table th,
.hc-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.hc-table thead th {
    background-color: var(--primary-color);
    color: var(--white);
    font-weight: 600;
    text-align: center;
}

.hc-table tbody tr:nth-child(even) {
    background-color: #f9f9f9;
}

.hc-table tbody tr:hover {
    background-color: #f5f0f5;
}

.hc-table .total-row {
    background-color: var(--primary-light);
    font-weight: 600;
    color: var(--primary-dark);
}

.hc-table .total-row td {
    border-top: 2px solid var(--primary-color);
}

/* Data Sections */
.data-sections {
    margin-top: 30px;
}

.section-container {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--shadow);
    animation: fadeIn 0.8s ease;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.section-header h2 {
    color: var(--primary-dark);
    font-size: 20px;
    font-weight: 600;
}

.section-nav {
    display: flex;
    gap: 10px;
}

.section-nav a {
    padding: 5px 15px;
    background: #f1f1f1;
    border-radius: 20px;
    color: var(--dark-color);
    text-decoration: none;
    font-size: 12px;
    transition: var(--transition);
}

.section-nav a:hover,
.section-nav a.active {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
}

/* Chart Container */
.chart-container {
    height: 350px;
    margin-top: 20px;
    background: var(--white);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.chart-wrapper {
    width: 100%;
    height: 100%;
    position: relative;
}

.chart-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--gray-color);
    font-family: 'Poppins', sans-serif;
}

.chart-loading i {
    font-size: 32px;
    margin-bottom: 15px;
    color: var(--primary-color);
    animation: spin 1s linear infinite;
}

.chart-loading p {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Status Table (smaller) */
.status-table-container {
    max-height: 300px;
    overflow-y: auto;
}

.status-table {
    font-size: 13px;
}

.status-table th,
.status-table td {
    padding: 8px 12px;
}

/* Responsive Design for Admin Home */
@media (max-width: 1200px) {
    .stats-container {
        grid-template-columns: repeat(2, 1fr);
    }

    .hc-table-container {
        width: 100%;
    }
}

@media (max-width: 768px) {
    .stats-container {
        grid-template-columns: 1fr;
    }

    .section-nav {
        flex-wrap: wrap;
    }
}

/* Default Avatar Styles */
.default-avatar {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(145deg, #f0f0f0, #ffffff);
    border-radius: 50%;
    color: var(--primary-dark);
    width: 100%;
    height: 100%;
    min-width: 45px;
    min-height: 45px;
    border: 2px solid var(--primary-light);
    overflow: hidden;
    position: relative;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.default-avatar:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(208, 164, 208, 0.2);
    border-color: var(--primary-color);
}

.default-avatar i {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.4em;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-dark);
    opacity: 0.9;
    transition: all 0.3s ease;
}

.default-avatar:hover i {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.1);
}

/* Profile Image in Header */
.welcome-section {
    display: flex;
    align-items: center;
    flex-direction: row; /* Ensure horizontal layout */
}

.welcome-section .profile-pic,
.welcome-section .default-avatar {
    width: 75px;
    height: 75px;
    margin-right: 20px;
    flex-shrink: 0; /* Prevent image from shrinking */
    position: relative;
    transition: all 0.3s ease;
}

.welcome-message {
    flex: 1; /* Take remaining space */
}

.welcome-message h1 {
    margin: 0;
    padding: 0;
    font-size: 1.5rem;
}

/* Fix any potential flex-direction issues */
@media (max-width: 768px) {
    .welcome-section {
        flex-direction: row; /* Force row even on mobile */
        align-items: center;
    }
}

.welcome-section .profile-pic {
    width: 60px;
    height: 60px;
    margin-right: 15px;
    position: relative;
    transition: all 0.3s ease;
}

.welcome-section .default-avatar {
    width: 60px;
    height: 60px;
    background: linear-gradient(145deg, #f8f8f8, #ffffff);
    border: 2px solid var(--primary-light);
}

.welcome-section .default-avatar i {
    font-size: 1.8em;
    color: var(--primary-dark);
}

.welcome-section .default-avatar:hover {
    border-color: var(--primary-color);
    box-shadow: 0 8px 25px rgba(208, 164, 208, 0.25);
}

/* Profile Image in Profile Page */
.profile-img {
    width: 90px;
    height: 90px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--primary-light);
    margin: 0 auto 15px;
    box-shadow: 0 6px 20px rgba(0,0,0,0.12);
    position: relative;
    transition: all 0.3s ease;
}

.profile-img.default-avatar {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(145deg, #f8f8f8, #ffffff);
    border: 3px solid var(--primary-light);
}

.profile-img.default-avatar i {
    font-size: 2.5em;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--primary-dark);
    opacity: 0.9;
    transition: all 0.3s ease;
}

.profile-img.default-avatar:hover {
    transform: scale(1.03);
    border-color: var(--primary-color);
    box-shadow: 0 8px 25px rgba(208, 164, 208, 0.25);
}

.profile-img.default-avatar:hover i {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.05);
}

/* Regular Profile Picture Styles */
.profile-pic {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--primary-light);
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.profile-pic:hover {
    transform: scale(1.05);
    border-color: var(--primary-color);
    box-shadow: 0 6px 20px rgba(208, 164, 208, 0.2);
}

/* All sidebar styles moved to sidebar.css */

/* تنسيق حاوية جدول Team Leader Headcount */
.leader-headcount-container {
    margin-top: 20px;
    margin-bottom: 30px;
    width: 100%;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* تنسيق جدول Team Leader Headcount */
.leader-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.leader-table th,
.leader-table td {
    padding: 12px 15px;
    text-align: center;
    border: 1px solid #e0e0e0;
}

.leader-table thead th {
    background-color: #f5f7fa;
    color: #333;
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: 10;
}

.leader-table tbody tr:nth-child(even) {
    background-color: #f9f9f9;
}

.leader-table tbody tr:hover {
    background-color: #f0f4ff;
}

.leader-table .total-row {
    background-color: #e9ecef;
    font-weight: 600;
}

/* تحسين التوافق مع الشاشات الصغيرة */
@media (max-width: 768px) {
    .leader-table {
        font-size: 12px;
    }

    .leader-table th,
    .leader-table td {
        padding: 8px 10px;
    }

    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .hc-table-container,
    .leader-headcount-container {
        max-width: 100%;
    }
}
