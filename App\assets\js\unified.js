// Unified JavaScript for Admin Pages
// ملف JavaScript موحد لجميع صفحات الإدارة

// ==================== COMMON FUNCTIONS ==================== //

// Toggle sidebar function
function toggleSidebar() {
    const sidebar = document.querySelector('.sidebar');
    const mainContent = document.querySelector('.main-content');

    if (sidebar) {
        sidebar.classList.toggle('collapsed');

        // تحديث المحتوى الرئيسي فوراً
        if (mainContent) {
            if (sidebar.classList.contains('collapsed')) {
                // عند تصغير القائمة
                if (window.innerWidth >= 1200) {
                    mainContent.style.marginLeft = '65px';
                } else if (window.innerWidth > 992) {
                    mainContent.style.marginLeft = '60px';
                } else {
                    mainContent.style.marginLeft = '0';
                }
            } else {
                // عند توسيع القائمة
                if (window.innerWidth >= 1200) {
                    mainContent.style.marginLeft = '240px';
                } else if (window.innerWidth > 992) {
                    mainContent.style.marginLeft = '220px';
                } else {
                    mainContent.style.marginLeft = '0';
                }
            }
        }
    }
}

// Toggle mobile menu function
function toggleMobileMenu() {
    const sidebar = document.querySelector('.sidebar');
    if (sidebar) {
        sidebar.classList.toggle('show');
    }
}

// Close mobile menu when clicking outside
document.addEventListener('click', function(event) {
    const sidebar = document.querySelector('.sidebar');
    const mobileBtn = document.querySelector('.mobile-menu-btn');

    if (sidebar && mobileBtn && window.innerWidth <= 992) {
        if (!sidebar.contains(event.target) && !mobileBtn.contains(event.target)) {
            sidebar.classList.remove('show');
        }
    }
});

// Handle window resize
window.addEventListener('resize', function() {
    const sidebar = document.querySelector('.sidebar');
    const mainContent = document.querySelector('.main-content');

    if (sidebar && window.innerWidth > 992) {
        sidebar.classList.remove('show');
    }

    // تحديث هامش المحتوى عند تغيير حجم الشاشة
    if (mainContent && sidebar) {
        if (window.innerWidth <= 992) {
            mainContent.style.marginLeft = '0';
        } else {
            if (sidebar.classList.contains('collapsed')) {
                if (window.innerWidth >= 1200) {
                    mainContent.style.marginLeft = '65px';
                } else {
                    mainContent.style.marginLeft = '60px';
                }
            } else {
                if (window.innerWidth >= 1200) {
                    mainContent.style.marginLeft = '240px';
                } else {
                    mainContent.style.marginLeft = '220px';
                }
            }
        }
    }
});

// ==================== PROFILE TAB FUNCTIONALITY ==================== //

// Tab functionality for profile pages
function openTab(tabName) {
    // Hide all tab contents
    const tabContents = document.getElementsByClassName('tab-content');
    for (let i = 0; i < tabContents.length; i++) {
        tabContents[i].classList.remove('active');
    }

    // Remove active class from all tab buttons
    const tabButtons = document.getElementsByClassName('tab-btn');
    for (let i = 0; i < tabButtons.length; i++) {
        tabButtons[i].classList.remove('active');
    }

    // Show selected tab content and mark button as active
    const selectedTab = document.getElementById(tabName);
    if (selectedTab) {
        selectedTab.classList.add('active');
    }

    // Mark the clicked button as active
    if (event && event.currentTarget) {
        event.currentTarget.classList.add('active');
    }
}

// ==================== MODAL FUNCTIONALITY ==================== //

// Show modal function
function showModal(modalId = 'logout-modal') {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'flex';
    }
}

// Hide modal function
function hideModal(modalId = 'logout-modal') {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'none';
    }
}

// Close modals when clicking outside
window.addEventListener('click', function(event) {
    if (event.target.classList.contains('modal')) {
        event.target.style.display = 'none';
    }
});

// ==================== FORM VALIDATION ==================== //

// Basic form validation
function validateForm(formId) {
    const form = document.getElementById(formId);
    if (!form) return false;

    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('error');
            isValid = false;
        } else {
            field.classList.remove('error');
        }
    });

    return isValid;
}

// Email validation
function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// ==================== TABLE FUNCTIONALITY ==================== //

// Initialize DataTables with common settings
function initializeDataTable(tableId, options = {}) {
    const defaultOptions = {
        responsive: true,
        pageLength: 50,
        lengthChange: false,
        language: {
            search: "_INPUT_",
            searchPlaceholder: "Search...",
            info: "Showing _START_ to _END_ of _TOTAL_ entries",
            infoEmpty: "Showing 0 to 0 of 0 entries",
            infoFiltered: "(filtered from _MAX_ total entries)",
            paginate: {
                first: "First",
                last: "Last",
                next: "Next",
                previous: "Previous"
            },
            zeroRecords: "No matching records found"
        }
    };

    const finalOptions = { ...defaultOptions, ...options };

    if (typeof $ !== 'undefined' && $.fn.DataTable) {
        return $('#' + tableId).DataTable(finalOptions);
    }

    return null;
}

// ==================== NOTIFICATION SYSTEM ==================== //

// Show notification
function showNotification(message, type = 'info', duration = 3000) {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => notification.remove());

    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${getNotificationIcon(type)}"></i>
            <span>${message}</span>
            <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 10000;
        background: ${getNotificationColor(type)};
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        animation: slideInRight 0.3s ease;
        max-width: 400px;
    `;

    // Add to page
    document.body.appendChild(notification);

    // Auto remove after duration
    if (duration > 0) {
        setTimeout(() => {
            if (notification.parentElement) {
                notification.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => notification.remove(), 300);
            }
        }, duration);
    }
}

// Get notification icon based on type
function getNotificationIcon(type) {
    const icons = {
        success: 'check-circle',
        error: 'exclamation-circle',
        warning: 'exclamation-triangle',
        info: 'info-circle'
    };
    return icons[type] || 'info-circle';
}

// Get notification color based on type
function getNotificationColor(type) {
    const colors = {
        success: '#4caf50',
        error: '#f44336',
        warning: '#ff9800',
        info: '#2196f3'
    };
    return colors[type] || '#2196f3';
}

// ==================== AJAX HELPERS ==================== //

// Generic AJAX request function
function makeAjaxRequest(url, method = 'GET', data = null, successCallback = null, errorCallback = null) {
    const xhr = new XMLHttpRequest();
    xhr.open(method, url, true);

    if (method === 'POST') {
        xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
    }

    xhr.onreadystatechange = function() {
        if (xhr.readyState === XMLHttpRequest.DONE) {
            if (xhr.status === 200) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (successCallback) {
                        successCallback(response);
                    }
                } catch (e) {
                    if (successCallback) {
                        successCallback(xhr.responseText);
                    }
                }
            } else {
                if (errorCallback) {
                    errorCallback(xhr.status, xhr.statusText);
                } else {
                    showNotification('An error occurred. Please try again.', 'error');
                }
            }
        }
    };

    xhr.send(data);
}

// ==================== UTILITY FUNCTIONS ==================== //

// Format date - استخدام TimezoneManager إذا كان متاحاً
function formatDate(dateString, format = 'MM/DD/YYYY') {
    // التحقق من وجود TimezoneManager
    if (typeof TimezoneManager !== 'undefined') {
        return TimezoneManager.formatDate(dateString, format);
    }

    // Fallback للطريقة القديمة
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;

    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const year = date.getFullYear();

    switch (format) {
        case 'DD/MM/YYYY':
            return `${day}/${month}/${year}`;
        case 'YYYY-MM-DD':
            return `${year}-${month}-${day}`;
        default:
            return `${month}/${day}/${year}`;
    }
}

// Debounce function for search inputs
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// ==================== INITIALIZATION ==================== //

// Initialize common functionality when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Add CSS animations
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes slideOutRight {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }

        .notification-content {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .notification-close {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            padding: 0;
            margin-left: auto;
        }

        .form-control.error {
            border-color: #f44336;
            box-shadow: 0 0 0 3px rgba(244, 67, 54, 0.1);
        }
    `;
    document.head.appendChild(style);

    // Initialize tooltips if Bootstrap is available
    if (typeof bootstrap !== 'undefined') {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    // Add loading states to forms (excluding export and reactivate forms)
    const forms = document.querySelectorAll('form:not(.export-form):not(.reactivate-form)');
    forms.forEach(form => {
        form.addEventListener('submit', function() {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
            }
        });
    });
});

// ==================== EXPORT FUNCTIONS ==================== //

// Make functions available globally
window.toggleSidebar = toggleSidebar;
window.toggleMobileMenu = toggleMobileMenu;
window.openTab = openTab;
window.showModal = showModal;
window.hideModal = hideModal;
window.validateForm = validateForm;
window.validateEmail = validateEmail;
window.initializeDataTable = initializeDataTable;
window.showNotification = showNotification;
window.makeAjaxRequest = makeAjaxRequest;
window.formatDate = formatDate;
window.debounce = debounce;
