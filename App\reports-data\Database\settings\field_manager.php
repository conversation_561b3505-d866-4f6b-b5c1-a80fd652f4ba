<?php
/**
 * Field Manager - Helper functions for field management
 */

class FieldManager {
    private $conn;
    private $configFile;
    private $defaultConfigFile;
    
    public function __construct($conn) {
        $this->conn = $conn;
        $this->configFile = __DIR__ . '/field_config.json';
        $this->defaultConfigFile = __DIR__ . '/default_field_config.json';
    }
    
    /**
     * Get all available database columns
     */
    public function getDatabaseColumns() {
        $columns = [];
        $result = $this->conn->query("DESCRIBE databasehc");
        if ($result) {
            while ($row = $result->fetch_assoc()) {
                $columns[$row['Field']] = [
                    'name' => $row['Field'],
                    'type' => $row['Type'],
                    'null' => $row['Null'],
                    'key' => $row['Key'],
                    'default' => $row['Default'],
                    'extra' => $row['Extra']
                ];
            }
        }
        return $columns;
    }
    
    /**
     * Get fields that exist in database but not in configuration
     */
    public function getUnmappedFields() {
        $dbColumns = $this->getDatabaseColumns();
        $config = $this->loadConfiguration();
        
        $mappedFields = [];
        foreach ($config as $section) {
            if (isset($section['fields'])) {
                $mappedFields = array_merge($mappedFields, array_keys($section['fields']));
            }
        }
        
        $unmappedFields = [];
        foreach ($dbColumns as $columnName => $columnInfo) {
            if (!in_array($columnName, $mappedFields) && $columnName !== 'ID') {
                $unmappedFields[$columnName] = $columnInfo;
            }
        }
        
        return $unmappedFields;
    }
    
    /**
     * Get fields that exist in configuration but not in database
     */
    public function getOrphanedFields() {
        $dbColumns = $this->getDatabaseColumns();
        $config = $this->loadConfiguration();
        
        $orphanedFields = [];
        foreach ($config as $sectionKey => $section) {
            if (isset($section['fields'])) {
                foreach ($section['fields'] as $fieldName => $fieldConfig) {
                    if (!isset($dbColumns[$fieldName])) {
                        $orphanedFields[] = [
                            'field' => $fieldName,
                            'section' => $sectionKey,
                            'config' => $fieldConfig
                        ];
                    }
                }
            }
        }
        
        return $orphanedFields;
    }
    
    /**
     * Load field configuration with database fallback
     */
    public function loadConfiguration() {
        global $conn;

        // Try file first
        if (file_exists($this->configFile) && is_readable($this->configFile)) {
            $content = file_get_contents($this->configFile);
            $config = json_decode($content, true);
            if (json_last_error() === JSON_ERROR_NONE && !empty($config)) {
                return $config;
            }
        }

        // Try database fallback
        try {
            $stmt = $conn->prepare("SELECT config_data FROM field_config_storage WHERE config_key = ?");
            $key = "main_config";
            $stmt->bind_param("s", $key);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($row = $result->fetch_assoc()) {
                $config = json_decode($row['config_data'], true);
                if (!empty($config)) {
                    return $config;
                }
            }
        } catch (Exception $e) {
            error_log("Database config load error: " . $e->getMessage());
        }

        // Fallback to default
        if (file_exists($this->defaultConfigFile)) {
            $config = json_decode(file_get_contents($this->defaultConfigFile), true);
            if ($config) {
                return $config;
            }
        }

        return [];
    }
    
    /**
     * Save field configuration with database fallback
     */
    public function saveConfiguration($config) {
        global $conn;

        $configJson = json_encode($config, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

        // Try to save to file first
        if (is_writable(dirname($this->configFile))) {
            $result = file_put_contents($this->configFile, $configJson);
            if ($result !== false) {
                return true;
            }
        }

        // Fallback to database storage
        try {
            // Create table if not exists
            $conn->query("CREATE TABLE IF NOT EXISTS field_config_storage (
                id INT PRIMARY KEY AUTO_INCREMENT,
                config_key VARCHAR(255) UNIQUE,
                config_data TEXT,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )");

            $stmt = $conn->prepare("INSERT INTO field_config_storage (config_key, config_data) VALUES (?, ?)
                                   ON DUPLICATE KEY UPDATE config_data = VALUES(config_data)");
            $key = "main_config";
            $stmt->bind_param("ss", $key, $configJson);

            return $stmt->execute();
        } catch (Exception $e) {
            error_log("Failed to save configuration: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Auto-map unmapped fields to appropriate sections
     */
    public function autoMapFields() {
        $unmappedFields = $this->getUnmappedFields();
        $config = $this->loadConfiguration();
        
        $mapped = 0;
        foreach ($unmappedFields as $fieldName => $fieldInfo) {
            $section = $this->determineBestSection($fieldName, $fieldInfo);
            $fieldConfig = $this->generateFieldConfig($fieldName, $fieldInfo);
            
            // Ensure section exists
            if (!isset($config[$section])) {
                $config[$section] = [
                    'title' => ucwords(str_replace('_', ' ', $section)),
                    'icon' => 'fa-cog',
                    'fields' => []
                ];
            }
            
            $config[$section]['fields'][$fieldName] = $fieldConfig;
            $mapped++;
        }
        
        if ($mapped > 0) {
            $this->saveConfiguration($config);
        }
        
        return $mapped;
    }
    
    /**
     * Clean orphaned fields from configuration with detailed logging
     */
    public function cleanOrphanedFields() {
        $debug = isset($_GET['debug']) ? true : false;

        if ($debug) {
            echo "<div style='background: #e7f3ff; padding: 10px; margin: 5px 0; border-radius: 5px; font-family: monospace;'>";
            echo "<strong>🔍 cleanOrphanedFields() Debug:</strong><br>";
        }

        $orphanedFields = $this->getOrphanedFields();
        $config = $this->loadConfiguration();

        if ($debug) {
            echo "📊 Found " . count($orphanedFields) . " orphaned fields<br>";
            echo "📋 Current config sections: " . implode(', ', array_keys($config)) . "<br>";
            echo "🔍 Orphaned fields details:<br>";
            foreach ($orphanedFields as $i => $orphan) {
                echo "&nbsp;&nbsp;" . ($i+1) . ". {$orphan['section']}|{$orphan['field']}<br>";
            }
        }

        $cleaned = 0;
        $cleanedList = [];

        foreach ($orphanedFields as $orphan) {
            $section = $orphan['section'];
            $field = $orphan['field'];

            if ($debug) {
                echo "🔍 Checking: {$section}|{$field} - ";
            }

            if (isset($config[$section]['fields'][$field])) {
                unset($config[$section]['fields'][$field]);
                $cleaned++;
                $cleanedList[] = "{$section}|{$field}";

                if ($debug) {
                    echo "✅ REMOVED<br>";
                }

                // Remove section if empty
                if (empty($config[$section]['fields'])) {
                    unset($config[$section]);
                    if ($debug) {
                        echo "&nbsp;&nbsp;📁 Section '{$section}' removed (empty)<br>";
                    }
                }
            } else {
                if ($debug) {
                    echo "⚠️ NOT FOUND in config<br>";
                }
            }
        }

        if ($debug) {
            echo "📊 Total cleaned: $cleaned fields<br>";
            echo "📋 Cleaned list: " . implode(', ', $cleanedList) . "<br>";
        }

        if ($cleaned > 0) {
            $saveResult = $this->saveConfiguration($config);
            if ($debug) {
                echo "💾 Save result: " . ($saveResult ? "✅ SUCCESS" : "❌ FAILED") . "<br>";
            }

            if (!$saveResult) {
                if ($debug) {
                    echo "❌ Failed to save configuration!<br>";
                }
                error_log("Failed to save configuration after cleaning orphaned fields");
            }
        } else {
            if ($debug) {
                echo "ℹ️ No fields were cleaned<br>";
            }
        }

        if ($debug) {
            echo "</div>";
        }

        // Force reload configuration to get accurate count
        $this->configCache = null;

        return $cleaned;
    }
    
    /**
     * Determine best section for a field
     */
    private function determineBestSection($fieldName, $fieldInfo) {
        $fieldLower = strtolower($fieldName);
        
        // Basic info patterns
        if (preg_match('/^(name|email|contact|phone|address)/', $fieldLower)) {
            return 'basic_info';
        }
        
        // Work details patterns
        if (preg_match('/^(module|status|account|seat|vendor|leader|position|role)/', $fieldLower)) {
            return 'work_details';
        }
        
        // Schedule patterns
        if (preg_match('/^(monday|tuesday|wednesday|thursday|friday|saturday|sunday|shift|schedule)/', $fieldLower)) {
            return 'schedule';
        }
        
        // Date patterns
        if (preg_match('/(date|time)/', $fieldLower) || strpos($fieldInfo['type'], 'date') !== false) {
            return 'dates';
        }
        
        // Boolean/checkbox patterns
        if (strpos($fieldInfo['type'], 'tinyint(1)') !== false || preg_match('/^(is_|has_|can_|enable_)/', $fieldLower)) {
            return 'certifications';
        }
        
        // Default to additional
        return 'additional';
    }
    
    /**
     * Generate field configuration based on database column info
     */
    private function generateFieldConfig($fieldName, $fieldInfo) {
        $config = [
            'label' => ucwords(str_replace(['_', '-'], ' ', $fieldName)),
            'icon' => $this->getFieldIcon($fieldName),
            'col' => 6,
            'required' => $fieldInfo['null'] === 'NO' && $fieldInfo['default'] === null,
            'enabled' => true
        ];
        
        // Determine field type
        $type = strtolower($fieldInfo['type']);
        
        if (strpos($type, 'enum') !== false) {
            $config['type'] = 'select';
            $config['other'] = true;
        } elseif (strpos($type, 'date') !== false) {
            $config['type'] = 'date';
        } elseif (strpos($type, 'time') !== false) {
            $config['type'] = 'time';
        } elseif (strpos($type, 'int') !== false) {
            $config['type'] = 'number';
        } elseif (strpos($type, 'text') !== false) {
            $config['type'] = 'textarea';
        } elseif (strpos($type, 'tinyint(1)') !== false) {
            $config['type'] = 'checkbox';
        } elseif (preg_match('/email/i', $fieldName)) {
            $config['type'] = 'email';
        } else {
            $config['type'] = 'text';
        }
        
        return $config;
    }
    
    /**
     * Get appropriate icon for field
     */
    private function getFieldIcon($fieldName) {
        $iconMap = [
            'name' => 'fa-user',
            'email' => 'fa-envelope',
            'phone' => 'fa-phone',
            'address' => 'fa-map-marker-alt',
            'date' => 'fa-calendar',
            'time' => 'fa-clock',
            'status' => 'fa-info-circle',
            'module' => 'fa-cube',
            'account' => 'fa-building',
            'seat' => 'fa-chair',
            'vendor' => 'fa-store',
            'leader' => 'fa-user-shield',
            'score' => 'fa-star',
            'password' => 'fa-key',
            'language' => 'fa-globe',
        ];
        
        $fieldLower = strtolower($fieldName);
        
        foreach ($iconMap as $pattern => $icon) {
            if (strpos($fieldLower, $pattern) !== false) {
                return $icon;
            }
        }
        
        return 'fa-edit';
    }
    
    /**
     * Get field statistics
     */
    public function getFieldStats() {
        $dbColumns = count($this->getDatabaseColumns()) - 1; // Exclude ID
        $config = $this->loadConfiguration();
        
        $configuredFields = 0;
        $enabledFields = 0;
        $sections = count($config);
        
        foreach ($config as $section) {
            if (isset($section['fields'])) {
                $configuredFields += count($section['fields']);
                foreach ($section['fields'] as $field) {
                    if ($field['enabled'] ?? true) {
                        $enabledFields++;
                    }
                }
            }
        }
        
        return [
            'total_db_columns' => $dbColumns,
            'configured_fields' => $configuredFields,
            'enabled_fields' => $enabledFields,
            'sections' => $sections,
            'unmapped_fields' => count($this->getUnmappedFields()),
            'orphaned_fields' => count($this->getOrphanedFields())
        ];
    }
}
?>
