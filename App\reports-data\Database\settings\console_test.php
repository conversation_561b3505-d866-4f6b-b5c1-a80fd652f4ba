<?php
/**
 * Console Test - Test console logging functionality
 */

session_start();
include $_SERVER['DOCUMENT_ROOT'].'/config/Databasehost.php';
include '../system/database_config.php';

// Check session and access
if (!isset($_SESSION['username']) || !isset($_SESSION['random_code'])) {
    header("Location: ../../../../Loginpage.php");
    exit();
}

$access = $_SESSION['access_level'];
$email = $_SESSION['username'];

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Console Logging Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            min-height: 100vh; 
            font-family: 'Segoe UI', sans-serif;
        }
        .test-card { 
            background: white; 
            border-radius: 15px; 
            box-shadow: 0 10px 30px rgba(0,0,0,0.2); 
            margin: 20px 0; 
        }
        .test-header { 
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); 
            color: white; 
            padding: 20px; 
            border-radius: 15px 15px 0 0; 
        }
        .btn:hover { 
            transform: translateY(-2px); 
            box-shadow: 0 5px 15px rgba(0,0,0,0.3); 
        }
        .console-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="test-card">
            <div class="test-header text-center">
                <h1><i class="fas fa-terminal me-3"></i>Console Logging Test</h1>
                <p class="mb-0">Test detailed console logging for all button actions</p>
            </div>

            <div class="card-body p-4">
                <div class="console-info">
                    <h5><i class="fas fa-info-circle me-2"></i>How to View Console Logs</h5>
                    <ol>
                        <li><strong>Chrome/Edge:</strong> Press F12 → Click "Console" tab</li>
                        <li><strong>Firefox:</strong> Press F12 → Click "Console" tab</li>
                        <li><strong>Safari:</strong> Press Cmd+Option+I → Click "Console" tab</li>
                    </ol>
                    <p class="mb-0"><strong>Note:</strong> All button clicks will show detailed information in the console.</p>
                </div>

                <div class="row">
                    <!-- Field Settings Actions -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">Field Settings Actions</h5>
                            </div>
                            <div class="card-body">
                                <!-- Sync Button -->
                                <form method="POST" class="mb-3" id="syncForm">
                                    <input type="hidden" name="action" value="sync_with_db">
                                    <button type="submit" class="btn btn-info btn-lg w-100" id="syncButton">
                                        <i class="fas fa-sync me-2"></i>Test Sync with Database
                                    </button>
                                </form>

                                <!-- Reset Button -->
                                <form method="POST" class="mb-3" id="resetForm">
                                    <input type="hidden" name="action" value="reset_config">
                                    <button type="submit" class="btn btn-warning btn-lg w-100" id="resetButton">
                                        <i class="fas fa-undo me-2"></i>Test Reset Configuration
                                    </button>
                                </form>

                                <!-- Bulk Delete Button -->
                                <button type="button" class="btn btn-danger btn-lg w-100 mb-3" onclick="testBulkDelete()">
                                    <i class="fas fa-trash me-2"></i>Test Bulk Delete
                                </button>

                                <!-- Add Field Button -->
                                <button type="button" class="btn btn-success btn-lg w-100" onclick="testAddField()">
                                    <i class="fas fa-plus me-2"></i>Test Add Field
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Diagnostics Actions -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0">Diagnostics Actions</h5>
                            </div>
                            <div class="card-body">
                                <!-- Auto Map Button -->
                                <form method="POST" class="mb-3">
                                    <input type="hidden" name="action" value="auto_map">
                                    <button type="submit" class="btn btn-info btn-lg w-100">
                                        <i class="fas fa-magic me-2"></i>Test Auto-Map Fields
                                    </button>
                                </form>

                                <!-- Clean Orphaned Button -->
                                <form method="POST" class="mb-3">
                                    <input type="hidden" name="action" value="clean_orphaned">
                                    <button type="submit" class="btn btn-warning btn-lg w-100">
                                        <i class="fas fa-broom me-2"></i>Test Clean Orphaned
                                    </button>
                                </form>

                                <!-- Navigation Button -->
                                <a href="field_diagnostics.php" class="btn btn-outline-primary btn-lg w-100 mb-3">
                                    <i class="fas fa-stethoscope me-2"></i>Go to Diagnostics
                                </a>

                                <!-- Field Settings Button -->
                                <a href="field_settings.php" class="btn btn-outline-secondary btn-lg w-100">
                                    <i class="fas fa-cogs me-2"></i>Go to Field Settings
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Console Output Display -->
                <div class="card mt-4">
                    <div class="card-header bg-dark text-white">
                        <h5 class="mb-0"><i class="fas fa-terminal me-2"></i>Live Console Output</h5>
                    </div>
                    <div class="card-body">
                        <div id="consoleOutput" style="background: #000; color: #00ff00; padding: 15px; border-radius: 5px; font-family: 'Courier New', monospace; height: 300px; overflow-y: auto;">
                            <div>Console output will appear here...</div>
                        </div>
                        <button type="button" class="btn btn-secondary mt-2" onclick="clearConsoleOutput()">
                            <i class="fas fa-eraser me-1"></i>Clear Output
                        </button>
                    </div>
                </div>

                <!-- Instructions -->
                <div class="alert alert-info mt-4">
                    <h5><i class="fas fa-lightbulb me-2"></i>Testing Instructions</h5>
                    <ol>
                        <li>Open your browser's developer console (F12)</li>
                        <li>Click any button above</li>
                        <li>Watch the detailed logs in the console</li>
                        <li>Each action will show:
                            <ul>
                                <li>Timestamp</li>
                                <li>Action type</li>
                                <li>Button details</li>
                                <li>Form data</li>
                                <li>User confirmations</li>
                                <li>Processing status</li>
                            </ul>
                        </li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Initialize console logging
        console.log('🚀 Console Test Page Initialized');
        console.log('📊 Page loaded at:', new Date().toLocaleString());
        console.log('👤 Current user:', '<?php echo $email; ?>');
        console.log('🔑 Access level:', '<?php echo $access; ?>');

        // Capture console.log and display in page
        const originalLog = console.log;
        const originalGroup = console.group;
        const originalGroupEnd = console.groupEnd;
        const originalWarn = console.warn;
        
        const consoleOutput = document.getElementById('consoleOutput');
        
        function addToConsoleOutput(message, type = 'log') {
            const div = document.createElement('div');
            div.style.marginBottom = '5px';
            
            switch(type) {
                case 'group':
                    div.style.fontWeight = 'bold';
                    div.style.color = '#ffff00';
                    break;
                case 'warn':
                    div.style.color = '#ff8800';
                    break;
                case 'error':
                    div.style.color = '#ff0000';
                    break;
                default:
                    div.style.color = '#00ff00';
            }
            
            div.textContent = message;
            consoleOutput.appendChild(div);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsoleOutput(args.join(' '), 'log');
        };
        
        console.group = function(...args) {
            originalGroup.apply(console, args);
            addToConsoleOutput('▼ ' + args.join(' '), 'group');
        };
        
        console.groupEnd = function() {
            originalGroupEnd.apply(console);
            addToConsoleOutput('▲ Group End', 'group');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsoleOutput('⚠️ ' + args.join(' '), 'warn');
        };

        // Test functions
        function testBulkDelete() {
            console.group('🔥 Test Bulk Delete Action');
            console.log('⏰ Timestamp:', new Date().toLocaleString());
            console.log('🎯 Action: Test Bulk Delete');
            console.log('📊 Simulating selection of 3 fields');
            console.log('📋 Selected fields: ["basic_info|name", "contact_info|email", "work_details|status"]');
            
            const confirmed = confirm('Are you sure you want to delete 3 selected fields?');
            if (confirmed) {
                console.log('✅ User confirmed bulk delete operation');
                console.log('🗑️ Would delete: name, email, status');
                console.log('💾 Would save updated configuration');
            } else {
                console.log('❌ User cancelled bulk delete operation');
            }
            console.groupEnd();
        }

        function testAddField() {
            console.group('➕ Test Add Field Action');
            console.log('⏰ Timestamp:', new Date().toLocaleString());
            console.log('🎯 Action: Test Add Field');
            console.log('📝 Would open Add Field Modal');
            console.log('⚙️ User can configure: name, type, section, icon');
            console.log('💾 Would save new field to configuration');
            console.groupEnd();
        }

        function clearConsoleOutput() {
            consoleOutput.innerHTML = '<div>Console cleared...</div>';
        }

        // Add enhanced logging to all buttons
        document.addEventListener('DOMContentLoaded', function() {
            const allButtons = document.querySelectorAll('button[type="submit"], button[onclick]');
            console.log('🔘 Found', allButtons.length, 'buttons for logging');

            allButtons.forEach((button, index) => {
                if (!button.onclick && button.type === 'submit') {
                    button.addEventListener('click', function(e) {
                        const form = this.closest('form');
                        if (form) {
                            const actionInput = form.querySelector('input[name="action"]');
                            const action = actionInput ? actionInput.value : 'unknown';
                            
                            console.group('🔄 Form Submit Action #' + (index + 1));
                            console.log('⏰ Timestamp:', new Date().toLocaleString());
                            console.log('🎯 Action:', action);
                            console.log('🔘 Button text:', this.textContent.trim());
                            
                            // Prevent actual submission for testing
                            e.preventDefault();
                            console.log('🚫 Form submission prevented (test mode)');
                            console.groupEnd();
                        }
                    });
                }
            });
        });
    </script>
</body>
</html>
