/* @import url('root-variables.css'); - تم تعطيله لتجنب مشاكل المسارات */

/*home , users, leaves, archive, profile, create leaves , my leaves admin, AuxTransactions*/
/* ==================== BASE STYLES ==================== */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', sans-serif;
}

body {
    background-color: var(--light-color);
    color: var(--dark-color);
    display: flex;
    min-height: 100vh;
    transition: var(--transition);
}

/* ==================== COMMON COMPONENTS ==================== */

/* Sidebar - Shared across all pages */
.sidebar {
    width: 250px;
    height: 100vh;
    background-color: var(--primary-color);
    color: var(--white);
    position: fixed;
    transition: var(--transition);
    z-index: 1000;
    box-shadow: var(--shadow);
    display: flex;
    flex-direction: column;
}

.sidebar.collapsed {
    width: 70px;
}

.sidebar-header {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    justify-content: center;
    align-items: center;
    height: 80px;
}

.sidebar-header .logo,
.sidebar-header img {
    max-width: 80%;
    max-height: 60px;
    width: 120px;
    height: auto;
    object-fit: contain;
    transition: all 0.3s ease;
    display: block;
    margin: 0 auto;
}

.sidebar.collapsed .sidebar-header .logo,
.sidebar.collapsed .sidebar-header img {
    max-width: 60%;
    width: 40px;
    opacity: 0.9;
}

.sidebar-menu {
    flex: 1;
    overflow-y: auto;
    padding: 15px 0;
}

.sidebar-menu a {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: var(--white);
    text-decoration: none;
    transition: var(--transition);
    border-left: 3px solid transparent;
    margin: 5px 0;
}

.sidebar-menu a:hover, 
.sidebar-menu a.active {
    background-color: var(--primary-dark);
    border-left: 3px solid var(--white);
}

.sidebar-menu a i {
    margin-right: 10px;
    font-size: 18px;
    min-width: 25px;
    text-align: center;
}

.sidebar-menu a span {
    transition: var(--transition);
}

.sidebar.collapsed .sidebar-menu a span {
    display: none;
}

.sidebar-footer {
    padding: 15px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.sidebar-footer button {
    width: 100%;
    padding: 10px;
    background-color: var(--primary-dark);
    border: none;
    color: var(--white);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
}

.sidebar-footer button:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
}

.sidebar-footer button i {
    margin-right: 5px;
}

.sidebar-footer button span {
    transition: var(--transition);
}

.sidebar.collapsed .sidebar-footer button span {
    display: none;
}

/* Main Content - Shared across all pages */
.main-content {
    flex: 1;
    margin-left: 250px;
    transition: var(--transition);
    padding: 20px;
}

.sidebar.collapsed ~ .main-content {
    margin-left: 70px;
}

/* Page Header - Shared across all pages */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    animation: fadeIn 0.5s ease;
}

.welcome-section {
    display: flex;
    align-items: center;
}

.welcome-message h1 {
    font-size: 24px;
    font-weight: 600;
    color: var(--primary-dark);
    margin: 0;
}

.welcome-message p {
    color: var(--gray-color);
    font-size: 14px;
    margin-top: 5px;
}

.profile-pic {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--primary-light);
    transition: all 0.3s ease;
}

.profile-pic.default-avatar {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f0f0f0;
    border: 1px solid #ddd;
}

.welcome-section .profile-pic {
    width: 60px;
    height: 60px;
    margin-right: 15px;
    position: relative;
}

.welcome-section .default-avatar {
    width: 60px;
    height: 60px;
}

.welcome-section .default-avatar i {
    font-size: 1.8em;
}

/* Modal - Shared across all pages */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    justify-content: center;
    align-items: center;
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 30px;
    width: 400px;
    max-width: 90%;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    transform: translateY(0);
    animation: modalSlideUp 0.3s ease;
}

.modal h2 {
    margin-bottom: 20px;
    color: var(--primary-dark);
}

.modal p {
    margin-bottom: 25px;
    line-height: 1.6;
}

.modal-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
}

.modal-btn {
    padding: 10px 25px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 500;
    transition: var(--transition);
}

.confirm-btn {
    background: var(--primary-color);
    color: var(--white);
}

.confirm-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
}

.cancel-btn {
    background: var(--gray-color);
    color: var(--white);
}

.cancel-btn:hover {
    background: #757575;
    transform: translateY(-2px);
}

/* Button Styles - Shared across all pages */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 500;
    transition: var(--transition);
}

.btn-primary {
    background-color: var(--primary-dark);
    color: var(--white);
}

.btn-primary:hover {
    background-color: var(--primary-color);
    transform: translateY(-2px);
}

/* Form Elements - Shared across all pages */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.form-control {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-dark);
    outline: none;
    box-shadow: 0 0 0 2px rgba(208, 164, 208, 0.3);
}

/* Table Styles - Shared across all pages */
.table-responsive {
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
    font-size: 12px;
}

th, td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

th {
    background-color: var(--primary-color);
    color: var(--white);
    font-weight: 500;
    position: sticky;
    top: 0;
}

tr:nth-child(even) {
    background-color: #f9f9f9;
}

tr:hover {
    background-color: #f5f0f5;
}

/* Status Badges - Shared across all pages */
.status-badge {
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    display: inline-block;
    min-width: 70px;
    text-align: center;
}

.status-pending {
    background-color: var(--warning-color);
    color: var(--white);
}

.status-approved {
    background-color: var(--success-color);
    color: var(--white);
}

.status-rejected {
    background-color: var(--danger-color);
    color: var(--white);
}

/* ==================== ADMIN PAGE SPECIFIC ==================== */

/* Stats Cards */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    animation: slideUp 0.5s ease;
    z-index: 1;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 100%;
    background: var(--primary-color);
}

.stat-card h3 {
    font-size: 14px;
    color: var(--gray-color);
    margin-bottom: 10px;
}

.stat-card p {
    font-size: 28px;
    font-weight: 600;
    color: var(--primary-dark);
    margin: 0;
}

/* Active Headcount Table */
.hc-table-container {
    position: relative;
    width: 300px;
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    margin-bottom: 30px;
    animation: fadeIn 0.8s ease;
    z-index: 2;
}

/* Data Sections */
.data-sections {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
}

.section-container {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--shadow);
    animation: fadeIn 0.8s ease;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.section-header h2 {
    color: var(--primary-dark);
    font-size: 20px;
    font-weight: 600;
}

.section-nav {
    display: flex;
    gap: 10px;
}

.section-nav a {
    padding: 5px 15px;
    background: #f1f1f1;
    border-radius: 20px;
    color: var(--dark-color);
    text-decoration: none;
    font-size: 12px;
    transition: var(--transition);
}

.section-nav a:hover, 
.section-nav a.active {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
}

/* Chart Container */
.chart-container {
    height: 350px;
    margin-top: 20px;
}

/* Status Table (smaller) */
.status-table-container {
    max-height: 300px;
    overflow-y: auto;
}

.status-table {
    font-size: 13px;
}

.status-table th, 
.status-table td {
    padding: 8px 12px;
}

/* ==================== LEAVES REQUEST PAGE SPECIFIC ==================== */

/* Export Form */
.export-form {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.export-form input[type="date"] {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.export-form input[type="date"]:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
}

/* Table Container */
.table-container {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 15px;
    margin-top: 20px;
}

/* Filters Container */
.filters-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 10px;
    margin-bottom: 15px;
    padding: 10px;
    background: #f9f9f9;
    border-radius: var(--border-radius);
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-group label {
    font-size: 12px;
    margin-bottom: 5px;
    color: var(--primary-dark);
    font-weight: 500;
}

.filter-group select, 
.filter-group input[type="date"] {
    padding: 6px 10px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    font-size: 12px;
    background-color: var(--white);
}

/* Compact Table */
#leavesTable {
    font-size: 12px;
    width: 100% !important;
}

#leavesTable thead th {
    padding: 8px 10px !important;
    white-space: nowrap;
}

#leavesTable tbody td {
    padding: 6px 8px !important;
    white-space: nowrap;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 5px;
}

.btn-approved, 
.btn-rejected {
    padding: 3px 8px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: var(--transition);
    font-size: 11px;
    color: var(--white);
    display: flex;
    align-items: center;
    gap: 3px;
}

.btn-approved {
    background-color: var(--success-color);
}

.btn-approved:hover {
    background-color: #3d8b40;
}

.btn-rejected {
    background-color: var(--danger-color);
}

.btn-rejected:hover {
    background-color: #d32f2f;
}

/* WFM Comment Input */
.wfm-comment {
    width: 100%;
    padding: 4px;
    font-size: 11px;
    border: 1px solid #ddd;
    border-radius: 3px;
}

/* Attachment Link */
.attachment-link {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 11px;
    display: flex;
    align-items: center;
    gap: 3px;
}

.attachment-link:hover {
    text-decoration: underline;
}

/* DataTables Customization */
.dataTables_wrapper .dataTables_filter {
    margin-bottom: 10px;
}

.dataTables_wrapper .dataTables_filter input {
    padding: 4px 8px;
    font-size: 12px;
}

/* No Data Message */
.no-data-message {
    text-align: center;
    padding: 20px;
    color: var(--gray-color);
}

/* ==================== USER PROFILE PAGE SPECIFIC ==================== */

/* Profile Container */
.profile-container {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

/* Profile Card */
.profile-card {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--shadow);
    width: 100%;
    max-width: 300px;
    text-align: center;
}

.profile-img {
    width: 90px;
    height: 90px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--primary-light);
    margin: 0 auto 15px;
    box-shadow: 0 3px 8px rgba(0,0,0,0.1);
    position: relative;
}

.profile-img.default-avatar {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f0f0f0;
    border: 1px solid #ddd;
}

.profile-img.default-avatar i {
    font-size: 2.5em;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.profile-name {
    font-size: 16px;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 4px;
}

.profile-role {
    color: var(--primary-dark);
    margin-bottom: 15px;
    font-weight: 500;
    padding: 4px 12px;
    background: rgba(208, 164, 208, 0.1);
    border-radius: 16px;
    display: inline-block;
    font-size: 12px;
}

.social-links {
    margin: 15px 0;
}

.social-links a {
    color: var(--primary-dark);
    font-size: 16px;
    margin: 0 8px;
    transition: var(--transition);
    display: inline-block;
}

.social-links a:hover {
    color: var(--accent-color);
    transform: scale(1.1);
}

.profile-info {
    text-align: left;
    margin-top: 20px;
}

.info-item {
    margin-bottom: 12px;
    display: flex;
    align-items: flex-start;
}

.info-item i {
    color: var(--primary-dark);
    margin-right: 10px;
    margin-top: 2px;
    min-width: 16px;
    text-align: center;
    font-size: 12px;
}

.info-content {
    flex: 1;
}

.info-label {
    font-weight: 500;
    color: var(--gray-color);
    font-size: 10px;
    margin-bottom: 2px;
}

.info-value {
    font-weight: 500;
    color: var(--dark-color);
    word-break: break-word;
    font-size: 12px;
}

/* Profile Content */
.profile-content {
    flex: 1;
    min-width: 250px;
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 18px;
    box-shadow: var(--shadow);
}

/* Tabs */
.profile-tabs {
    display: flex;
    border-bottom: 1px solid #eee;
    margin-bottom: 15px;
}

.tab-btn {
    padding: 8px 16px;
    cursor: pointer;
    border: none;
    background: none;
    font-weight: 500;
    color: var(--gray-color);
    transition: var(--transition);
    position: relative;
    font-size: 12px;
}

.tab-btn.active {
    color: var(--primary-dark);
}

.tab-btn.active:after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--primary-dark);
}

.tab-btn:hover:not(.active) {
    color: var(--dark-color);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Overview Section */
.welcome-title {
    font-size: 24px;
    font-weight: 600;
    color: var(--primary-dark);
    margin-bottom: 10px;
}

.profile-details {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
}

.detail-card {
    background: var(--secondary-color);
    border-radius: var(--border-radius);
    padding: 15px;
    border-left: 4px solid var(--primary-dark);
}

.detail-label {
    font-weight: 500;
    color: var(--gray-color);
    font-size: 13px;
    margin-bottom: 5px;
}

/* ==================== ARCHIVE USER PAGE SPECIFIC ==================== */

/* Search Form */
.search-container {
    margin-bottom: 20px;
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.search-form {
    position: relative;
    margin-bottom: 0;
    max-width: 350px;
    flex-grow: 1;
    display: flex;
    align-items: center;
}

.search-input-container {
    position: relative;
    flex-grow: 1;
}

.search-form input {
    width: 100%;
    padding: 8px 12px 8px 35px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    font-size: 13px;
    transition: var(--transition);
}

.search-form input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(208, 164, 208, 0.2);
}

.search-icon {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--primary-color);
    pointer-events: none;
}

.search-actions {
    display: flex;
    gap: 8px;
    margin-left: 10px;
}

.search-button, .reset-button {
    padding: 8px 15px;
    border-radius: var(--border-radius);
    font-size: 13px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    white-space: nowrap;
}

.search-button {
    background-color: var(--primary-color);
    color: white;
    border: none;
}

.search-button:hover {
    background-color: var(--primary-dark);
}

.reset-button {
    background-color: #f44336;
    color: white;
    border: none;
}

.reset-button:hover {
    background-color: #d32f2f;
}

/* Table Actions */
.archive-table-actions {
    display: flex;
    gap: 5px;
    justify-content: center;
}

.archive-action-btn {
    padding: 4px 8px;
    border-radius: 3px;
    color: white;
    text-decoration: none;
    font-size: 8px;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
}

.archive-edit-btn {
    background: var(--success-color);
}

.archive-edit-btn:hover {
    background: #3d8b40;
    transform: translateY(-2px);
}

.archive-archive-btn {
    background: var(--danger-color);
}

.archive-archive-btn:hover {
    background: #d32f2f;
    transform: translateY(-2px);
}

/* Pagination */
.archive-pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    gap: 5px;
}

.archive-pagination-link {
    padding: 5px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    color: #333;
    transition: all 0.3s ease;
}

.archive-pagination-link:hover {
    background-color: #d0a4d0;
    color: white;
}

.archive-pagination-link.active {
    background-color: #d0a4d0;
    color: white;
    border-color: #d0a4d0;
}

.archive-pagination-link.disabled {
    color: #ccc;
    pointer-events: none;
}

/* Message Alert */
.archive-alert {
    padding: 12px;
    margin-bottom: 15px;
    border-radius: var(--border-radius);
    animation: slideUp 0.5s ease;
    font-size: 13px;
}

.archive-alert-success {
    background-color: #dff0d8;
    color: #3c763d;
    border: 1px solid #d6e9c6;
}

.archive-alert-error {
    background-color: #f2dede;
    color: #a94442;
    border: 1px solid #ebccd1;
}

/* ==================== CREATE LEAVES ADMIN ==================== */
 .topbar {
            background-color: var(--white);
            height: 70px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 25px;
            position: sticky;
            top: 0;
            z-index: 90;
        }
       
        
        .page-title {
            font-family: 'Poppins', sans-serif;
            font-weight: 600;
            font-size: 1.5rem;
            color: var(--primary-dark);
            margin: 0;
        }
        
        .user-profile {
            display: flex;
            align-items: center;
        }
        
        .user-info {
            margin-right: 15px;
            text-align: right;
        }
        
        .user-name {
            font-weight: 500;
            color: var(--text-dark);
            margin-bottom: 2px;
        }
        
        .user-role {
            font-size: 0.8rem;
            color: var(--text-light);
        }
        
        .avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid var(--primary-color);
            transition: all 0.3s ease;
        }
        
        .avatar:hover {
            transform: scale(1.1);
            box-shadow: 0 0 0 3px var(--primary-light);
        }
        
       
/* Card Styles */
.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    margin-bottom: 25px;
    background-color: var(--white);
    transition: all 0.3s ease;
        margin-top: 27px; /* المسافة التي تريدها أسفل التوب بار */

}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(199, 154, 199, 0.2);
}

.card-header {
    background-color: transparent;
    border-bottom: 1px solid var(--border-color);
    padding: 18px 25px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-title {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    font-size: 1.25rem;
    color: var(--primary-dark);
    margin: 0;
}

.card-body {
    padding: 25px;
}

/* Phone Input Container */
.intl-tel-input {
    width: 100%;
}

.intl-tel-input input {
    direction: ltr;
    text-align: left;
}

.iti__selected-dial-code {
    margin-right: 10px;
    color: #333;
    font-weight: bold;
}

.iti__flag-container {
    padding: 0 5px;
}

/* ==================== ANIMATIONS & UTILITIES ==================== */

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { 
        opacity: 0;
        transform: translateY(20px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes modalSlideUp {
    from { 
        opacity: 0;
        transform: translateY(50px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in {
    animation: fadeInUp 0.5s ease-out forwards;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

.rounded-lg {
    border-radius: 12px;
}

.shadow-sm {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.hover-scale {
    transition: transform 0.3s ease;
}

.hover-scale:hover {
    transform: scale(1.02);
}

/* Datepicker Customization */
.flatpickr-day.selected {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* ==================== RESPONSIVE DESIGN ==================== */

@media (max-width: 1200px) {
    .stats-container {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .hc-table-container {
        width: 100%;
    }
}

@media (max-width: 992px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .sidebar.collapsed {
        width: 250px;
        transform: translateX(-100%);
    }
    
    .sidebar.collapsed.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .profile-container {
        flex-direction: column;
    }
    
    .profile-card {
        max-width: 100%;
    }
    
    .mobile-menu-btn {
        display: block;
        position: fixed;
        top: 20px;
        left: 20px;
        z-index: 1001;
        background: var(--primary-color);
        color: white;
        border: none;
        padding: 10px;
        border-radius: 50%;
        width: 50px;
        height: 50px;
        cursor: pointer;
        box-shadow: var(--shadow);
    }
}

@media (max-width: 768px) {
    .stats-container {
        grid-template-columns: 1fr;
    }
    
    .page-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .welcome-section {
        flex-direction: column;
        align-items: flex-start;
        margin-bottom: 15px;
    }
    
    .profile-pic {
        margin-left: 0;
        margin-top: 10px;
        margin-right: 0;
    }
    
    .section-nav,
    .profile-tabs {
        flex-wrap: wrap;
    }
    
    .export-form {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .filters-container {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
    
    .dataTables_wrapper .dataTables_filter {
        float: none;
        text-align: left;
        margin-bottom: 10px;
    }
    
    .action-buttons,
    .archive-action-buttons {
        width: 100%;
        flex-direction: column;
        gap: 8px;
    }
    
    .btn,
    .archive-action-btn {
        flex: 1;
        min-width: 120px;
        text-align: center;
    }
    
    .profile-card,
    .profile-content {
        padding: 15px;
    }
}

@media (max-width: 576px) {
    .filters-container {
        grid-template-columns: 1fr;
    }
    
    .modal-buttons {
        flex-direction: column;
    }
    
    .modal-btn {
        width: 100%;
    }
    
    .pagination-container {
        flex-wrap: wrap;
    }
}

@media (max-width: 480px) {
    .profile-details {
        grid-template-columns: 1fr;
    }
    
    .profile-tabs {
        flex-direction: column;
    }
    
    .tab-btn {
        text-align: left;
        padding: 8px;
        font-size: 11px;
    }
}

/* ==================== users page ==================== */
 
     

        /* Action Buttons */
        .action-buttons {
            display: flex;
            gap: 8px;
            margin-bottom: 10px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 8px 12px;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-weight: 500;
            transition: var(--transition);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            font-size: 13px;
        }

        .btn-primary {
            background: var(--primary-color);
            color: var(--white);
        }

        .btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .btn-secondary {
            background: var(--gray-color);
            color: var(--white);
        }

        .btn-secondary:hover {
            background: #757575;
            transform: translateY(-2px);
        }

        .btn i {
            margin-right: 5px;
            font-size: 14px;
        }

        /* Search Form */
        .search-form {
            position: relative;
            margin-bottom: 15px;
            max-width: 350px;
        }

        .search-form input {
            width: 100%;
            padding: 8px 12px 8px 35px;
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            font-size: 13px;
            transition: var(--transition);
        }

        .search-form input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(208, 164, 208, 0.2);
        }

        .search-form button {
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            padding: 0 10px;
            background: transparent;
            border: none;
            color: var(--primary-color);
            cursor: pointer;
        }

        /* Table Container */
        .table-container {
            background: var(--white);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            overflow: hidden;
            margin-bottom: 20px;
            animation: fadeIn 0.8s ease;
        }

        .table-responsive {
            overflow-x: auto;
            max-width: 100%;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 11px;
            min-width: 900px;
        }

        th, td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
            transition: var(--transition);
        }

        th {
            background-color: var(--primary-color);
            color: var(--white);
            font-weight: 500;
            position: sticky;
            top: 0;
        }

        tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        tr:hover {
            background-color: #f5f0f5;
        }

        .table-actions {
            display: flex;
            gap: 5px;
            justify-content: center;
        }

        .action-btn {
            padding: 4px 8px;
            border-radius: 3px;
            color: white;
            text-decoration: none;
            font-size: 11px;
            transition: var(--transition);
            display: inline-flex;
            align-items: center;
        }

        .edit-btn {
            background: var(--success-color);
        }

        .edit-btn:hover {
            background: #3d8b40;
            transform: translateY(-2px);
        }

        .archive-btn {
            background: var(--danger-color);
        }

        .archive-btn:hover {
            background: #d32f2f;
            transform: translateY(-2px);
        }

        /* Pagination */
        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 15px;
            gap: 4px;
        }

        .pagination a, .pagination span {
            padding: 6px 10px;
            border-radius: var(--border-radius);
            text-decoration: none;
            color: var(--dark-color);
            border: 1px solid #ddd;
            transition: var(--transition);
            font-size: 12px;
        }

        .pagination a:hover {
            background: var(--primary-light);
            color: var(--white);
            border-color: var(--primary-light);
        }

        .pagination .active {
            background: var(--primary-color);
            color: var(--white);
            border-color: var(--primary-color);
        }

        
        .confirm-btn {
            background: var(--primary-color);
            color: var(--white);
        }

        .confirm-btn:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }

        .cancel-btn {
            background: var(--gray-color);
            color: var(--white);
        }

        .cancel-btn:hover {
            background: #757575;
            transform: translateY(-2px);
        }

        /* Message Alert */
        .alert {
            padding: 12px;
            margin-bottom: 15px;
            border-radius: var(--border-radius);
            animation: slideUp 0.5s ease;
            font-size: 13px;
        }

        .alert-success {
            background-color: #dff0d8;
            color: #3c763d;
            border: 1px solid #d6e9c6;
        }

        .alert-error {
            background-color: #f2dede;
            color: #a94442;
            border: 1px solid #ebccd1;
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUp {
            from { 
                opacity: 0;
                transform: translateY(20px);
            }
            to { 
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes modalSlideUp {
            from { 
                opacity: 0;
                transform: translateY(50px);
            }
            to { 
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Mobile Menu Button */
        .mobile-menu-btn {
            display: none;
            position: fixed;
            top: 15px;
            left: 15px;
            z-index: 1001;
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 8px;
            border-radius: 50%;
            width: 45px;
            height: 45px;
            cursor: pointer;
            box-shadow: var(--shadow);
        }

        /* Responsive Design */
        @media (max-width: 992px) {
            .sidebar {
                transform: translateX(-100%);
                z-index: 1100;
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
                padding: 20px;
            }
            
            .mobile-menu-btn {
                display: block;
            }
        }

        @media (max-width: 768px) {
            .page-header {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .welcome-section {
                margin-bottom: 15px;
            }
            
            .profile-pic {
                margin-left: 12px;
                width: 50px;
                height: 50px;
            }
            
            .welcome-message h1 {
                font-size: 1.5rem;
            }
            
            .main-content {
                padding: 15px;
            }
        }

        @media (max-width: 576px) {
            .modal-content {
                padding: 20px;
                margin: 0 15px;
            }
            
            .modal-buttons {
                flex-direction: column;
                gap: 8px;
            }
            
            .modal-btn {
                width: 100%;
            }
            
            table.dataTable thead th,
            table.dataTable tbody td {
                padding: 8px 10px;
            }
        }
        /* ==================== My leaves admin page==================== */
        
        /* Button Styles */
        .create-leaves-btn {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: var(--border-radius);
            font-weight: 500;
            transition: var(--transition);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            box-shadow: var(--shadow);
            font-size: 0.9rem;
        }

        .create-leaves-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 7px 20px rgba(208, 164, 208, 0.4);
            color: white;
        }

        /* Table Styles */
        .data-card {
            background: var(--white);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            transition: var(--transition);
            margin-bottom: 25px;
            overflow: hidden;
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: var(--white);
            padding: 12px 20px;
            border-bottom: none;
        }

        .card-header h5 {
            font-weight: 600;
            margin: 0;
            font-size: 1.1rem;
        }

        .table-responsive {
            border-radius: var(--border-radius);
            overflow: hidden;
        }

        table.dataTable {
            width: 100% !important;
            margin: 0 !important;
            border-collapse: collapse !important;
        }

        table.dataTable thead th {
            background-color: var(--primary-color);
            color: var(--white);
            border-bottom: none;
            font-weight: 500;
            padding: 10px 12px;
            font-size: 0.85rem;
        }

        table.dataTable tbody td {
            padding: 10px 12px;
            vertical-align: middle;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            font-size: 0.82rem;
        }

        table.dataTable tbody tr:nth-child(even) {
            background-color: rgba(208, 164, 208, 0.05);
        }

        table.dataTable tbody tr:hover {
            background-color: rgba(208, 164, 208, 0.1);
        }

        /* Badge Styles */
        .badge {
            padding: 5px 8px;
            font-weight: 500;
            border-radius: 20px;
            font-size: 0.75rem;
            text-transform: capitalize;
        }

        .badge-pending {
            background-color: #fff3cd;
            color: #856404;
        }

        .badge-approved {
            background-color: #d4edda;
            color: #155724;
        }

        .badge-rejected {
            background-color: #f8d7da;
            color: #721c24;
        }

        /* Attachment Link */
        .attachment-link {
            color: var(--primary-dark);
            text-decoration: none;
            transition: var(--transition);
            font-size: 0.85rem;
        }

        .attachment-link:hover {
            color: var(--accent-color);
            text-decoration: underline;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 2000;
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background: var(--white);
            border-radius: var(--border-radius);
            padding: 25px;
            width: 100%;
            max-width: 400px;
            text-align: center;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        .modal h2 {
            margin-bottom: 15px;
            color: var(--primary-dark);
            font-weight: 600;
            font-size: 1.3rem;
        }

        .modal p {
            margin-bottom: 20px;
            color: var(--gray-color);
            line-height: 1.5;
            font-size: 0.95rem;
        }

        .modal-buttons {
            display: flex;
            justify-content: center;
            gap: 12px;
        }

        .modal-btn {
            padding: 8px 20px;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-weight: 500;
            transition: var(--transition);
            min-width: 90px;
            font-size: 0.9rem;
        }

        .confirm-btn {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: var(--white);
        }

        .confirm-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(208, 164, 208, 0.3);
        }

        .cancel-btn {
            background-color: var(--gray-color);
            color: var(--white);
        }

        .cancel-btn:hover {
            background-color: #757575;
            transform: translateY(-2px);
        }

        /* Mobile Menu Button */
        .mobile-menu-btn {
            display: none;
            position: fixed;
            top: 15px;
            left: 15px;
            z-index: 1001;
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 10px;
            border-radius: 50%;
            width: 45px;
            height: 45px;
            cursor: pointer;
            box-shadow: var(--shadow);
        }

        /* Responsive Design */
        @media (max-width: 992px) {
            .sidebar {
                transform: translateX(-100%);
                z-index: 1100;
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
                padding: 20px;
            }
            
            .mobile-menu-btn {
                display: block;
            }
        }

        @media (max-width: 768px) {
            .page-header {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .welcome-section {
                margin-bottom: 15px;
            }
            
            .profile-pic {
                margin-left: 12px;
                width: 50px;
                height: 50px;
            }
            
            .welcome-message h1 {
                font-size: 1.5rem;
            }
            
            .main-content {
                padding: 15px;
            }
        }

        @media (max-width: 576px) {
            .modal-content {
                padding: 20px;
                margin: 0 15px;
            }
            
            .modal-buttons {
                flex-direction: column;
                gap: 8px;
            }
            
            .modal-btn {
                width: 100%;
            }
            
            table.dataTable thead th,
            table.dataTable tbody td {
                padding: 8px 10px;
            }
        }
        /* ==================== AuxTransactions ==================== */
         

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            background-color: var(--light-color);
            color: var(--dark-color);
            min-height: 100vh;
            transition: var(--transition);
        }

        .container {
            width: 95%;
            max-width: 1400px;
            margin: 30px auto;
            background-color: var(--white);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 30px;
            animation: fadeIn 0.5s ease;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .page-header h1 {
            color: var(--primary-dark);
            font-size: 28px;
            font-weight: 600;
        }

        .filter-section {
            background-color: #f9f0f9;
            padding: 20px;
            border-radius: var(--border-radius);
            margin-bottom: 30px;
            box-shadow: var(--shadow);
        }

        .filter-form {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }

        .form-group {
            margin-bottom: 0;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--primary-dark);
            font-size: 14px;
        }

        .form-control {
            width: 100%;
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            font-size: 14px;
            transition: var(--transition);
        }

        .form-control:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 3px rgba(199, 154, 199, 0.2);
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: var(--border-radius);
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: var(--white);
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        .btn-export {
            background-color: var(--accent-color);
            color: var(--white);
        }

        .btn-export:hover {
            background-color: #7b1fa2;
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        .table-container {
            margin-top: 30px;
            overflow-x: auto;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        th {
            background-color: var(--primary-color);
            color: var(--white);
            font-weight: 500;
            position: sticky;
            top: 0;
        }

        tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        tr:hover {
            background-color: #f5f0f5;
        }

        .no-data {
            text-align: center;
            padding: 30px;
            color: var(--gray-color);
            font-size: 16px;
        }

        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 30px;
            gap: 5px;
        }

        .page-link {
            padding: 8px 15px;
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            color: var(--primary-dark);
            text-decoration: none;
            transition: var(--transition);
        }

        .page-link:hover {
            background-color: var(--primary-light);
            color: var(--white);
        }

        .page-link.active {
            background-color: var(--primary-color);
            color: var(--white);
            border-color: var(--primary-color);
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @media (max-width: 768px) {
            .filter-form {
                grid-template-columns: 1fr;
            }
            
            .page-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }
            
            .btn {
                width: 100%;
            }
        }
        /* ====================  ==================== */
        
/* Default Avatar Styles */
.default-avatar {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(145deg, #f0f0f0, #ffffff);
    border-radius: 50%;
    color: var(--primary-dark);
    width: 100%;
    height: 100%;
    min-width: 45px;
    min-height: 45px;
    border: 2px solid var(--primary-light);
    overflow: hidden;
    position: relative;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.default-avatar:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(208, 164, 208, 0.2);
    border-color: var(--primary-color);
}

.default-avatar i {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.4em;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-dark);
    opacity: 0.9;
    transition: all 0.3s ease;
}

.default-avatar:hover i {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.1);
}

/* Profile Image in Header */
.welcome-section .profile-pic {
    width: 75px;
    height: 75px;
    margin-right: 20px;
    position: relative;
    transition: all 0.3s ease;
}

.welcome-section .default-avatar {
    width: 75px;
    height: 75px;
    background: linear-gradient(145deg, #f8f8f8, #ffffff);
    border: 3px solid var(--primary-light);
}

.welcome-section .default-avatar i {
    font-size: 2.2em;
    color: var(--primary-dark);
}

.welcome-section .default-avatar:hover {
    border-color: var(--primary-color);
    box-shadow: 0 8px 25px rgba(208, 164, 208, 0.25);
}

/* Profile Image in Profile Page */
.profile-img {
    width: 180px;
    height: 180px;
    border-radius: 50%;
    object-fit: cover;
    border: 6px solid var(--primary-light);
    margin: 0 auto 25px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    position: relative;
    transition: all 0.3s ease;
}

.profile-img.default-avatar {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(145deg, #f8f8f8, #ffffff);
    border: 6px solid var(--primary-light);
}

.profile-img.default-avatar i {
    font-size: 4em;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--primary-dark);
    opacity: 0.9;
    transition: all 0.3s ease;
}

.profile-img.default-avatar:hover {
    transform: scale(1.05);
    border-color: var(--primary-color);
    box-shadow: 0 12px 30px rgba(208, 164, 208, 0.3);
}

.profile-img.default-avatar:hover i {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.1);
}

/* Regular Profile Picture Styles */
.profile-pic {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--primary-light);
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.profile-pic:hover {
    transform: scale(1.05);
    border-color: var(--primary-color);
    box-shadow: 0 6px 20px rgba(208, 164, 208, 0.2);
}
        