/* @import url('root-variables.css'); - تم تعطيله لتجنب مشاكل المسارات */

/* ==================== BASE STYLES ==================== */
    /* ==================== AuxTransactions ==================== */
         

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            background-color: var(--light-color);
            color: var(--dark-color);
            min-height: 100vh;
            transition: var(--transition);
        }

        .container {
            width: 95%;
            max-width: 1400px;
            margin: 30px auto;
            background-color: var(--white);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 30px;
            animation: fadeIn 0.5s ease;
        }

        /* إضافة تنسيق جديد للوجو */
        .logo-container {
            display: flex;
            justify-content: flex-end; /* محاذاة إلى اليمين */
            margin-bottom: 15px;
        }

        .logo-container img {
            max-height: 50px; /* تصغير حجم اللوجو */
            width: auto;
        }

        /* تعديل page-header لدعم اللوجو على اليمين */
        .page-header {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .page-header h1 {
            color: var(--primary-dark);
            font-size: 28px;
            font-weight: 600;
        }

        .filter-section {
            background-color: #f9f0f9;
            padding: 20px;
            border-radius: var(--border-radius);
            margin-bottom: 30px;
            box-shadow: var(--shadow);
        }

        .filter-form {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }

        .form-group {
            margin-bottom: 0;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--primary-dark);
            font-size: 14px;
        }

        .form-control {
            width: 100%;
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            font-size: 14px;
            transition: var(--transition);
        }

        .form-control:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 3px rgba(199, 154, 199, 0.2);
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: var(--border-radius);
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: var(--white);
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        .btn-export {
            background-color: var(--accent-color);
            color: var(--white);
        }

        .btn-export:hover {
            background-color: #7b1fa2;
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        .table-container {
            margin-top: 30px;
            overflow-x: auto;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        th {
            background-color: var(--primary-color);
            color: var(--white);
            font-weight: 500;
            position: sticky;
            top: 0;
        }

        tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        tr:hover {
            background-color: #f5f0f5;
        }

        .no-data {
            text-align: center;
            padding: 30px;
            color: var(--gray-color);
            font-size: 16px;
        }

        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 30px;
            gap: 5px;
        }

        .page-link {
            padding: 8px 15px;
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            color: var(--primary-dark);
            text-decoration: none;
            transition: var(--transition);
        }

        .page-link:hover {
            background-color: var(--primary-light);
            color: var(--white);
        }

        .page-link.active {
            background-color: var(--primary-color);
            color: var(--white);
            border-color: var(--primary-color);
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @media (max-width: 768px) {
            .filter-form {
                grid-template-columns: 1fr;
            }
            
            .page-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }
            
            .btn {
                width: 100%;
            }
        }

        /* تعديل تنسيق الشعار (اللوجو) */
        .sidebar-header {
            position: absolute;
            top: 15px; /* تم تغييرها من 30px إلى 15px لرفع اللوجو للأعلى */
            right: 30px;
        }

        .sidebar-header img {
            max-height: 30px; /* حجم اللوجو */
            width: auto;
        }

        /* تأكد من أن page-header يأخذ العرض الكامل مع مساحة للوجو */
        .page-header {
            position: relative;
            width: 100%;
        }
