@import url('root-variables.css');

/* ==================== All insert and delete and update schedule and breaks ==================== */
/* ==================== ROOT VARIABLES & BASE STYLES ==================== */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            background-color: var(--light-color);
            color: var(--dark-color);
            display: flex;
            min-height: 100vh;
            font-size: 0.9rem;
        }

        .main-container {
            width: 100%;
            max-width: 1400px;
            margin: 1.5rem auto;
            padding: 0 1.25rem;
        }

        .card {
            background: var(--white);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            border: none;
            overflow: hidden;
            margin-bottom: 1.5rem;
            transition: var(--transition);
        }

        .card:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: var(--white);
            padding: 1.25rem;
            border-bottom: none;
        }

        .card-title {
            font-size: 1.1rem;
            font-weight: 500;
            margin: 0;
            display: flex;
            align-items: center;
        }

        .card-title i {
            margin-right: 0.5rem;
            font-size: 1rem;
        }

        .card-body {
            padding: 1.5rem;
        }

        .btn {
            border-radius: var(--border-radius);
            padding: 0.5rem 1rem;
            font-weight: 500;
            transition: var(--transition);
            border: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 0.85rem;
        }

        .btn-primary {
            background: var(--primary-color);
            color: var(--white);
        }

        .btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }

        .template-btn {
            background: var(--white);
            color: var(--primary-color);
            border: 1px solid var(--primary-color);
            padding: 0.45rem 0.9rem;
            font-size: 0.8rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .template-btn:hover {
            background: var(--primary-color);
            color: var(--white);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .drag-container {
            border: 2px dashed var(--border-color);
            border-radius: var(--border-radius);
            padding: 2rem;
            text-align: center;
            transition: var(--transition);
            margin-bottom: 1.5rem;
            background: var(--secondary-color);
            cursor: pointer;
        }

        .drag-container.active {
            border-color: var(--primary-color);
            background-color: rgba(199, 154, 199, 0.1);
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% { border-color: var(--border-color); }
            50% { border-color: var(--primary-color); }
            100% { border-color: var(--border-color); }
        }

        .drag-container:hover {
            border-color: var(--primary-color);
        }

        .upload-icon {
            font-size: 2rem;
            color: var(--primary-color);
            margin-bottom: 0.75rem;
            transition: var(--transition);
        }

        .drag-container:hover .upload-icon {
            transform: scale(1.1);
        }

        .file-input {
            display: none;
        }

        .file-info {
            margin-top: 1rem;
            font-size: 0.85rem;
        }

        .file-info .alert {
            display: inline-flex;
            align-items: center;
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius);
            font-size: 0.85rem;
        }

        .table-container {
            overflow-x: auto;
            margin-top: 1.5rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: var(--white);
            font-size: 0.85rem;
        }

        th {
            background: var(--primary-light);
            color: var(--text-dark);
            padding: 0.75rem;
            text-align: left;
            font-weight: 500;
            font-size: 0.8rem;
        }

        td {
            padding: 0.75rem;
            border-bottom: 1px solid var(--border-color);
            color: var(--text-medium);
        }

        tr:hover td {
            background: var(--secondary-color);
        }

        .alert {
            border-radius: var(--border-radius);
            padding: 0.75rem 1rem;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            font-size: 0.85rem;
        }

        .alert i {
            margin-right: 0.5rem;
            font-size: 1rem;
        }

        .section-title {
            color: #000;
            font-size: 1.1rem;
            font-weight: 500;
            margin: 1.5rem 0 0.75rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid var(--primary-light);
        }

        @media (max-width: 768px) {
            .card-body {
                padding: 1.25rem;
            }
            
            .drag-container {
                padding: 1.5rem 0.75rem;
            }
            
            .card-title {
                font-size: 1rem;
            }
            
            .btn {
                padding: 0.5rem 0.75rem;
            }
        }