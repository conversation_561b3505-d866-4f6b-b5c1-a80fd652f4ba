<?php
/**
 * Secure Error Handler - Enhanced Security Version
 * معالج الأخطاء الآمن - نسخة محسنة للأمان
 * 
 * @version 2.0
 * <AUTHOR> CX Security Team
 */

// منع الوصول المباشر
if (!defined('SECURE_ERROR_HANDLER_LOADED')) {
    define('SECURE_ERROR_HANDLER_LOADED', true);
} else {
    http_response_code(403);
    exit('Direct access not allowed');
}

// تحديد البيئة والثوابت الأمنية
define('IS_PRODUCTION', getenv('ENVIRONMENT') !== 'development');
define('ERROR_LOG_PATH', __DIR__ . '/logs/');
define('MAX_LOG_SIZE', 10 * 1024 * 1024); // 10MB
define('MAX_LOG_FILES', 5);
define('SENSITIVE_PATTERNS', [
    '/password[=:]\s*[^\s]+/i',
    '/mysql[^:]*:[^@]*@[^\/]+/i',
    '/session[_\s]*id[=:]\s*[^\s]+/i',
    '/api[_\s]*key[=:]\s*[^\s]+/i',
    '/token[=:]\s*[^\s]+/i',
    '/secret[=:]\s*[^\s]+/i'
]);

/**
 * إنشاء مجلد اللوجات الآمن
 */
function ensureSecureLogDirectory() {
    if (!is_dir(ERROR_LOG_PATH)) {
        mkdir(ERROR_LOG_PATH, 0750, true);
        
        // إنشاء ملف .htaccess لحماية اللوجات
        $htaccess_content = "# حماية ملفات اللوجات\n";
        $htaccess_content .= "Order Deny,Allow\n";
        $htaccess_content .= "Deny from all\n";
        $htaccess_content .= "<Files ~ \"\\.(log|txt)$\">\n";
        $htaccess_content .= "    Order Deny,Allow\n";
        $htaccess_content .= "    Deny from all\n";
        $htaccess_content .= "</Files>\n";
        file_put_contents(ERROR_LOG_PATH . '.htaccess', $htaccess_content);
        
        // إنشاء ملف index.php فارغ
        file_put_contents(ERROR_LOG_PATH . 'index.php', '<?php http_response_code(403); exit("Access denied"); ?>');
    }
}

/**
 * تدوير ملفات اللوجات لمنع نموها بشكل مفرط
 */
function rotateLogFile($logFile) {
    if (file_exists($logFile) && filesize($logFile) > MAX_LOG_SIZE) {
        // حذف أقدم ملف
        $oldestFile = $logFile . '.' . MAX_LOG_FILES;
        if (file_exists($oldestFile)) {
            unlink($oldestFile);
        }
        
        // تحريك الملفات
        for ($i = MAX_LOG_FILES - 1; $i > 0; $i--) {
            $oldFile = $logFile . '.' . $i;
            $newFile = $logFile . '.' . ($i + 1);
            if (file_exists($oldFile)) {
                rename($oldFile, $newFile);
            }
        }
        rename($logFile, $logFile . '.1');
    }
}

/**
 * تنظيف رسائل الأخطاء من المعلومات الحساسة
 */
function sanitizeErrorMessage($message) {
    // إزالة مسارات النظام الحساسة في الإنتاج
    if (IS_PRODUCTION) {
        $message = preg_replace('/\/[a-zA-Z0-9_\-\/]+\//', '/[PATH]/', $message);
    }
    
    // إزالة المعلومات الحساسة
    foreach (SENSITIVE_PATTERNS as $pattern) {
        $message = preg_replace($pattern, '[SENSITIVE_DATA_HIDDEN]', $message);
    }
    
    // تحديد طول الرسالة
    if (strlen($message) > 1000) {
        $message = substr($message, 0, 1000) . '... [TRUNCATED]';
    }
    
    return $message;
}

/**
 * تسجيل آمن ومفصل للأخطاء
 */
function secureErrorLog($message, $type = 'error', $context = []) {
    ensureSecureLogDirectory();
    
    $logFile = ERROR_LOG_PATH . $type . '.log';
    rotateLogFile($logFile);
    
    $timestamp = date('Y-m-d H:i:s');
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $user = $_SESSION['username'] ?? 'anonymous';
    $userAgent = substr($_SERVER['HTTP_USER_AGENT'] ?? 'unknown', 0, 200);
    $requestUri = $_SERVER['REQUEST_URI'] ?? 'unknown';
    $method = $_SERVER['REQUEST_METHOD'] ?? 'unknown';
    
    // تنظيف الرسالة من المعلومات الحساسة
    $cleanMessage = sanitizeErrorMessage($message);
    
    // إضافة معلومات السياق
    $contextStr = '';
    if (!empty($context)) {
        $contextStr = ' | Context: ' . json_encode($context);
    }
    
    $logEntry = "[{$timestamp}] [{$type}] IP: {$ip} | User: {$user} | Method: {$method} | URI: {$requestUri} | Message: {$cleanMessage} | UA: {$userAgent}{$contextStr}" . PHP_EOL;
    
    // كتابة اللوج بشكل آمن
    if (file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX) === false) {
        // إذا فشل الكتابة، استخدم error_log العادي
        error_log("Failed to write to secure log: " . $cleanMessage);
    }
}

/**
 * تسجيل الأخطاء في قاعدة البيانات بشكل آمن
 */
function logErrorToDatabase($errorType, $message, $file, $line, $severity = 'error') {
    try {
        // تضمين اتصال قاعدة البيانات
        include_once $_SERVER['DOCUMENT_ROOT'] . '/config/Databasehost.php';
        
        if (!isset($conn) || $conn === null) {
            secureErrorLog("Database connection not available for error logging", 'warning');
            return false;
        }

        // التحقق من وجود جدول error_logs
        $check_table = $conn->query("SHOW TABLES LIKE 'error_logs'");
        if ($check_table->num_rows == 0) {
            $create_table = "CREATE TABLE IF NOT EXISTS error_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                error_type VARCHAR(100) NOT NULL,
                message TEXT NOT NULL,
                file_name VARCHAR(255),
                line_number INT,
                severity ENUM('critical', 'error', 'warning', 'notice') DEFAULT 'error',
                user_email VARCHAR(255),
                ip_address VARCHAR(45),
                user_agent TEXT,
                request_uri VARCHAR(500),
                request_method VARCHAR(10),
                session_data JSON,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_severity (severity),
                INDEX idx_created_at (created_at),
                INDEX idx_user_email (user_email)
            ) ENGINE=InnoDB";
            $conn->query($create_table);
        }

        // تحضير البيانات للإدراج
        $cleanMessage = sanitizeErrorMessage($message);
        $fileName = IS_PRODUCTION ? basename($file) : $file;
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $userAgent = substr($_SERVER['HTTP_USER_AGENT'] ?? 'unknown', 0, 500);
        $requestUri = substr($_SERVER['REQUEST_URI'] ?? 'unknown', 0, 500);
        $method = $_SERVER['REQUEST_METHOD'] ?? 'unknown';
        $user = $_SESSION['username'] ?? 'anonymous';
        
        // جمع بيانات الجلسة الآمنة
        $sessionData = [];
        if (isset($_SESSION)) {
            $sessionData = [
                'access_level' => $_SESSION['access_level'] ?? 'none',
                'last_activity' => $_SESSION['last_activity'] ?? null,
                'session_started' => isset($_SESSION['username']) ? 'yes' : 'no'
            ];
        }
        $sessionJson = json_encode($sessionData);

        $sql = "INSERT INTO error_logs (
                    error_type, message, file_name, line_number, severity,
                    user_email, ip_address, user_agent, request_uri, request_method,
                    session_data
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $conn->prepare($sql);
        if ($stmt) {
            $stmt->bind_param("sssississss", 
                $errorType, $cleanMessage, $fileName, $line, $severity,
                $user, $ip, $userAgent, $requestUri, $method, $sessionJson
            );
            
            $result = $stmt->execute();
            $stmt->close();
            
            if (!$result) {
                secureErrorLog("Failed to insert error into database: " . $conn->error, 'warning');
            }
            
            return $result;
        } else {
            secureErrorLog("Failed to prepare error insert statement: " . $conn->error, 'warning');
            return false;
        }
        
    } catch (Exception $e) {
        secureErrorLog("Exception in logErrorToDatabase: " . $e->getMessage(), 'critical');
        return false;
    }
}

/**
 * تحديد مستوى الخطورة بناءً على نوع الخطأ
 */
function getSeverityLevel($errno) {
    $critical_errors = [E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR, E_USER_ERROR, E_RECOVERABLE_ERROR];
    $warning_errors = [E_WARNING, E_CORE_WARNING, E_COMPILE_WARNING, E_USER_WARNING];
    $notice_errors = [E_NOTICE, E_USER_NOTICE, E_STRICT, E_DEPRECATED, E_USER_DEPRECATED];
    
    if (in_array($errno, $critical_errors)) {
        return 'critical';
    } elseif (in_array($errno, $warning_errors)) {
        return 'warning';
    } elseif (in_array($errno, $notice_errors)) {
        return 'notice';
    } else {
        return 'error';
    }
}

/**
 * معالج الأخطاء المخصص - نسخة آمنة ومحسنة
 */
function secureErrorHandler($errno, $errstr, $errfile, $errline) {
    // تجاهل الأخطاء المكبوتة بـ @
    if (!(error_reporting() & $errno)) {
        return false;
    }

    $error_types = [
        E_ERROR => 'Fatal Error',
        E_WARNING => 'Warning',
        E_PARSE => 'Parse Error',
        E_NOTICE => 'Notice',
        E_CORE_ERROR => 'Core Error',
        E_CORE_WARNING => 'Core Warning',
        E_COMPILE_ERROR => 'Compile Error',
        E_COMPILE_WARNING => 'Compile Warning',
        E_USER_ERROR => 'User Error',
        E_USER_WARNING => 'User Warning',
        E_USER_NOTICE => 'User Notice',
        E_STRICT => 'Strict Notice',
        E_RECOVERABLE_ERROR => 'Recoverable Error',
        E_DEPRECATED => 'Deprecated',
        E_USER_DEPRECATED => 'User Deprecated'
    ];

    $error_type = $error_types[$errno] ?? 'Unknown Error';
    $severity = getSeverityLevel($errno);
    
    // إنشاء رسالة الخطأ الآمنة
    $safe_file = IS_PRODUCTION ? basename($errfile) : $errfile;
    $log_message = "[$error_type] $errstr in $safe_file on line $errline";
    
    // تسجيل الخطأ في الملفات
    secureErrorLog($log_message, $severity, [
        'errno' => $errno,
        'file' => $safe_file,
        'line' => $errline
    ]);
    
    // تسجيل الأخطاء الخطيرة في قاعدة البيانات
    if (in_array($errno, [E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR, E_USER_ERROR, E_RECOVERABLE_ERROR])) {
        logErrorToDatabase($error_type, $errstr, $errfile, $errline, $severity);
        
        // إعادة التوجيه للصفحة الآمنة
        if (!headers_sent()) {
            header("Location: /App/error_handlers/error_handler.php?error=500");
            exit();
        } else {
            // إذا تم إرسال headers، استخدم JavaScript
            echo "<script>window.location.href='/App/error_handlers/error_handler.php?error=500';</script>";
            exit();
        }
    }

    return true;
}

/**
 * معالج الاستثناءات المخصص - نسخة آمنة
 */
function secureExceptionHandler($exception) {
    $message = $exception->getMessage();
    $file = $exception->getFile();
    $line = $exception->getLine();
    $trace = $exception->getTraceAsString();

    // تنظيف المعلومات الحساسة
    $cleanMessage = sanitizeErrorMessage($message);
    $safe_file = IS_PRODUCTION ? basename($file) : $file;
    $cleanTrace = IS_PRODUCTION ? '[TRACE_HIDDEN_IN_PRODUCTION]' : sanitizeErrorMessage($trace);

    // تسجيل الاستثناء
    $log_message = "Uncaught Exception: $cleanMessage in $safe_file on line $line";
    secureErrorLog($log_message, 'critical', [
        'exception_class' => get_class($exception),
        'trace' => $cleanTrace
    ]);

    // تسجيل في قاعدة البيانات
    logErrorToDatabase('Uncaught Exception', $cleanMessage, $file, $line, 'critical');

    // إعادة التوجيه الآمنة
    if (!headers_sent()) {
        header("Location: /App/error_handlers/error_handler.php?error=500");
        exit();
    } else {
        echo "<script>window.location.href='/App/error_handlers/error_handler.php?error=500';</script>";
        echo "<noscript><meta http-equiv='refresh' content='0;url=/App/error_handlers/error_handler.php?error=500'></noscript>";
        exit();
    }
}

/**
 * معالج الأخطاء القاتلة - نسخة آمنة
 */
function secureFatalErrorHandler() {
    $error = error_get_last();

    if ($error && in_array($error['type'], [E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR, E_USER_ERROR])) {
        $message = $error['message'];
        $file = $error['file'];
        $line = $error['line'];

        // تنظيف المعلومات الحساسة
        $cleanMessage = sanitizeErrorMessage($message);
        $safe_file = IS_PRODUCTION ? basename($file) : $file;

        // تسجيل الخطأ القاتل
        $log_message = "Fatal Error: $cleanMessage in $safe_file on line $line";
        secureErrorLog($log_message, 'critical', [
            'error_type' => $error['type'],
            'is_fatal' => true
        ]);

        // تسجيل في قاعدة البيانات
        logErrorToDatabase('Fatal Error', $cleanMessage, $file, $line, 'critical');

        // محاولة إعادة التوجيه
        if (!headers_sent()) {
            header("Location: /App/error_handlers/error_handler.php?error=500");
            exit();
        }
    }
}

/**
 * معالج أخطاء قاعدة البيانات
 */
function handleDatabaseError($conn, $operation = 'unknown') {
    if ($conn && $conn->error) {
        $error_message = "Database Error in $operation: " . $conn->error;
        secureErrorLog($error_message, 'error', [
            'operation' => $operation,
            'errno' => $conn->errno
        ]);

        // في الإنتاج، لا نكشف تفاصيل قاعدة البيانات
        if (IS_PRODUCTION) {
            throw new Exception("Database operation failed. Please try again later.");
        } else {
            throw new Exception($error_message);
        }
    }
}

/**
 * معالج أخطاء الملفات
 */
function handleFileError($operation, $filename, $error_message = '') {
    $log_message = "File Error in $operation: $filename";
    if ($error_message) {
        $log_message .= " - $error_message";
    }

    secureErrorLog($log_message, 'warning', [
        'operation' => $operation,
        'filename' => basename($filename)
    ]);

    if (IS_PRODUCTION) {
        throw new Exception("File operation failed. Please try again later.");
    } else {
        throw new Exception($log_message);
    }
}

/**
 * معالج أخطاء التحقق من الصحة
 */
function handleValidationError($field, $value, $rule) {
    $log_message = "Validation Error: Field '$field' failed rule '$rule'";
    secureErrorLog($log_message, 'notice', [
        'field' => $field,
        'rule' => $rule,
        'value_length' => strlen($value)
    ]);

    throw new InvalidArgumentException("Invalid value for field: $field");
}

/**
 * معالج أخطاء الأمان
 */
function handleSecurityError($type, $details = []) {
    $log_message = "Security Error: $type";
    secureErrorLog($log_message, 'critical', $details);

    // تسجيل في قاعدة البيانات
    logErrorToDatabase('Security Error', $type, __FILE__, __LINE__, 'critical');

    // إعادة توجيه فورية لصفحة الأمان
    if (!headers_sent()) {
        header("Location: /App/error_handlers/403_handler.php");
        exit();
    } else {
        echo "<script>window.location.href='/App/error_handlers/403_handler.php';</script>";
        exit();
    }
}

/**
 * تهيئة معالجات الأخطاء الآمنة
 */
function initializeSecureErrorHandlers() {
    // تعيين معالج الأخطاء المخصص
    set_error_handler('secureErrorHandler');

    // تعيين معالج الاستثناءات المخصص
    set_exception_handler('secureExceptionHandler');

    // تعيين معالج الأخطاء القاتلة
    register_shutdown_function('secureFatalErrorHandler');

    // تعيين إعدادات الأخطاء حسب البيئة
    if (IS_PRODUCTION) {
        ini_set('display_errors', 0);
        ini_set('display_startup_errors', 0);
        error_reporting(E_ALL & ~E_NOTICE & ~E_STRICT & ~E_DEPRECATED);
    } else {
        ini_set('display_errors', 1);
        ini_set('display_startup_errors', 1);
        error_reporting(E_ALL);
    }

    // تعيين مسار لوج الأخطاء
    ini_set('log_errors', 1);
    ini_set('error_log', ERROR_LOG_PATH . 'php_errors.log');
}

/**
 * دالة للتحقق من صحة النظام
 */
function performSystemHealthCheck() {
    $issues = [];

    // فحص مجلد اللوجات
    if (!is_writable(ERROR_LOG_PATH)) {
        $issues[] = "Log directory is not writable: " . ERROR_LOG_PATH;
    }

    // فحص مساحة القرص
    $freeSpace = disk_free_space(ERROR_LOG_PATH);
    if ($freeSpace < 100 * 1024 * 1024) { // أقل من 100MB
        $issues[] = "Low disk space: " . round($freeSpace / 1024 / 1024, 2) . "MB remaining";
    }

    // فحص ذاكرة PHP
    $memoryLimit = ini_get('memory_limit');
    $memoryUsage = memory_get_usage(true);
    if ($memoryUsage > (0.8 * return_bytes($memoryLimit))) {
        $issues[] = "High memory usage: " . round($memoryUsage / 1024 / 1024, 2) . "MB";
    }

    if (!empty($issues)) {
        secureErrorLog("System health check failed", 'warning', ['issues' => $issues]);
    }

    return empty($issues);
}

/**
 * تحويل قيم الذاكرة إلى bytes
 */
function return_bytes($val) {
    $val = trim($val);
    $last = strtolower($val[strlen($val)-1]);
    $val = (int)$val;
    switch($last) {
        case 'g':
            $val *= 1024;
        case 'm':
            $val *= 1024;
        case 'k':
            $val *= 1024;
    }
    return $val;
}

// تهيئة تلقائية عند تضمين الملف
if (!defined('SECURE_ERROR_HANDLERS_INITIALIZED')) {
    define('SECURE_ERROR_HANDLERS_INITIALIZED', true);
    initializeSecureErrorHandlers();

    // إجراء فحص صحة النظام
    register_shutdown_function('performSystemHealthCheck');
}

?>
