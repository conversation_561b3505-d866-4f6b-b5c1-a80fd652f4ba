<?php
session_start();
include $_SERVER['DOCUMENT_ROOT'].'/config/Databasehost.php';
include '../system/database_config.php';
require_once __DIR__ . '/dynamic_column_helper.php';
include $_SERVER['DOCUMENT_ROOT']. '/PHPMailer/vendor/phpmailer/phpmailer/src/PHPMailer.php';
include $_SERVER['DOCUMENT_ROOT']. '/PHPMailer/vendor/phpmailer/phpmailer/src/SMTP.php';
include $_SERVER['DOCUMENT_ROOT']. '/PHPMailer/vendor/phpmailer/phpmailer/src/Exception.php';

// Check session
if (!isset($_SESSION['username']) || !isset($_SESSION['random_code'])) {
    header("Location: ../../../Loginpage.php");
    exit();
}

// Check access level
$access = $_SESSION['access_level'];
$email = $_SESSION['username'];

/**
 * Load field configurations from field_settings.php
 */
function loadFieldSettingsConfiguration($conn) {
    $configFile = __DIR__ . '/../settings/field_config.json';
    $defaultConfigFile = __DIR__ . '/../settings/default_field_config.json';

    // Try to load custom configuration first
    if (file_exists($configFile)) {
        $config = json_decode(file_get_contents($configFile), true);
        if ($config) {
            return filterEnabledFields($config, $conn);
        }
    }

    // Fallback to default configuration
    if (file_exists($defaultConfigFile)) {
        $config = json_decode(file_get_contents($defaultConfigFile), true);
        if ($config) {
            return filterEnabledFields($config, $conn);
        }
    }

    // No fallback - return empty if no configuration found
    return [];
}

/**
 * Filter only enabled fields and fields that exist in database
 */
function filterEnabledFields($config, $conn) {
    $dbColumns = getDatabaseColumns($conn);
    $filteredConfig = [];

    foreach ($config as $sectionKey => $section) {
        $filteredFields = [];

        if (isset($section['fields']) && is_array($section['fields'])) {
            foreach ($section['fields'] as $fieldKey => $field) {
                // Only include enabled fields that exist in database
                $isEnabled = isset($field['enabled']) ?
                    (is_bool($field['enabled']) ? $field['enabled'] : ($field['enabled'] === '1' || $field['enabled'] === 1 || $field['enabled'] === true)) :
                    false;

                if ($isEnabled && isset($dbColumns[$fieldKey])) {
                    $filteredFields[$fieldKey] = $field;
                }
            }
        }

        // Only include sections that have fields
        if (!empty($filteredFields)) {
            $filteredConfig[$sectionKey] = [
                'title' => $section['title'] ?? ucwords(str_replace('_', ' ', $sectionKey)),
                'icon' => $section['icon'] ?? 'fa-cog',
                'fields' => $filteredFields
            ];
        }
    }

    return $filteredConfig;
}

// Get current table structure to check which columns exist
$tableStructure = $conn->query("DESCRIBE databasehc");
$availableColumns = [];
while ($row = $tableStructure->fetch_assoc()) {
    $availableColumns[] = $row['Field'];
}

// Function to safely get options for a column if it exists
function getOptionsIfExists($conn, $columnName, $availableColumns) {
    if (in_array($columnName, $availableColumns)) {
        return $conn->query("SELECT DISTINCT `$columnName` FROM databasehc WHERE `$columnName` IS NOT NULL AND `$columnName` != '' ORDER BY `$columnName`");
    }
    return false;
}

// Get field configurations from field_settings.php
$fieldConfigurations = loadFieldSettingsConfiguration($conn);

// Prepare dropdown options for enabled fields
$dropdownOptions = [];
foreach ($fieldConfigurations as $section) {
    foreach ($section['fields'] as $fieldName => $fieldConfig) {
        if ($fieldConfig['type'] === 'select') {
            $result = getOptionsIfExists($conn, $fieldName, $availableColumns);
            if ($result) {
                $options = [];
                while ($row = $result->fetch_assoc()) {
                    $options[] = $row[$fieldName];
                }
                $dropdownOptions[$fieldName] = $options;
            }
        } elseif ($fieldConfig['type'] === 'dropdown') {
            $sourceType = $fieldConfig['source_type'] ?? 'custom';

            if ($sourceType === 'custom' && isset($fieldConfig['options'])) {
                // Use custom dropdown options
                $dropdownOptions[$fieldName] = $fieldConfig['options'];
            } elseif ($sourceType === 'database') {
                // Use database values
                $result = getOptionsIfExists($conn, $fieldName, $availableColumns);
                if ($result) {
                    $options = [];
                    while ($row = $result->fetch_assoc()) {
                        $options[] = $row[$fieldName];
                    }
                    $dropdownOptions[$fieldName] = $options;
                }
            }
        }
    }
}



// Process form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Debug: Log POST data
    error_log("POST data received: " . json_encode($_POST));

    $result = processFormSubmission($conn, $fieldConfigurations, $email);
    if ($result['success']) {
        $successMessage = $result['message'];
        // Clear any error messages
        unset($errorMessage);
    } else {
        $errorMessage = $result['message'];
        // Clear any success messages
        unset($successMessage);
    }
}

function processFormSubmission($conn, $fieldConfigurations, $email) {
    try {
        // Get all enabled fields
        $enabledFields = [];
        foreach ($fieldConfigurations as $section) {
            foreach ($section['fields'] as $fieldName => $fieldConfig) {
                $enabledFields[] = $fieldName;
            }
        }

        $data = [];

        // Process each enabled field
        foreach ($enabledFields as $fieldName) {
            // Handle "Other" fields
            if (isset($_POST[$fieldName . '_other']) && !empty($_POST[$fieldName . '_other']) && $_POST[$fieldName] == 'Other') {
                $data[$fieldName] = $conn->real_escape_string(trim($_POST[$fieldName . '_other']));
            } elseif (isset($_POST[$fieldName]) && !empty($_POST[$fieldName])) {
                $data[$fieldName] = $conn->real_escape_string(trim($_POST[$fieldName]));
            }
        }

        // Handle checkbox fields
        $checkboxFields = ['Crowd', 'Legal', 'Certified', 'IMS', 'TTG', 'MW', 'Lango', 'Bar', 'GlB', 'Eff', 'Foc', 'TUS', 'Mav', 'Abs', 'Inlin', 'LB', 'Propio'];
        foreach ($checkboxFields as $field) {
            if (in_array($field, $enabledFields)) {
                $data[$field] = isset($_POST[$field]) ? 'YES' : 'NO';
            }
        }

        // Add default values for required fields if not present
        if (!isset($data['FGEmails']) || empty($data['FGEmails'])) {
            $data['FGEmails'] = $email; // Use session email as default
        }

        // Add timestamp fields
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');

        // Debug: Log the data being processed
        error_log("Processing form data: " . json_encode($data));

        // Build dynamic insert query
        $queryData = buildDynamicInsertQuery($conn, $data);
        if (!$queryData) {
            return ['success' => false, 'message' => 'No valid data to insert. Please fill at least one field.'];
        }

        // Debug: Log the query being executed
        error_log("Executing query: " . $queryData['query']);
        error_log("With values: " . json_encode($queryData['values']));

        // Prepare and execute statement
        $stmt = $conn->prepare($queryData['query']);
        if (!$stmt) {
            error_log("Database prepare error: " . $conn->error);
            return ['success' => false, 'message' => 'Database prepare error: ' . $conn->error];
        }

        // Create types string for bind_param
        $types = str_repeat('s', count($queryData['values']));
        $stmt->bind_param($types, ...$queryData['values']);

        if ($stmt->execute()) {
            $insertedId = $conn->insert_id;

            // Send email notification
            try {
                sendEmailNotification($data, $email);
            } catch (Exception $e) {
                error_log("Email notification error: " . $e->getMessage());
                // Don't fail the entire operation if email fails
            }

            // Audit log
            try {
                $fgEmail = $data['FGEmails'] ?? $email;
                $auditQuery = "INSERT INTO audit_log (
                    record_id, changed_by, change_date, field_name,
                    old_value, new_value, action, updated_from
                ) VALUES (
                    ?, ?, NOW(), 'All Fields',
                    '', 'Added via field settings form', 'Insert', 'addindatabse'
                )";

                $auditStmt = $conn->prepare($auditQuery);
                if ($auditStmt) {
                    $auditStmt->bind_param('ss', $fgEmail, $email);
                    $auditStmt->execute();
                }
            } catch (Exception $e) {
                error_log("Audit log error: " . $e->getMessage());
                // Don't fail the entire operation if audit log fails
            }

            return ['success' => true, 'message' => "Data added successfully! Record ID: $insertedId. Notification email sent."];
        } else {
            error_log("Database insert error: " . $stmt->error);
            return ['success' => false, 'message' => 'Database insert error: ' . $stmt->error];
        }

    } catch (Exception $e) {
        error_log("Form processing error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Error processing form: ' . $e->getMessage()];
    }
}

// Helper functions for dynamic form processing
function buildDynamicInsertQuery($conn, $data) {
    if (empty($data)) {
        return false;
    }

    // Get available columns from database
    $availableColumns = [];
    $result = $conn->query("DESCRIBE databasehc");
    while ($row = $result->fetch_assoc()) {
        $availableColumns[] = $row['Field'];
    }

    $fields = [];
    $placeholders = [];
    $values = [];

    foreach ($data as $field => $value) {
        // Only include fields that exist in the database
        if (in_array($field, $availableColumns)) {
            $fields[] = "`$field`";
            $placeholders[] = "?";
            $values[] = $value;
        } else {
            error_log("Warning: Field '$field' does not exist in database table 'databasehc'");
        }
    }

    if (empty($fields)) {
        error_log("No valid fields found for insert query");
        return false;
    }

    $query = "INSERT INTO databasehc (" . implode(', ', $fields) . ") VALUES (" . implode(', ', $placeholders) . ")";

    return [
        'query' => $query,
        'values' => $values,
        'fields' => $fields
    ];
}

function sendEmailNotification($data, $email) {
    try {
        $mailConfig = include $_SERVER['DOCUMENT_ROOT'].'/config/Emailinformation.php';
        $mail = new PHPMailer\PHPMailer\PHPMailer(true);

        // SMTP settings
        $mail->isSMTP();
        $mail->Host = $mailConfig['host'];
        $mail->SMTPAuth = true;
        $mail->Username = $mailConfig['username'];
        $mail->Password = $mailConfig['password'];
        $mail->SMTPSecure = $mailConfig['encryption'];
        $mail->Port = $mailConfig['port'];
        $mail->CharSet = $mailConfig['charset'];

        // Email setup
        $mail->setFrom($mailConfig['username'], 'Kalam App IMS');

        // Add recipients
        if (isset($mailConfig['recipients']) && is_array($mailConfig['recipients'])) {
            foreach ($mailConfig['recipients'] as $recipient) {
                $mail->addAddress($recipient);
            }
        }

        // Add CC
        $mail->addCC($email);
        $mail->addCC('<EMAIL>');

        // Set EST timezone and format the date/time
        $estTimezone = new DateTimeZone('America/New_York');
        $estDateTime = new DateTime('now', $estTimezone);
        $formattedDateTime = $estDateTime->format('Y-m-d h:i:s A');

        // Build email content
        $emailBody = buildEmailContent($data, $email, $formattedDateTime);

        // Email setup
        $mail->isHTML(true);
        $mail->Subject = "New Data Entry Report (Field Settings) - " . $formattedDateTime;
        $mail->Body = $emailBody;

        // Send email
        $mail->send();

    } catch (Exception $e) {
        error_log("Email sending error: " . $e->getMessage());
    }
}

function buildEmailContent($data, $email, $formattedDateTime) {
    $dataRows = '';

    // Build table rows with available data
    $displayFields = ['Module', 'Status', 'Account', 'InterpreterName', 'Language', 'Startdate'];

    $dataRows .= '<tr>';
    foreach ($displayFields as $field) {
        $value = $data[$field] ?? 'N/A';
        $dataRows .= '<td style="padding: 8px; border: 1px solid #ddd;">' . htmlspecialchars($value) . '</td>';
    }
    $dataRows .= '</tr>';

    return '
    <div style="font-family: Arial, sans-serif; max-width: 1200px; margin: 0 auto; background-color: #f9f9f9; padding: 20px;">
        <div style="background-color: #8a5f8a; color: white; padding: 15px; text-align: center; border-radius: 5px 5px 0 0;">
            <h2 style="margin: 0;">New Data Entry Report (Field Settings Form)</h2>
        </div>

        <div style="background-color: white; padding: 20px; border-radius: 0 0 5px 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
            <div style="margin-bottom: 20px;">
                <p><strong>Entry Summary:</strong></p>
                <ul style="list-style-type: none; padding-left: 0;">
                    <li>📅 <strong>Date:</strong> ' . $formattedDateTime . ' (EST)</li>
                    <li>👤 <strong>Entered By:</strong> ' . $email . '</li>
                    <li>✅ <strong>Record Added Successfully</strong></li>
                </ul>
            </div>

            <div style="overflow-x: auto;">
                <table style="width: 100%; border-collapse: collapse; margin-top: 20px; font-size: 14px;">
                    <thead>
                        <tr style="background-color: #8a5f8a; color: white;">
                            <th style="padding: 10px; text-align: left;">Module</th>
                            <th style="padding: 10px; text-align: left;">Status</th>
                            <th style="padding: 10px; text-align: left;">Account</th>
                            <th style="padding: 10px; text-align: left;">Interpreter</th>
                            <th style="padding: 10px; text-align: left;">Language</th>
                            <th style="padding: 10px; text-align: left;">Start Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        ' . $dataRows . '
                    </tbody>
                </table>
            </div>

            <div style="text-align: center; margin-top: 30px;">
                <a href="https://kalamcxapp.site" style="display: inline-block; padding: 10px 20px; background-color: #8a5f8a; color: white; text-decoration: none; border-radius: 5px; font-weight: bold;">View Database</a>
            </div>

            <div style="margin-top: 30px; text-align: center; font-size: 12px; color: #666;">
                <p>This email was automatically generated by Kalam App IMS System (Field Settings Form)</p>
            </div>
        </div>
    </div>';
}

// Helper functions for generating dynamic form HTML
function generateFormFieldsHTML($fields, $dropdownOptions) {
    $html = '';
    $currentRow = [];
    $currentRowCols = 0;

    foreach ($fields as $fieldKey => $field) {
        $colSize = $field['col'] ?? 6;

        // Start new row if current row would exceed 12 columns
        if ($currentRowCols + $colSize > 12) {
            if (!empty($currentRow)) {
                $html .= '<div class="row">' . implode('', $currentRow) . '</div>';
            }
            $currentRow = [];
            $currentRowCols = 0;
        }

        $currentRow[] = generateSingleFieldHTML($fieldKey, $field, $dropdownOptions);
        $currentRowCols += $colSize;
    }

    // Add remaining fields
    if (!empty($currentRow)) {
        $html .= '<div class="row">' . implode('', $currentRow) . '</div>';
    }

    return $html;
}
function generateSingleFieldHTML($fieldKey, $field, $dropdownOptions) {
    $colSize = $field['col'] ?? 6;
    $fieldType = $field['type'] ?? 'text';
    $label = $field['label'] ?? ucwords(str_replace('_', ' ', $fieldKey));
    $icon = $field['icon'] ?? 'fa-edit';
    $required = ($field['required'] ?? false) ? 'required' : '';
    $allowOther = $field['other'] ?? false;

    $html = '<div class="col-md-' . $colSize . '">';

    if ($fieldType === 'checkbox') {
        $html .= '<div class="checkbox-label">';
        $html .= '<input type="checkbox" id="' . $fieldKey . '" name="' . $fieldKey . '" value="yes">';
        $html .= '<label for="' . $fieldKey . '"><i class="fas ' . $icon . ' me-2"></i>' . $label . '</label>';
        $html .= '</div>';
    } else {
        $html .= '<div class="floating-label">';
        $html .= '<label for="' . $fieldKey . '"><i class="fas ' . $icon . ' me-2"></i>' . $label . '</label>';

        switch ($fieldType) {
            case 'select':
            case 'dropdown':
                $html .= generateSelectFieldHTML($fieldKey, $field, $dropdownOptions, $allowOther, $required);
                break;
            case 'textarea':
                $html .= '<textarea id="' . $fieldKey . '" name="' . $fieldKey . '" class="form-control" rows="3" ' . $required . ' placeholder="Enter ' . strtolower($label) . '"></textarea>';
                break;
            case 'date':
                $html .= '<input type="date" id="' . $fieldKey . '" name="' . $fieldKey . '" class="form-control" ' . $required . '>';
                break;
            default:
                $html .= '<input type="' . $fieldType . '" id="' . $fieldKey . '" name="' . $fieldKey . '" class="form-control" ' . $required . ' placeholder="Enter ' . strtolower($label) . '">';
                break;
        }

        $html .= '</div>';
    }

    $html .= '</div>';
    return $html;
}
function generateSelectFieldHTML($fieldKey, $field, $dropdownOptions, $allowOther, $required) {
    $onchange = $allowOther ? "onchange=\"checkOther(this, '{$fieldKey}_other')\"" : '';

    $html = '<select id="' . $fieldKey . '" name="' . $fieldKey . '" class="form-control" ' . $required . ' ' . $onchange . '>';
    $html .= '<option value="">Select ' . ($field['label'] ?? $fieldKey) . '</option>';

    // Get options from dropdown options array
    if (isset($dropdownOptions[$fieldKey])) {
        $options = $dropdownOptions[$fieldKey];
        if (is_array($options)) {
            foreach ($options as $option) {
                $html .= '<option value="' . htmlspecialchars($option) . '">' . htmlspecialchars($option) . '</option>';
            }
        } elseif ($options) {
            while ($row = $options->fetch_assoc()) {
                $html .= '<option value="' . htmlspecialchars($row[$fieldKey]) . '">' . htmlspecialchars($row[$fieldKey]) . '</option>';
            }
        }
    }

    if ($allowOther) {
        $html .= '<option value="Other">Other</option>';
    }

    $html .= '</select>';

    if ($allowOther) {
        $html .= '<input type="text" id="' . $fieldKey . '_other" name="' . $fieldKey . '_other" class="form-control other-field mt-2" style="display: none;" placeholder="Please specify ' . strtolower($field['label'] ?? $fieldKey) . '">';
    }

    return $html;
}
?>


<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Management System</title>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome 6 with 3D icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../../assets/css/Database.css">

    <style>
        /* تحسينات التنسيق باستخدام ألوان Database.css */
        body {
            font-size: 12px !important;
            line-height: 1.4 !important;
            background: var(--gradient-primary);
            min-height: 100vh;
        }

        .container {
            max-width: 100% !important;
            padding: 1rem !important;
        }

        .card {
            border: none;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            margin-bottom: 1.5rem;
            background: var(--bg-white);
            transition: var(--transition);
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
        }

        .card-header {
            background: var(--gradient-primary);
            color: var(--text-white);
            border: none;
            border-radius: var(--radius-xl) var(--radius-xl) 0 0;
            padding: 1rem 1.5rem;
            font-weight: 600;
        }

        .card-header h3 {
            font-size: 14px !important;
            margin: 0;
            display: flex;
            align-items: center;
        }

        .card-header i {
            margin-right: 0.5rem;
            font-size: 1.2em;
        }

        .card-body {
            padding: 1.5rem !important;
        }

        .floating-label {
            margin-bottom: 1rem !important;
            position: relative;
        }

        .floating-label label {
            font-size: 11px !important;
            font-weight: 500 !important;
            color: var(--text-secondary);
            margin-bottom: 0.5rem;
            display: block;
        }

        .floating-label label i {
            font-size: 10px !important;
            margin-right: 0.5rem;
            color: var(--primary-color);
        }

        .form-control, .form-select {
            font-size: 11px !important;
            padding: 0.6rem 0.8rem !important;
            border: 1px solid var(--border-light);
            border-radius: var(--radius-md);
            transition: var(--transition);
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: var(--shadow-primary);
        }

        .btn {
            font-size: 12px !important;
            padding: 0.6rem 1.2rem !important;
            border-radius: var(--radius-md);
            font-weight: 500;
            transition: var(--transition);
        }

        .btn-primary {
            background: var(--gradient-primary);
            border: none;
            color: var(--text-white);
        }

        .btn-primary:hover {
            background: var(--gradient-accent);
            transform: translateY(-1px);
        }

        .btn-outline-secondary {
            border-color: var(--gray-color);
            color: var(--gray-color);
        }

        .btn-outline-secondary:hover {
            background-color: var(--gray-color);
            border-color: var(--gray-color);
        }

        .btn-outline-info {
            border-color: var(--info-color);
            color: var(--info-color);
        }

        .btn-outline-info:hover {
            background-color: var(--info-color);
            border-color: var(--info-color);
        }

        .alert {
            font-size: 11px !important;
            padding: 0.8rem 1rem;
            border-radius: var(--radius-md);
            border: none;
        }

        .alert-success {
            background: var(--success-light);
            color: var(--success-color);
            border-left: 4px solid var(--success-color);
        }

        .alert-danger {
            background: var(--danger-light);
            color: var(--danger-color);
            border-left: 4px solid var(--danger-color);
        }

        .alert-warning {
            background: var(--warning-light);
            color: var(--warning-color);
            border-left: 4px solid var(--warning-color);
        }

        .logo-container {
            text-align: center;
            margin-bottom: 2rem;
            background: var(--bg-white);
            padding: 2rem;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-md);
        }

        .logo-container img {
            max-height: 60px;
            width: auto;
        }

        .logo-container h1 {
            font-size: 18px !important;
            color: var(--primary-color);
            margin-top: 1rem;
            margin-bottom: 0.5rem;
        }

        .user-info .badge {
            font-size: 10px !important;
            padding: 0.4rem 0.8rem;
            background-color: var(--gray-100);
            color: var(--text-secondary);
            border: 1px solid var(--border-light);
        }

        .other-field {
            margin-top: 0.5rem !important;
            display: none;
        }

        textarea {
            min-height: 80px !important;
            resize: vertical;
        }

        .form-check-input:checked {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .form-check-label {
            color: var(--text-secondary);
            font-size: 11px !important;
        }

        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 768px) {
            .container {
                padding: 0.5rem !important;
            }

            .card-body {
                padding: 1rem !important;
            }

            .logo-container {
                padding: 1rem;
            }

            .logo-container h1 {
                font-size: 16px !important;
            }
        }

        /* تحسينات للطباعة */
        @media print {
            body {
                background: var(--bg-white) !important;
            }

            .card {
                box-shadow: none !important;
                border: 1px solid var(--border-light) !important;
            }
        }
    </style>
</head>
<body class="add-database">
    <div class="container py-5">
        <!-- Logo Header with Animation -->
        <div class="logo-container animate__animated animate__fadeInDown">
            <img src="/Logos/kalam.png" alt="Kalam Logo" class="img-fluid">
            <h1 class="mt-3" style="color: var(--primary-dark);">Data Management System</h1>
            <div class="user-info mt-2">
                <span class="badge bg-light text-dark">
                    <i class="fas fa-user-circle me-1"></i> <?php echo htmlspecialchars($email); ?>
                </span>
            </div>
        </div>

        <?php if (isset($successMessage)): ?>
            <div class="alert alert-success alert-dismissible fade show animate__animated animate__fadeIn">
                <i class="fas fa-check-circle me-2"></i> <?php echo $successMessage; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($errorMessage)): ?>
            <div class="alert alert-danger alert-dismissible fade show animate__animated animate__fadeIn">
                <i class="fas fa-exclamation-circle me-2"></i> <?php echo $errorMessage; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <!-- Debug Information (only show if there are issues) -->
        <?php if (isset($errorMessage) || empty($fieldConfigurations)): ?>
            <div class="card mb-4">
                <div class="card-header">
                    <h3><i class="fas fa-bug me-2"></i>Debug Information</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Field Configurations Status:</h5>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-info-circle text-info me-2"></i>Total Sections: <?= count($fieldConfigurations) ?></li>
                                <li><i class="fas fa-info-circle text-info me-2"></i>Total Fields:
                                    <?php
                                    $totalFields = 0;
                                    foreach ($fieldConfigurations as $section) {
                                        $totalFields += count($section['fields']);
                                    }
                                    echo $totalFields;
                                    ?>
                                </li>
                                <li><i class="fas fa-info-circle text-info me-2"></i>Available Columns: <?= count($availableColumns) ?></li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5>Configuration Files:</h5>
                            <ul class="list-unstyled">
                                <li>
                                    <i class="fas <?= file_exists(__DIR__ . '/../settings/field_config.json') ? 'fa-check text-success' : 'fa-times text-danger' ?> me-2"></i>
                                    field_config.json
                                </li>
                                <li>
                                    <i class="fas <?= file_exists(__DIR__ . '/../settings/default_field_config.json') ? 'fa-check text-success' : 'fa-times text-danger' ?> me-2"></i>
                                    default_field_config.json
                                </li>
                            </ul>
                        </div>
                    </div>

                    <?php if (empty($fieldConfigurations)): ?>
                        <div class="alert alert-warning mt-3">
                            <strong>No field configurations found!</strong><br>
                            Please check:
                            <ul>
                                <li>Field configuration files exist in the settings directory</li>
                                <li>JSON files are valid and properly formatted</li>
                                <li>At least one field is enabled in the configuration</li>
                                <li>Database connection is working</li>
                            </ul>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>

        <!-- Dynamic Form -->
        <form method="POST" class="animate__animated animate__fadeIn" id="dataForm">
            <?php
            // Check if we have any field configurations
            if (empty($fieldConfigurations)):
            ?>
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                No field configurations found. Please check the field settings configuration file.
            </div>
            <?php else: ?>

            <?php
            // Generate form sections from configuration
            foreach ($fieldConfigurations as $sectionKey => $section):
            ?>
            <div class="card mb-4 fade-in">
                <div class="card-header d-flex align-items-center">
                    <i class="fas <?= htmlspecialchars($section['icon']) ?> fa-2x me-3 icon-3d"></i>
                    <h3 class="mb-0"><?= htmlspecialchars($section['title']) ?></h3>
                    <span class="badge bg-light text-dark ms-auto">
                        <?= count($section['fields']) ?> fields
                    </span>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php foreach ($section['fields'] as $fieldKey => $field): ?>
                        <div class="col-md-<?= htmlspecialchars($field['col'] ?? 6) ?>">
                            <div class="floating-label">
                                <label for="<?= htmlspecialchars($fieldKey) ?>">
                                    <i class="fas <?= htmlspecialchars($field['icon'] ?? 'fa-edit') ?> me-2"></i>
                                    <?= htmlspecialchars($field['label'] ?? ucwords(str_replace('_', ' ', $fieldKey))) ?>
                                    <?php if (isset($field['required']) && $field['required']): ?>
                                        <span class="text-danger">*</span>
                                    <?php endif; ?>
                                </label>

                                <?php if (($field['type'] === 'select' || $field['type'] === 'dropdown') && isset($field['other']) && $field['other']): ?>
                                    <select id="<?= htmlspecialchars($fieldKey) ?>" name="<?= htmlspecialchars($fieldKey) ?>" class="form-control" onchange="checkOther(this, '<?= htmlspecialchars($fieldKey) ?>_other')" <?= (isset($field['required']) && $field['required']) ? 'required' : '' ?>>
                                        <option value="">Select <?= htmlspecialchars($field['label'] ?? $fieldKey) ?></option>
                                        <?php
                                        // Get dropdown options for this field
                                        if (isset($dropdownOptions[$fieldKey])) {
                                            foreach ($dropdownOptions[$fieldKey] as $option) {
                                                echo '<option value="' . htmlspecialchars($option) . '">' . htmlspecialchars($option) . '</option>';
                                            }
                                        }
                                        ?>
                                        <option value="Other">Other</option>
                                    </select>
                                    <input type="text" id="<?= htmlspecialchars($fieldKey) ?>_other" name="<?= htmlspecialchars($fieldKey) ?>_other" class="form-control other-field mt-2" style="display: none;" placeholder="Please specify <?= strtolower($field['label'] ?? $fieldKey) ?>">

                                <?php elseif ($field['type'] === 'select' || $field['type'] === 'dropdown'): ?>
                                    <select id="<?= htmlspecialchars($fieldKey) ?>" name="<?= htmlspecialchars($fieldKey) ?>" class="form-control" <?= (isset($field['required']) && $field['required']) ? 'required' : '' ?>>
                                        <option value="">Select <?= htmlspecialchars($field['label'] ?? $fieldKey) ?></option>
                                        <?php
                                        if (isset($dropdownOptions[$fieldKey])) {
                                            foreach ($dropdownOptions[$fieldKey] as $option) {
                                                echo '<option value="' . htmlspecialchars($option) . '">' . htmlspecialchars($option) . '</option>';
                                            }
                                        }
                                        ?>
                                    </select>

                                <?php elseif ($field['type'] === 'textarea'): ?>
                                    <textarea id="<?= htmlspecialchars($fieldKey) ?>" name="<?= htmlspecialchars($fieldKey) ?>" class="form-control" rows="3" placeholder="Enter <?= strtolower($field['label'] ?? $fieldKey) ?>" <?= (isset($field['required']) && $field['required']) ? 'required' : '' ?>></textarea>

                                <?php elseif ($field['type'] === 'date'): ?>
                                    <input type="date" id="<?= htmlspecialchars($fieldKey) ?>" name="<?= htmlspecialchars($fieldKey) ?>" class="form-control" <?= (isset($field['required']) && $field['required']) ? 'required' : '' ?>>

                                <?php elseif ($field['type'] === 'email'): ?>
                                    <input type="email" id="<?= htmlspecialchars($fieldKey) ?>" name="<?= htmlspecialchars($fieldKey) ?>" class="form-control" placeholder="Enter <?= strtolower($field['label'] ?? $fieldKey) ?>" <?= (isset($field['required']) && $field['required']) ? 'required' : '' ?>>

                                <?php elseif ($field['type'] === 'number'): ?>
                                    <input type="number" id="<?= htmlspecialchars($fieldKey) ?>" name="<?= htmlspecialchars($fieldKey) ?>" class="form-control" placeholder="Enter <?= strtolower($field['label'] ?? $fieldKey) ?>" <?= (isset($field['required']) && $field['required']) ? 'required' : '' ?>>

                                <?php elseif ($field['type'] === 'checkbox'): ?>
                                    <div class="form-check">
                                        <input type="checkbox" id="<?= htmlspecialchars($fieldKey) ?>" name="<?= htmlspecialchars($fieldKey) ?>" class="form-check-input" value="yes">
                                        <label class="form-check-label" for="<?= htmlspecialchars($fieldKey) ?>">
                                            <?= htmlspecialchars($field['label'] ?? ucwords(str_replace('_', ' ', $fieldKey))) ?>
                                        </label>
                                    </div>

                                <?php else: ?>
                                    <input type="text" id="<?= htmlspecialchars($fieldKey) ?>" name="<?= htmlspecialchars($fieldKey) ?>" class="form-control" placeholder="Enter <?= strtolower($field['label'] ?? $fieldKey) ?>" <?= (isset($field['required']) && $field['required']) ? 'required' : '' ?>>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>

            <!-- Submit Button -->
            <div class="text-center mt-4 animate__animated animate__fadeInUp">
                <button type="submit" class="btn btn-primary btn-lg">
                    <i class="fas fa-save me-2"></i>Save Data
                </button>
                <button type="reset" class="btn btn-outline-secondary btn-lg ms-3">
                    <i class="fas fa-undo me-2"></i>Reset Form
                </button>
                <a href="../index.php" class="btn btn-outline-info btn-lg ms-3">
                    <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                </a>
            </div>

            <?php endif; ?>
        </form>
    </div>

    <!-- Bootstrap 5 JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript -->
    <script>
        function checkOther(selectElement, otherFieldId) {
            const otherField = document.getElementById(otherFieldId);
            if (selectElement.value === 'Other') {
                otherField.style.display = 'block';
                otherField.required = true;
            } else {
                otherField.style.display = 'none';
                otherField.required = false;
                otherField.value = '';
            }
        }
    </script>
</body>

      

    <!-- Bootstrap 5 JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Animate form groups sequentially
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.fade-in');

            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = 1;
                }, index * 100);
            });

            // Add form validation
            const form = document.getElementById('dataForm');
            if (form) {
                form.addEventListener('submit', function(e) {
                    // Check if at least one field is filled
                    const formData = new FormData(form);
                    let hasData = false;

                    for (let [key, value] of formData.entries()) {
                        if (value && value.trim() !== '') {
                            hasData = true;
                            break;
                        }
                    }

                    if (!hasData) {
                        e.preventDefault();
                        alert('Please fill at least one field before submitting.');
                        return false;
                    }

                    // Show loading state
                    const submitBtn = form.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        submitBtn.disabled = true;
                        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';
                    }
                });
            }

            // Auto-save functionality (optional)
            const inputs = document.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                input.addEventListener('change', function() {
                    // Save to localStorage for recovery
                    localStorage.setItem('form_' + this.name, this.value);
                });
            });

            // Restore form data from localStorage
            inputs.forEach(input => {
                const savedValue = localStorage.getItem('form_' + input.name);
                if (savedValue && !input.value) {
                    input.value = savedValue;
                }
            });

            // Clear localStorage on successful submit
            if (window.location.search.includes('success')) {
                localStorage.clear();
            }
        });

        // Function to show/hide other field
        function checkOther(selectElement, otherFieldId) {
            const otherField = document.getElementById(otherFieldId);
            if (selectElement.value === 'Other') {
                otherField.style.display = 'block';
                otherField.required = true;
                otherField.focus();
            } else {
                otherField.style.display = 'none';
                otherField.required = false;
                otherField.value = '';
            }
        }

        // Add tooltips for better UX
        function addTooltips() {
            const labels = document.querySelectorAll('label');
            labels.forEach(label => {
                const input = document.getElementById(label.getAttribute('for'));
                if (input && input.placeholder) {
                    label.title = input.placeholder;
                }
            });
        }

        // Call addTooltips when page loads
        document.addEventListener('DOMContentLoaded', addTooltips);
    </script>
</body>
</html>
