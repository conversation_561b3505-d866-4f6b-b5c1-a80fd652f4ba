@import url('root-variables.css');

/* Body styles */
body {
    font-family: 'Roboto', sans-serif;
    font-size: 0.9rem;
    background: var(--leaves-bg-gradient);
    color: var(--leaves-text-medium);
    min-height: 100vh;
    padding: 20px;
    margin: 0;
}

/* Header styling */
.leaves-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: linear-gradient(135deg, var(--primary-100), var(--primary-200));
    border-radius: var(--radius-lg);
    margin-bottom: 25px;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-light);
}

    .leaves-title {
        color: var(--leaves-primary-dark);
        font-weight: 500;
        font-size: 1.5rem;
        margin: 0;
        display: flex;
        align-items: center;
    }

    .leaves-title img {
        height: 40px;
        margin-right: 15px;
    }

/* Filters styling */
.leaves-filters {
    background: var(--bg-white);
    padding: 20px;
    margin-bottom: 25px;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-light);
}

    .leaves-search-input {
        border-radius: 20px;
        padding-left: 2.5rem;
        border: 1px solid #e8e0e8;
        background-color: white;
        transition: border-color 0.2s, box-shadow 0.2s;
    }

    .leaves-search-input:focus {
        border-color: var(--leaves-primary-color);
        box-shadow: 0 0 0 3px rgba(158, 144, 215, 0.15);
        outline: none;
    }

    .leaves-search-icon {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: var(--leaves-gray-color);
    }

    .leaves-filter-select {
        border-radius: 6px;
        border: 1px solid #e8e0e8;
        font-size: 0.85rem;
        padding: 0.4rem 0.75rem;
        background-color: white;
        transition: border-color 0.2s, box-shadow 0.2s;
    }

    .leaves-filter-select:focus {
        border-color: var(--leaves-primary-color);
        box-shadow: 0 0 0 3px rgba(158, 144, 215, 0.15);
        outline: none;
    }

/* Table styling */
.leaves-table {
    background: var(--bg-white);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    overflow-x: auto;
    width: 100%;
    margin-bottom: 2rem;
    border: 1px solid var(--border-light);
}

    .leaves-table table {
        width: 100%;
        min-width: 1200px;
        border-collapse: separate;
        border-spacing: 0;
        font-size: 0.85rem;
    }

    .leaves-table thead {
        background: linear-gradient(90deg, var(--leaves-primary-dark), var(--leaves-accent-color));
        color: white;
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .leaves-table th {
        font-weight: 500;
        padding: 0.8rem 1rem;
        font-size: 0.85rem;
        white-space: nowrap;
        border: 1px solid #e0d0e0;
        text-align: center;
        letter-spacing: 0.5px;
    }

    .leaves-table td {
        padding: 0.7rem 1rem;
        font-size: 0.85rem;
        vertical-align: middle;
        border: 1px solid #f0e8f0;
        text-align: center;
        transition: background-color 0.2s;
    }

    /* Row styling */
    .leaves-table tr:nth-child(even) {
        background-color: #fcf9fc;
    }

    .leaves-table tr:nth-child(odd) {
        background-color: var(--leaves-white);
    }

    .leaves-table tr:hover {
        background-color: var(--leaves-light-color);
    }

/* Button styling */
.leaves-btn {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
    border: none;
    box-shadow: var(--shadow-sm);
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

    .leaves-btn-approve {
        background-color: var(--leaves-success-color);
        color: white;
    }

.leaves-btn-approve:hover {
    background-color: var(--success-700);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.leaves-btn-reject {
    background-color: var(--danger-500);
    color: white;
}

.leaves-btn-reject:hover {
    background-color: var(--danger-700);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.leaves-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

    /* Status styling */
    .leaves-status-approved {
        color: var(--leaves-success-color);
        font-weight: 500;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        background-color: rgba(106, 190, 106, 0.1);
    }

    .leaves-status-rejected {
        color: var(--leaves-danger-color);
        font-weight: 500;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        background-color: rgba(229, 115, 115, 0.1);
    }

    .leaves-status-pending {
        color: var(--leaves-warning-color);
        font-weight: 500;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        background-color: rgba(240, 180, 104, 0.1);
    }

    /* Comment input styling */
    .leaves-comment-input {
        width: 100%;
        padding: 0.4rem 0.75rem;
        border: 1px solid #e8e0e8;
        border-radius: 4px;
        font-size: 0.85rem;
        transition: border-color 0.2s, box-shadow 0.2s;
    }

    .leaves-comment-input:focus {
        border-color: var(--leaves-primary-color);
        box-shadow: 0 0 0 3px rgba(158, 144, 215, 0.15);
        outline: none;
    }

    /* Notification styling */
    .leaves-notification {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 6px;
        background-color: white;
        box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        z-index: 1000;
        transform: translateX(120%);
        transition: transform 0.3s ease;
        display: flex;
        align-items: center;
        max-width: 350px;
    }

    .leaves-notification.show {
        transform: translateX(0);
    }

    .leaves-notification-success {
        border-left: 4px solid var(--leaves-success-color);
    }

    .leaves-notification-danger {
        border-left: 4px solid var(--leaves-danger-color);
    }

    .leaves-notification-warning {
        border-left: 4px solid var(--leaves-warning-color);
    }

    .leaves-notification-icon {
        margin-right: 15px;
        font-size: 1.5rem;
    }

    .leaves-notification-success .leaves-notification-icon {
        color: var(--leaves-success-color);
    }

    .leaves-notification-danger .leaves-notification-icon {
        color: var(--leaves-danger-color);
    }

    .leaves-notification-warning .leaves-notification-icon {
        color: var(--leaves-warning-color);
    }

    .leaves-notification-content {
        flex: 1;
    }

    .leaves-notification-title {
        font-weight: 500;
        margin-bottom: 5px;
        color: var(--leaves-text-dark);
    }

    .leaves-notification-message {
        color: var(--leaves-text-medium);
        font-size: 0.85rem;
        margin: 0;
    }

    /* Floating action buttons */
    .leaves-floating-actions {
        position: fixed;
        bottom: 30px;
        right: 30px;
        display: flex;
        flex-direction: column;
        gap: 10px;
        z-index: 100;
    }

    .leaves-floating-btn {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 3px 10px rgba(0,0,0,0.15);
        border: none;
        font-size: 1.2rem;
        color: white;
        transition: transform 0.2s, box-shadow 0.2s;
    }

    .leaves-floating-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }

    .leaves-floating-btn-primary {
        background-color: var(--leaves-primary-color);
    }

    .leaves-floating-btn-success {
        background-color: var(--leaves-success-color);
    }

    .leaves-floating-btn-info {
        background-color: var(--leaves-accent-color);
    }

/* Responsive Design */
@media (max-width: 768px) {
    body {
        padding: 10px;
    }

    .leaves-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .leaves-filters .row {
        gap: 15px;
    }

    .leaves-table {
        font-size: 0.75rem;
    }

    .leaves-btn {
        padding: 0.3rem 0.6rem;
        font-size: 0.75rem;
    }

    .leaves-floating-actions {
        bottom: 20px;
        right: 20px;
    }

    .leaves-floating-btn {
        width: 45px;
        height: 45px;
        font-size: 1rem;
    }
}

/* Print styles */
@media print {
    .leaves-filters,
    .leaves-btn,
    .leaves-comment-input,
    .leaves-notification,
    .leaves-floating-actions {
        display: none !important;
    }

    body {
        padding: 0 !important;
        background: white !important;
        font-size: 10pt;
    }

    .leaves-header {
        border-bottom: 1px solid #000;
        background: white !important;
    }

    .leaves-table {
        width: 100%;
        max-width: 100%;
        box-shadow: none;
        overflow: visible;
        margin: 0;
        padding: 0;
    }

    .leaves-table table {
        width: 100%;
        border-collapse: collapse;
    }

    .leaves-table th,
    .leaves-table td {
        border: 1px solid #000;
        padding: 5px;
    }
}
