<?php
/**
 * إصلاح سريع لمشكلة التواريخ
 * Quick fix for date issues
 */

require_once '../../../config/database.php';

echo "<h1>🚀 إصلاح سريع لمشكلة التواريخ</h1>";

try {
    $conn = new mysqli($servername, $username, $password, $dbname);
    
    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }
    
    echo "<p style='color: green;'>✅ متصل بقاعدة البيانات</p>";
    
    // Quick fix for all date columns
    $dateColumns = ['Startdate', 'DateofBirth', 'LastActivitydate', 'AttritionDate'];
    
    echo "<h2>إصلاح أعمدة التاريخ...</h2>";
    
    $totalFixed = 0;
    
    foreach ($dateColumns as $column) {
        // Check if column exists first
        $checkColumn = $conn->query("SHOW COLUMNS FROM databasehc LIKE '$column'");
        
        if ($checkColumn && $checkColumn->num_rows > 0) {
            echo "<p>إصلاح عمود: <strong>$column</strong></p>";
            
            // Fix problematic date values
            $fixSql = "UPDATE databasehc SET `$column` = NULL WHERE `$column` = '' OR `$column` = '0000-00-00' OR `$column` IS NULL";
            
            if ($conn->query($fixSql)) {
                $affected = $conn->affected_rows;
                echo "<p style='color: green;'>  ✅ تم إصلاح $affected قيمة</p>";
                $totalFixed += $affected;
            } else {
                echo "<p style='color: red;'>  ❌ خطأ: " . $conn->error . "</p>";
            }
        } else {
            echo "<p style='color: orange;'>  ⚠ العمود $column غير موجود</p>";
        }
    }
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>✅ تم الإصلاح!</h3>";
    echo "<p>تم إصلاح <strong>$totalFixed</strong> قيمة تاريخ مشكلة</p>";
    echo "<p><a href='Database.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>جرب فتح قاعدة البيانات الآن</a></p>";
    echo "</div>";
    
    // Test the fix
    echo "<h2>اختبار الإصلاح...</h2>";
    
    try {
        $testSql = "SELECT DISTINCT Startdate FROM databasehc WHERE Startdate IS NOT NULL AND Startdate != '' AND Startdate != '0000-00-00' ORDER BY Startdate LIMIT 5";
        $testResult = $conn->query($testSql);
        
        if ($testResult) {
            echo "<p style='color: green;'>✅ الاختبار نجح! عينة من التواريخ:</p>";
            echo "<ul>";
            while ($row = $testResult->fetch_assoc()) {
                echo "<li>" . htmlspecialchars($row['Startdate']) . "</li>";
            }
            echo "</ul>";
        } else {
            echo "<p style='color: orange;'>⚠ لا توجد تواريخ صحيحة</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في الاختبار: " . $e->getMessage() . "</p>";
    }
    
    $conn->close();
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background: #f8f9fa;
}

h1 {
    color: #2c3e50;
    text-align: center;
}

p, li {
    margin: 8px 0;
}

div {
    margin: 15px 0;
}
</style>
