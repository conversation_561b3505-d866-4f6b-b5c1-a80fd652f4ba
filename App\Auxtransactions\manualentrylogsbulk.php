<?php
session_start();
if (!isset($_SESSION['username'])) {
    header("Location: ../../Loginpage.php");
    exit();
}

$email = $_SESSION['username'];
include $_SERVER['DOCUMENT_ROOT'].'/config/Databasehost.php';

$access = $_SESSION['access_level'];
if ($access == 'Admin') {
    // Admin code
} elseif ($access == 'Leader') {
    header("Location: ../../Teamleader/LeaderHome.php");
    exit();
} elseif ($access == 'Member') {
    header("Location: ../../Memberpages/MemberHome.php");
    exit();
} else {
    echo "<div class='alert alert-danger'>Access level unknown. Please contact the system administrator.</div>";
    exit();
}

error_reporting(E_ALL);
ini_set('display_errors', 1);

require '../../Program/src/SimpleXLSX.php';
include $_SERVER['DOCUMENT_ROOT'].'/config/Databasehost.php';

use <PERSON><PERSON><PERSON>\SimpleXLSX;

$insertedData = [];
$rowsInsertedCount = 0;
$errorMessages = [];
$invalidRows = [];
$missingColumnsLog = [];
$emptyRows = 0;

if (isset($_POST['download_template'])) {
    $file_path = __DIR__ . '/../../Templates/Logs.xlsx';
    if (file_exists($file_path)) {
        header('Content-Description: File Transfer');
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . basename($file_path) . '"');
        header('Content-Length: ' . filesize($file_path));
        readfile($file_path);
        exit;
    } else {
        $_SESSION['error_message'] = "Template file not found. Please contact administrator.";
    }
}

if (isset($_POST['submit'])) {
    if (empty($_FILES['excel_file']['tmp_name'])) {
        $_SESSION['error_message'] = "No file selected. Please choose a file to upload.";
    } else {
        $file = $_FILES['excel_file']['tmp_name'];
        $original_filename = $_FILES['excel_file']['name'];
        
        // Validate file type
        $file_type = $_FILES['excel_file']['type'];
        $allowed_types = [
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-excel'
        ];
        
        if (!in_array($file_type, $allowed_types)) {
            $_SESSION['error_message'] = "Invalid file type. Please upload an Excel file (.xlsx or .xls).";
        } elseif ($xlsx = SimpleXLSX::parse($file)) {
            $stmt = $conn->prepare("INSERT INTO interpretersaux (Email, AuxName, Duration, Starttime, Endtime) VALUES (?, ?, ?, ?, ?)");
            
            if ($stmt === false) {
                $_SESSION['error_message'] = "Database error: " . $conn->error;
            } else {
                $required_columns = ['Email', 'AuxName', 'Duration', 'Starttime', 'Endtime'];
                $rows = $xlsx->rows();
                $headerRow = isset($rows[0]) ? $rows[0] : [];
                
                // Validate file has data
                if (count($rows) < 2) {
                    $_SESSION['error_message'] = "The file contains no data rows. Please check your file.";
                } else {
                    // Validate column headers
                    $column_errors = [];
                    foreach ($required_columns as $index => $col) {
                        if (!isset($headerRow[$index]) || trim($headerRow[$index]) != $col) {
                            $column_errors[] = "Column " . ($index+1) . " should be '$col' but found '" . 
                                            (isset($headerRow[$index])) ? $headerRow[$index] : 'empty' . "'";
                        }
                    }
                    
                    if (!empty($column_errors)) {
                        $_SESSION['error_message'] = "Invalid file format:<br>" . implode("<br>", $column_errors);
                    } else {
                        foreach ($rows as $index => $row) {
                            if ($index === 0) continue; // Skip header row
                            
                            // Skip empty rows
                            if (empty(array_filter($row))) {
                                $emptyRows++;
                                continue;
                            }
                            
                            // Validate row data
                            $row_errors = [];
                            if (count($row) != 5) {
                                $missingColumns = 5 - count($row);
                                $missingColumnsLog[] = "Row $index: Missing $missingColumns column(s)";
                                continue;
                            }
                            
                            // Trim all values
                            $row = array_map('trim', $row);
                            
                            // Validate email format
                            if (empty($row[0])) {
                                $row_errors[] = "Email cannot be empty";
                            } elseif (!filter_var($row[0], FILTER_VALIDATE_EMAIL)) {
                                $row_errors[] = "Invalid email format";
                            }
                            
                            // Validate AuxName not empty
                            if (empty($row[1])) {
                                $row_errors[] = "Aux Name cannot be empty";
                            }
                            
                            // Validate duration is numeric and positive
                            if (!is_numeric($row[2])) {
                                $row_errors[] = "Duration must be a number";
                            } elseif ($row[2] < 0) {
                                $row_errors[] = "Duration cannot be negative";
                            }
                            
                            // Validate time formats
                            if (!preg_match('/^\d{2}:\d{2}:\d{2}$/', $row[3])) {
                                $row_errors[] = "Start time format should be HH:MM:SS";
                            }
                            
                            if (!preg_match('/^\d{2}:\d{2}:\d{2}$/', $row[4])) {
                                $row_errors[] = "End time format should be HH:MM:SS";
                            }
                            
                            if (!empty($row_errors)) {
                                $invalidRows[$index] = [
                                    'data' => $row,
                                    'errors' => $row_errors
                                ];
                                continue;
                            }
                            
                            // Insert valid row
                            $stmt->bind_param("sssss", ...$row);
                            $stmt->execute();
                            
                            if ($stmt->affected_rows > 0) {
                                $insertedData[] = $row;
                                $rowsInsertedCount++;
                            }
                        }
                        
                        // Prepare success/error messages
                        if ($rowsInsertedCount > 0) {
                            $_SESSION['success_message'] = "Successfully inserted $rowsInsertedCount records from file: $original_filename";
                        }
                        
                        if (!empty($invalidRows)) {
                            $errorMessages[] = count($invalidRows) . " rows contained errors and were skipped.";
                        }
                        
                        if (!empty($missingColumnsLog)) {
                            $errorMessages[] = count($missingColumnsLog) . " rows had missing columns.";
                        }
                        
                        if ($emptyRows > 0) {
                            $errorMessages[] = "$emptyRows empty rows were skipped.";
                        }
                    }
                }
            }
            $stmt->close();
        } else {
            $_SESSION['error_message'] = "Error reading file: " . SimpleXLSX::parseError();
        }
    }
}
$conn->close();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload Logs</title>
    
    <!-- Google Fonts -->
    <link href="<!-- Google Fonts replaced with system fonts for CSP compliance -->" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Shared CSS -->
        <link rel="stylesheet" href="../assets/css/schedulestyle.css">
    
    <!-- Additional Styles for Logs Page -->
    <style>
        .logs-container {
            max-width: 1000px;
            margin: 2rem auto;
        }
        
        .validation-summary {
            background-color: rgba(255, 152, 0, 0.1);
            border-left: 4px solid var(--warning-color);
            padding: 1rem;
            margin-bottom: 1.5rem;
            border-radius: var(--border-radius);
        }
        
        .error-badge {
            display: inline-block;
            background-color: var(--danger-color);
            color: white;
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            margin-right: 0.5rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .stat-card {
            background-color: var(--secondary-color);
            padding: 0.75rem;
            border-radius: var(--border-radius);
            text-align: center;
        }
        
        .stat-value {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--primary-color);
        }
        
        .stat-label {
            font-size: 0.8rem;
            color: var(--text-medium);
        }
        
        .error-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 0.5rem;
            font-size: 0.85rem;
        }
        
        .error-table th {
            background-color: var(--primary-light);
            padding: 0.5rem;
            text-align: left;
        }
        
        .error-table td {
            padding: 0.5rem;
            border-bottom: 1px solid var(--border-color);
        }
        
        .error-table tr:hover td {
            background-color: rgba(244, 67, 54, 0.05);
        }
    </style>
</head>
<body>
    <div class="main-container logs-container">
        <div class="card">
            <div class="card-header">
                <h1 class="card-title">
                    <i class="fas fa-file-upload"></i>Upload Interpreter Logs
                </h1>
            </div>
            <div class="card-body">
                <!-- Alert Messages -->
                <?php if (isset($_SESSION['success_message'])): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <div class="alert-content">
                            <div class="alert-title">Success!</div>
                            <?= $_SESSION['success_message'] ?>
                            
                            <?php if (!empty($insertedData)): ?>
                                <div class="stats-grid">
                                    <div class="stat-card">
                                        <div class="stat-value"><?= count($insertedData) ?></div>
                                        <div class="stat-label">Valid Rows</div>
                                    </div>
                                    <?php if (!empty($invalidRows)): ?>
                                    <div class="stat-card">
                                        <div class="stat-value"><?= count($invalidRows) ?></div>
                                        <div class="stat-label">Invalid Rows</div>
                                    </div>
                                    <?php endif; ?>
                                    <?php if (!empty($missingColumnsLog)): ?>
                                    <div class="stat-card">
                                        <div class="stat-value"><?= count($missingColumnsLog) ?></div>
                                        <div class="stat-label">Incomplete Rows</div>
                                    </div>
                                    <?php endif; ?>
                                    <?php if ($emptyRows > 0): ?>
                                    <div class="stat-card">
                                        <div class="stat-value"><?= $emptyRows ?></div>
                                        <div class="stat-label">Empty Rows</div>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php unset($_SESSION['success_message']); ?>
                <?php endif; ?>

                <?php if (isset($_SESSION['error_message'])): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle"></i>
                        <div class="alert-content">
                            <div class="alert-title">Upload Failed</div>
                            <?= $_SESSION['error_message'] ?>
                        </div>
                    </div>
                    <?php unset($_SESSION['error_message']); ?>
                <?php endif; ?>
                
                <div class="d-flex justify-content-end mb-3">
                    <form method="POST">
                        <button type="submit" name="download_template" class="btn template-btn">
                            <i class="fas fa-file-excel me-1"></i>Download Template
                        </button>
                    </form>
                </div>
                
                <form method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                    <div class="drag-container" id="dragArea">
                        <div class="upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <h4>Drag & Drop your Excel file here</h4>
                        <p class="text-muted my-2">or</p>
                        <label class="btn btn-primary">
                            <i class="fas fa-folder-open me-1"></i>Browse Files
                            <input type="file" name="excel_file" class="file-input" id="fileInput" required>
                        </label>
                        <div class="file-info" id="fileName"></div>
                        <div class="invalid-feedback">Please select an Excel file</div>
                    </div>
                    
                    <div class="d-grid mt-3">
                        <button type="submit" name="submit" class="btn btn-primary">
                            <i class="fas fa-upload me-1"></i>Upload Logs
                        </button>
                    </div>
                </form>
                
                <?php if (!empty($errorMessages)): ?>
                    <div class="validation-summary mt-4">
                        <h4><i class="fas fa-exclamation-triangle me-2"></i>Validation Summary</h4>
                        <?php foreach ($errorMessages as $message): ?>
                            <p><?= $message ?></p>
                        <?php endforeach; ?>
                        
                        <?php if (!empty($invalidRows)): ?>
                            <div class="error-details mt-3">
                                <h5>Detailed error report:</h5>
                                <div class="table-container">
                                    <table class="error-table">
                                        <thead>
                                            <tr>
                                                <th>Row #</th>
                                                <th>Data Preview</th>
                                                <th>Validation Errors</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($invalidRows as $rowNum => $rowData): ?>
                                                <tr>
                                                    <td><?= $rowNum + 1 ?></td>
                                                    <td>
                                                        <?= implode(', ', array_map(function($item) { 
                                                            return htmlspecialchars(substr($item, 0, 20)) . (strlen($item) > 20 ? '...' : ''); 
                                                        }, $rowData['data'])) ?>
                                                    </td>
                                                    <td>
                                                        <?php foreach ($rowData['errors'] as $error): ?>
                                                            <div style="margin-bottom: 5px;">
                                                                <span class="error-badge">Error</span><?= $error ?>
                                                            </div>
                                                        <?php endforeach; ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <?php if (!empty($insertedData)): ?>
        <div class="card mt-4">
            <div class="card-body">
                <h2 class="section-title">
                    <i class="fas fa-table"></i> Successfully Uploaded Data
                </h2>
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Email</th>
                                <th>Aux Name</th>
                                <th>Duration</th>
                                <th>Start Time</th>
                                <th>End Time</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($insertedData as $row): ?>
                            <tr>
                                <?php foreach ($row as $cell): ?>
                                <td><?= htmlspecialchars($cell) ?></td>
                                <?php endforeach; ?>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    
    <!-- Shared JavaScript -->
    <script src="../../js/admin-upload.js"></script>
    
    <!-- Additional Script for Logs Page -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // File size validation
            const fileInput = document.getElementById('fileInput');
            if (fileInput) {
                fileInput.addEventListener('change', function() {
                    if (this.files && this.files[0]) {
                        const fileSize = this.files[0].size / 1024 / 1024; // in MB
                        if (fileSize > 5) {
                            alert('File size exceeds 5MB limit. Please choose a smaller file.');
                            this.value = '';
                            document.getElementById('fileName').textContent = '';
                        }
                    }
                });
            }
            
            // Highlight row on hover in error table
            const errorTable = document.querySelector('.error-table');
            if (errorTable) {
                const rows = errorTable.querySelectorAll('tbody tr');
                rows.forEach(row => {
                    row.addEventListener('mouseenter', function() {
                        this.style.backgroundColor = 'rgba(244, 67, 54, 0.05)';
                    });
                    row.addEventListener('mouseleave', function() {
                        this.style.backgroundColor = '';
                    });
                });
            }
        });
    </script>
</body>
</html>