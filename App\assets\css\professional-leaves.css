/* ==================== PROFESSIONAL LEAVES MANAGEMENT CSS ==================== */

/* Import Root Variables */
@import url('root-variables.css');

/* ==================== GLOBAL STYLES ==================== */
.professional-body {
    font-family: 'Inter', 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, var(--primary-50) 0%, var(--secondary-100) 100%);
    color: var(--text-primary);
    margin: 0;
    padding: 0;
    min-height: 100vh;
    overflow-x: hidden;
    line-height: 1.6;
    font-size: 14px;
    font-weight: 400;
}

/* ==================== PROFESSIONAL NAVBAR ==================== */
.professional-navbar {
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 50%, var(--primary-800) 100%);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    border-bottom: 3px solid var(--primary-500);
    position: sticky;
    top: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
    padding: 0;
}

.navbar-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 2rem;
    max-width: 100%;
}

/* Logo Section */
.navbar-brand-section {
    display: flex;
    align-items: center;
    flex: 0 0 auto;
}

.logo-container {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.professional-logo {
    height: 50px;
    width: auto;
    border-radius: var(--radius-lg);
    box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
    transition: var(--transition-normal);
}

.professional-logo:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(255, 255, 255, 0.3);
}

.brand-text {
    display: flex;
    flex-direction: column;
}

.brand-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-white);
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.brand-subtitle {
    font-size: 0.85rem;
    color: var(--primary-200);
    font-weight: 400;
    margin-top: -2px;
}

/* Page Title Section */
.page-title-section {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
    justify-content: center;
}

.page-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--accent-main), var(--accent-dark));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 16px rgba(123, 109, 199, 0.4);
}

.page-icon i {
    font-size: 1.8rem;
    color: var(--text-white);
}

.page-info {
    text-align: center;
}

.page-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-white);
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.page-description {
    font-size: 0.9rem;
    color: var(--primary-200);
    font-weight: 400;
}

/* User Info Section */
.user-info-section {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    flex: 0 0 auto;
}

.user-stats {
    display: flex;
    gap: 1rem;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-item i {
    font-size: 1.2rem;
    color: var(--primary-200);
    margin-bottom: 0.25rem;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-white);
    line-height: 1;
}

.stat-label {
    font-size: 0.75rem;
    color: var(--primary-200);
    margin: 0;
}

.user-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    background: linear-gradient(135deg, var(--success-500), var(--success-700));
    border-radius: 25px;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
    position: relative;
}

.badge-text {
    color: var(--text-white);
    font-weight: 600;
    font-size: 0.9rem;
}

.badge-indicator {
    width: 8px;
    height: 8px;
    background: var(--text-white);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* ==================== MAIN CONTAINER ==================== */
.professional-container {
    padding: 2rem 0;
    min-height: calc(100vh - 120px);
}

/* ==================== FILTERS PANEL ==================== */
.professional-filters-panel {
    background: var(--bg-white);
    border-radius: var(--radius-xl);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
    overflow: hidden;
    border: 1px solid var(--border-light);
}

.filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    background: linear-gradient(135deg, var(--primary-100), var(--primary-200));
    border-bottom: 1px solid var(--border-light);
}

.filters-title {
    display: flex;
    flex-direction: column;
}

.filters-title i {
    font-size: 1.5rem;
    color: var(--primary-600);
    margin-bottom: 0.5rem;
}

.filters-title h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.filters-subtitle {
    font-size: 0.85rem;
    color: var(--text-secondary);
    margin-top: 0.25rem;
}

.btn-toggle-filters {
    background: var(--primary-500);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-white);
    transition: var(--transition-normal);
    box-shadow: 0 4px 12px rgba(158, 144, 215, 0.3);
}

.btn-toggle-filters:hover {
    background: var(--primary-600);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(158, 144, 215, 0.4);
}

.filters-form {
    padding: 2rem;
}

.filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.search-group {
    grid-column: 1 / -1;
}

.filter-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.75rem;
    font-size: 0.9rem;
}

.filter-label i {
    color: var(--primary-500);
    font-size: 1rem;
}

/* Search Input */
.search-input-container {
    position: relative;
}

.professional-search-input {
    width: 100%;
    padding: 0.875rem 1rem 0.875rem 3rem;
    border: 2px solid var(--border-light);
    border-radius: var(--radius-lg);
    font-size: 0.9rem;
    background: var(--bg-white);
    transition: var(--transition-normal);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.professional-search-input:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(158, 144, 215, 0.15);
    background: var(--primary-50);
}

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-tertiary);
    font-size: 1.1rem;
}

/* Professional Select */
.professional-select {
    padding: 0.875rem 1rem;
    border: 2px solid var(--border-light);
    border-radius: var(--radius-lg);
    font-size: 0.9rem;
    background: var(--bg-white);
    transition: var(--transition-normal);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.professional-select:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(158, 144, 215, 0.15);
}

/* Date Range */
.date-range-group {
    grid-column: span 2;
}

.date-range-inputs {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.professional-date-input {
    flex: 1;
    padding: 0.875rem 1rem;
    border: 2px solid var(--border-light);
    border-radius: var(--radius-lg);
    font-size: 0.9rem;
    background: var(--bg-white);
    transition: var(--transition-normal);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.professional-date-input:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(158, 144, 215, 0.15);
}

.date-separator {
    font-weight: 600;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Filter Actions */
.filters-actions-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-light);
}

.filters-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.filters-info i {
    color: var(--primary-500);
}

.filters-buttons {
    display: flex;
    gap: 1rem;
}

.btn-apply-filters,
.btn-reset-filters {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: 0.9rem;
    text-decoration: none;
    transition: var(--transition-normal);
    border: none;
    cursor: pointer;
}

.btn-apply-filters {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    color: var(--text-white);
    box-shadow: 0 4px 12px rgba(158, 144, 215, 0.3);
}

.btn-apply-filters:hover {
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(158, 144, 215, 0.4);
    color: var(--text-white);
}

.btn-reset-filters {
    background: var(--bg-white);
    color: var(--text-secondary);
    border: 2px solid var(--border-medium);
}

.btn-reset-filters:hover {
    background: var(--gray-100);
    color: var(--text-primary);
    border-color: var(--border-dark);
    text-decoration: none;
}

/* ==================== RESPONSIVE DESIGN ==================== */
@media (max-width: 1200px) {
    .navbar-content {
        padding: 1rem;
    }

    .page-title {
        font-size: 1.75rem;
    }

    .filters-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .navbar-content {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
    }

    .page-title-section {
        order: -1;
    }

    .user-info-section {
        flex-direction: column;
        gap: 1rem;
    }

    .page-title {
        font-size: 1.5rem;
    }

    .filters-grid {
        grid-template-columns: 1fr;
    }

    .date-range-group {
        grid-column: span 1;
    }

    .date-range-inputs {
        flex-direction: column;
    }

    .filters-actions-bar {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .filters-buttons {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .professional-container {
        padding: 1rem 0;
    }

    .filters-form {
        padding: 1rem;
    }

    .filters-header {
        padding: 1rem;
    }

    .logo-container {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .brand-title {
        font-size: 1.25rem;
    }
}

/* ==================== PROFESSIONAL TABLE ==================== */
.professional-table-container {
    background: var(--bg-white);
    border-radius: 0;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    border: none;
    margin: 0;
    width: 100%;
    position: relative;
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    border-bottom: 1px solid var(--border-light);
    position: sticky;
    top: 0;
    z-index: 1000;
    color: var(--text-white);
}

.table-title {
    display: flex;
    flex-direction: column;
}

.table-title i {
    font-size: 1.5rem;
    color: var(--text-white);
    margin-bottom: 0.5rem;
}

.table-title h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-white);
    margin: 0;
}

.table-subtitle {
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.9);
    margin-top: 0.25rem;
}

.table-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-table-action {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-normal);
    cursor: pointer;
    font-size: 1rem;
}

.btn-table-action:nth-child(1) {
    background: linear-gradient(135deg, var(--success-500), var(--success-700));
    color: var(--text-white);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.btn-table-action:nth-child(2) {
    background: linear-gradient(135deg, var(--info-500), var(--info-700));
    color: var(--text-white);
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
}

.btn-table-action:nth-child(3) {
    background: linear-gradient(135deg, var(--warning-500), var(--warning-700));
    color: var(--text-white);
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
}

.btn-table-action:nth-child(4) {
    background: linear-gradient(135deg, var(--gray-500), var(--gray-700));
    color: var(--text-white);
    box-shadow: 0 4px 12px rgba(117, 117, 117, 0.3);
}

.btn-table-action:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

/* Table Wrapper */
.table-wrapper {
    overflow-x: auto;
    overflow-y: auto;
    height: calc(100vh - 300px);
    border: none;
    border-radius: 0;
    position: relative;
    width: 100%;
}

.professional-table {
    width: 100%;
    min-width: 2000px;
    border-collapse: separate;
    border-spacing: 0;
    font-size: 0.85rem;
    margin: 0;
    table-layout: fixed;
    background: var(--bg-white);
}

/* تحديد عرض محدد لكل عمود */
.professional-table .th-code,
.professional-table .td-code {
    width: 120px;
    min-width: 120px;
}

.professional-table .th-email,
.professional-table .td-email {
    width: 200px;
    min-width: 200px;
}

.professional-table .th-date,
.professional-table .td-date {
    width: 120px;
    min-width: 120px;
}

.professional-table .th-days,
.professional-table .td-days {
    width: 80px;
    min-width: 80px;
}

.professional-table .th-status,
.professional-table .td-status {
    width: 120px;
    min-width: 120px;
}

.professional-table .th-approver,
.professional-table .td-approver {
    width: 150px;
    min-width: 150px;
}

.professional-table .th-comment,
.professional-table .td-comment {
    width: 200px;
    min-width: 200px;
}

.professional-table .th-request-date,
.professional-table .td-request-date {
    width: 120px;
    min-width: 120px;
}

.professional-table .th-leave-type,
.professional-table .td-leave-type {
    width: 130px;
    min-width: 130px;
}

.professional-table .th-attachment,
.professional-table .td-attachment {
    width: 100px;
    min-width: 100px;
}

.professional-table .th-description,
.professional-table .td-description {
    width: 150px;
    min-width: 150px;
}

.professional-table .th-seat,
.professional-table .td-seat {
    width: 80px;
    min-width: 80px;
}

.professional-table .th-language,
.professional-table .td-language {
    width: 100px;
    min-width: 100px;
}

.professional-table .th-module,
.professional-table .td-module {
    width: 100px;
    min-width: 100px;
}

.professional-table .th-shift,
.professional-table .td-shift {
    width: 120px;
    min-width: 120px;
}

.professional-table .th-leader,
.professional-table .td-leader {
    width: 130px;
    min-width: 130px;
}

.professional-table .th-leader-email,
.professional-table .td-leader-email {
    width: 180px;
    min-width: 180px;
}

.professional-table .th-actions,
.professional-table .td-actions {
    width: 200px;
    min-width: 200px;
}

/* Table Header */
.professional-thead {
    background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
    position: sticky;
    top: 0;
    z-index: 999;
}

.professional-thead th {
    padding: 0;
    border: none;
    position: relative;
}

.th-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 0.75rem;
    color: var(--text-white);
    font-weight: 600;
    font-size: 0.8rem;
    text-align: left;
    white-space: nowrap;
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    overflow: hidden;
    text-overflow: ellipsis;
}

.th-content i {
    font-size: 0.9rem;
    opacity: 0.8;
}

.th-content span {
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Table Body */
.professional-tbody {
    background: var(--bg-white);
}

.professional-row {
    transition: var(--transition-normal);
    border-bottom: 1px solid var(--border-light);
}

.professional-row:hover {
    background: linear-gradient(135deg, var(--primary-50), var(--secondary-100));
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.professional-row td {
    padding: 0;
    border: none;
    vertical-align: middle;
}

.cell-content {
    padding: 1rem 0.75rem;
    border-right: 1px solid var(--border-light);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* استثناءات للخلايا التي تحتاج عرض متعدد الأسطر */
.td-comment .cell-content,
.td-description .cell-content {
    white-space: normal;
    word-wrap: break-word;
}

/* شريط التمرير المخصص */
.table-wrapper::-webkit-scrollbar {
    height: 12px;
    width: 12px;
}

.table-wrapper::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: 6px;
}

.table-wrapper::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    border-radius: 6px;
    border: 2px solid var(--gray-100);
}

.table-wrapper::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
}

.table-wrapper::-webkit-scrollbar-corner {
    background: var(--gray-100);
}

/* Specific Cell Styles */
.td-code .code-badge {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    color: var(--text-white);
    padding: 0.375rem 0.75rem;
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: 0.8rem;
    box-shadow: 0 2px 8px rgba(158, 144, 215, 0.3);
}

.td-email .email-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.email-icon {
    color: var(--primary-500);
    font-size: 1.1rem;
}

.email-text {
    color: var(--text-primary);
    font-weight: 500;
}

.td-date .date-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.date-icon {
    color: var(--accent-main);
    font-size: 0.9rem;
}

.date-text {
    color: var(--text-primary);
    font-weight: 500;
    font-size: 0.85rem;
}

.td-days .days-badge {
    display: flex;
    align-items: center;
    gap: 0.375rem;
    background: var(--warning-50);
    color: var(--warning-700);
    padding: 0.375rem 0.75rem;
    border-radius: var(--radius-lg);
    font-weight: 600;
    border: 1px solid var(--warning-200);
}

/* Status Badges */
.status-badge {
    display: flex;
    align-items: center;
    gap: 0.375rem;
    padding: 0.5rem 0.875rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-success {
    background: linear-gradient(135deg, var(--success-500), var(--success-700));
    color: var(--text-white);
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

.badge-danger {
    background: linear-gradient(135deg, var(--danger-500), var(--danger-700));
    color: var(--text-white);
    box-shadow: 0 2px 8px rgba(244, 67, 54, 0.3);
}

.badge-warning {
    background: linear-gradient(135deg, var(--warning-500), var(--warning-700));
    color: var(--text-white);
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
}

/* Approver Info */
.approver-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.approver-icon {
    color: var(--success-500);
    font-size: 1rem;
}

.approver-name {
    color: var(--text-primary);
    font-weight: 500;
}

.pending-icon {
    color: var(--warning-500);
    font-size: 1rem;
}

.pending-text {
    color: var(--text-secondary);
    font-style: italic;
}

/* Comment Input */
.comment-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.professional-comment-input {
    flex: 1;
    padding: 0.5rem 0.75rem;
    border: 2px solid var(--border-light);
    border-radius: var(--radius-md);
    font-size: 0.8rem;
    background: var(--bg-white);
    transition: var(--transition-normal);
    min-width: 150px;
}

.professional-comment-input:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 2px rgba(158, 144, 215, 0.15);
    background: var(--primary-50);
}

.btn-save-comment {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: none;
    background: linear-gradient(135deg, var(--success-500), var(--success-600));
    color: var(--text-white);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-normal);
    font-size: 0.8rem;
}

.btn-save-comment:hover {
    background: linear-gradient(135deg, var(--success-600), var(--success-700));
    transform: scale(1.1);
}

/* Leave Type Badge */
.leave-type-badge {
    background: linear-gradient(135deg, var(--info-100), var(--info-200));
    color: var(--info-700);
    padding: 0.375rem 0.75rem;
    border-radius: var(--radius-lg);
    font-weight: 500;
    font-size: 0.8rem;
    border: 1px solid var(--info-300);
}

/* Attachment Link */
.attachment-link {
    display: flex;
    align-items: center;
    gap: 0.375rem;
    color: var(--primary-500);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.8rem;
    transition: var(--transition-normal);
}

.attachment-link:hover {
    color: var(--primary-700);
    text-decoration: none;
}

.no-attachment {
    display: flex;
    align-items: center;
    gap: 0.375rem;
    color: var(--text-tertiary);
    font-size: 0.8rem;
}

/* Description */
.description-text {
    display: flex;
    align-items: center;
    gap: 0.375rem;
    color: var(--text-primary);
    font-size: 0.8rem;
}

.no-description {
    display: flex;
    align-items: center;
    gap: 0.375rem;
    color: var(--text-tertiary);
    font-size: 0.8rem;
}

/* Info Text */
.info-text {
    color: var(--text-secondary);
    font-size: 0.8rem;
    font-weight: 400;
}

/* Action Buttons */
.actions-container {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.btn-action {
    display: flex;
    align-items: center;
    gap: 0.375rem;
    padding: 0.5rem 0.875rem;
    border: none;
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    cursor: pointer;
    transition: var(--transition-normal);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.btn-approve {
    background: linear-gradient(135deg, var(--success-500), var(--success-600));
    color: var(--text-white);
}

.btn-approve:hover {
    background: linear-gradient(135deg, var(--success-600), var(--success-700));
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.btn-reject {
    background: linear-gradient(135deg, var(--danger-500), var(--danger-600));
    color: var(--text-white);
}

.btn-reject:hover {
    background: linear-gradient(135deg, var(--danger-600), var(--danger-700));
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
}

.btn-action:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.action-loading {
    color: var(--primary-500);
    font-size: 1rem;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* No Data Row */
.no-data-row {
    background: var(--bg-white);
}

.no-data-cell {
    text-align: center;
    padding: 3rem 2rem;
}

.no-data-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    color: var(--text-tertiary);
}

.no-data-content i {
    font-size: 3rem;
    color: var(--gray-400);
}

.no-data-content h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-secondary);
    margin: 0;
}

.no-data-content p {
    font-size: 0.9rem;
    color: var(--text-tertiary);
    margin: 0;
}

/* ==================== STATISTICS PANEL ==================== */
.professional-stats-panel {
    background: var(--bg-white);
    border-radius: var(--radius-xl);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    padding: 2rem;
    margin-bottom: 2rem;
    border: 1px solid var(--border-light);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.stat-card {
    background: linear-gradient(135deg, var(--bg-white), var(--secondary-100));
    border-radius: var(--radius-xl);
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border-light);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-500), var(--accent-main));
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--text-white);
    flex-shrink: 0;
}

.stat-total .stat-icon {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    box-shadow: 0 4px 12px rgba(158, 144, 215, 0.3);
}

.stat-approved .stat-icon {
    background: linear-gradient(135deg, var(--success-500), var(--success-600));
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.stat-rejected .stat-icon {
    background: linear-gradient(135deg, var(--danger-500), var(--danger-600));
    box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
}

.stat-pending .stat-icon {
    background: linear-gradient(135deg, var(--warning-500), var(--warning-600));
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    line-height: 1;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
    margin: 0.25rem 0 0 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* ==================== FLOATING ACTIONS ==================== */
.professional-floating-actions {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    z-index: 1000;
}

.floating-action-group {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.floating-btn {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: var(--text-white);
    cursor: pointer;
    transition: var(--transition-normal);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.floating-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: inherit;
    opacity: 0.8;
    transition: var(--transition-normal);
}

.floating-btn:hover::before {
    opacity: 1;
}

.floating-btn:hover {
    transform: translateY(-4px) scale(1.05);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.floating-btn i {
    position: relative;
    z-index: 1;
}

.floating-btn-primary {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
}

.floating-btn-success {
    background: linear-gradient(135deg, var(--success-500), var(--success-600));
}

.floating-btn-info {
    background: linear-gradient(135deg, var(--info-500), var(--info-600));
}

.floating-btn-warning {
    background: linear-gradient(135deg, var(--warning-500), var(--warning-600));
}

/* ==================== RESPONSIVE TABLE ==================== */
@media (max-width: 1600px) {
    .professional-table {
        min-width: 1800px;
    }
}

@media (max-width: 1400px) {
    .professional-table {
        min-width: 1600px;
    }

    .th-content {
        padding: 0.875rem 0.5rem;
        font-size: 0.75rem;
    }

    .cell-content {
        padding: 0.875rem 0.5rem;
    }

    /* تقليل عرض الأعمدة للشاشات الأصغر */
    .professional-table .th-email,
    .professional-table .td-email {
        width: 180px;
        min-width: 180px;
    }

    .professional-table .th-comment,
    .professional-table .td-comment {
        width: 180px;
        min-width: 180px;
    }

    .professional-table .th-leader-email,
    .professional-table .td-leader-email {
        width: 160px;
        min-width: 160px;
    }
}

@media (max-width: 1200px) {
    .table-header {
        padding: 1rem 1.5rem;
    }

    .table-title h3 {
        font-size: 1.1rem;
    }

    .table-subtitle {
        font-size: 0.8rem;
    }

    .btn-table-action {
        width: 36px;
        height: 36px;
        font-size: 0.9rem;
    }
}

@media (max-width: 768px) {
    .professional-table-container {
        margin: 0 -1rem;
        border-radius: 0;
    }

    .table-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
        padding: 1rem;
    }

    .table-actions {
        align-self: stretch;
        justify-content: center;
    }

    .table-wrapper {
        max-height: 60vh;
    }

    .professional-table {
        min-width: 1200px; /* الحد الأدنى للهواتف */
        font-size: 0.75rem;
    }

    .th-content {
        padding: 0.75rem 0.4rem;
        font-size: 0.7rem;
    }

    .cell-content {
        padding: 0.75rem 0.4rem;
    }

    .btn-action {
        padding: 0.375rem 0.5rem;
        font-size: 0.65rem;
    }

    .professional-comment-input {
        min-width: 100px;
        font-size: 0.7rem;
    }

    /* تقليل عرض الأعمدة أكثر للهواتف */
    .professional-table .th-email,
    .professional-table .td-email {
        width: 150px;
        min-width: 150px;
    }

    .professional-table .th-comment,
    .professional-table .td-comment {
        width: 150px;
        min-width: 150px;
    }

    .professional-table .th-description,
    .professional-table .td-description {
        width: 120px;
        min-width: 120px;
    }

    .professional-table .th-actions,
    .professional-table .td-actions {
        width: 160px;
        min-width: 160px;
    }
}

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .stat-card {
        padding: 1rem;
    }

    .stat-icon {
        width: 48px;
        height: 48px;
        font-size: 1.25rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .floating-btn {
        width: 48px;
        height: 48px;
        font-size: 1.1rem;
    }

    .professional-floating-actions {
        bottom: 1rem;
        right: 1rem;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .stat-card {
        padding: 0.875rem;
    }

    .professional-table {
        min-width: 1000px; /* حد أدنى للهواتف الصغيرة */
    }

    .floating-action-group {
        gap: 0.5rem;
    }

    .floating-btn {
        width: 44px;
        height: 44px;
        font-size: 1rem;
    }

    .th-content {
        padding: 0.5rem 0.3rem;
        font-size: 0.65rem;
    }

    .cell-content {
        padding: 0.5rem 0.3rem;
    }
}

/* ==================== PRINT STYLES ==================== */
@media print {
    .professional-navbar,
    .professional-filters-panel,
    .professional-floating-actions,
    .table-actions,
    .btn-table-action,
    .btn-action,
    .btn-save-comment,
    .professional-stats-panel {
        display: none !important;
    }

    .professional-body {
        background: white !important;
        font-size: 10pt;
    }

    .professional-container {
        padding: 0;
    }

    .professional-table-container {
        box-shadow: none;
        border: 1px solid #000;
        margin: 0;
    }

    .table-header {
        background: white !important;
        border-bottom: 2px solid #000;
        padding: 1rem;
    }

    .table-title h3 {
        color: #000 !important;
    }

    .professional-table {
        min-width: auto;
        width: 100%;
    }

    .professional-thead {
        background: #f0f0f0 !important;
    }

    .th-content {
        color: #000 !important;
        border-right: 1px solid #000;
        padding: 0.5rem 0.25rem;
        font-size: 8pt;
    }

    .cell-content {
        border-right: 1px solid #000;
        padding: 0.5rem 0.25rem;
        font-size: 8pt;
    }

    .professional-row {
        border-bottom: 1px solid #000;
    }

    .professional-row:hover {
        background: white !important;
        transform: none !important;
        box-shadow: none !important;
    }

    .code-badge,
    .status-badge,
    .days-badge,
    .leave-type-badge {
        background: white !important;
        color: #000 !important;
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
}

/* ==================== ACCESSIBILITY ==================== */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus styles for accessibility */
.btn-action:focus,
.btn-table-action:focus,
.floating-btn:focus,
.professional-search-input:focus,
.professional-select:focus,
.professional-date-input:focus,
.professional-comment-input:focus {
    outline: 3px solid var(--primary-500);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .professional-table {
        border: 2px solid;
    }

    .th-content,
    .cell-content {
        border: 1px solid;
    }

    .btn-action,
    .btn-table-action,
    .floating-btn {
        border: 2px solid;
    }
}

/* ==================== ADDITIONAL TABLE IMPROVEMENTS ==================== */
/* تحسين عرض الجدول على الشاشات الكبيرة */
@media (min-width: 1920px) {
    .professional-table {
        min-width: 100%;
    }

    .table-wrapper {
        overflow-x: visible;
    }
}

/* تحسين التمرير للأجهزة اللمسية */
@media (hover: none) and (pointer: coarse) {
    .table-wrapper {
        -webkit-overflow-scrolling: touch;
        scroll-behavior: smooth;
    }

    .table-wrapper::-webkit-scrollbar {
        height: 8px;
        width: 8px;
    }
}

/* تحسين الأداء للجداول الكبيرة */
.professional-table {
    will-change: scroll-position;
    transform: translateZ(0);
}

.professional-row {
    will-change: transform;
}

/* تحسين عرض النصوص الطويلة */
.cell-content[title] {
    cursor: help;
}

.cell-content[title]:hover {
    position: relative;
}

/* تحسين عرض الأزرار في الأعمدة الضيقة */
@media (max-width: 1200px) {
    .btn-action span {
        display: none;
    }

    .btn-action {
        width: 32px;
        height: 32px;
        padding: 0;
        justify-content: center;
    }

    .actions-container {
        gap: 0.25rem;
    }
}

/* تحسين عرض التعليقات */
.professional-comment-input:focus {
    min-width: 200px;
    z-index: 100;
    position: relative;
}

/* تحسين عرض الحالات */
.status-badge {
    min-width: 80px;
    justify-content: center;
}

/* تحسين عرض التواريخ */
.date-text {
    white-space: nowrap;
}

/* تحسين عرض الإيميلات */
.email-text {
    max-width: 180px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.email-text:hover {
    overflow: visible;
    white-space: normal;
    word-break: break-all;
}

/* تحسين عرض الوصف */
.description-text span {
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
}

/* تحسين عرض أسماء القادة */
.approver-name,
.info-text {
    max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
}

/* تحسين التمرير السلس */
.table-wrapper {
    scroll-behavior: smooth;
}

/* تحسين عرض الجدول للطباعة */
@media print {
    .professional-table {
        font-size: 8pt !important;
        min-width: auto !important;
    }

    .th-content,
    .cell-content {
        padding: 0.25rem 0.125rem !important;
        font-size: 7pt !important;
    }

    .email-text,
    .description-text span,
    .approver-name,
    .info-text {
        max-width: none !important;
        overflow: visible !important;
        white-space: normal !important;
    }
}

/* ==================== SIMPLE FULL WIDTH TABLE ==================== */
/* إزالة الكونتينر وجعل الجدول بعرض الصفحة */
.professional-table-container {
    width: 100vw;
    margin-left: calc(-50vw + 50%);
    position: relative;
}

/* تحسين شريط التمرير */
.table-wrapper::-webkit-scrollbar {
    height: 12px;
    width: 12px;
}

.table-wrapper::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: 6px;
}

.table-wrapper::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    border-radius: 6px;
    border: 2px solid var(--gray-100);
}

.table-wrapper::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
}

/* تحسين أزرار الرأس */
.btn-table-action {
    background: rgba(255, 255, 255, 0.2);
    color: var(--text-white);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-table-action:hover {
    background: rgba(255, 255, 255, 0.3);
    color: var(--text-white);
    transform: translateY(-2px);
}
