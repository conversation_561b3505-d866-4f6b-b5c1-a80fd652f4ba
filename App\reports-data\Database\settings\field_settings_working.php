<?php
/**
 * Working Field Settings - No permission issues
 */

session_start();
include $_SERVER['DOCUMENT_ROOT'].'/config/Databasehost.php';
include '../system/database_config.php';

// Check session and access
if (!isset($_SESSION['username']) || !isset($_SESSION['random_code'])) {
    header("Location: ../../../../Loginpage.php");
    exit();
}

$access = $_SESSION['access_level'];
$email = $_SESSION['username'];

// Working functions that use database storage
function loadFieldConfigurationDB() {
    global $conn;
    
    // Create table if not exists
    $conn->query("CREATE TABLE IF NOT EXISTS field_config_storage (
        id INT PRIMARY KEY AUTO_INCREMENT,
        config_key VARCHAR(255) UNIQUE,
        config_data TEXT,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");
    
    try {
        $stmt = $conn->prepare("SELECT config_data FROM field_config_storage WHERE config_key = ?");
        $key = "main_config";
        $stmt->bind_param("s", $key);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($row = $result->fetch_assoc()) {
            return json_decode($row['config_data'], true) ?: getDefaultConfig();
        }
    } catch (Exception $e) {
        error_log("Database config load error: " . $e->getMessage());
    }
    
    return getDefaultConfig();
}

function saveFieldConfigurationDB() {
    global $conn, $email;
    
    try {
        // Build config from POST data
        $config = [];
        
        if (isset($_POST['sections'])) {
            foreach ($_POST['sections'] as $sectionKey => $sectionData) {
                $config[$sectionKey] = [
                    'title' => $sectionData['title'] ?? ucwords(str_replace('_', ' ', $sectionKey)),
                    'icon' => $sectionData['icon'] ?? 'fa-cog',
                    'fields' => []
                ];
                
                if (isset($_POST['fields'][$sectionKey])) {
                    foreach ($_POST['fields'][$sectionKey] as $fieldName => $fieldData) {
                        $config[$sectionKey]['fields'][$fieldName] = [
                            'enabled' => isset($fieldData['enabled']) ? '1' : '0',
                            'type' => $fieldData['type'] ?? 'text',
                            'col' => $fieldData['col'] ?? '6',
                            'icon' => $fieldData['icon'] ?? 'fa-edit',
                            'source_type' => $fieldData['source_type'] ?? 'custom',
                            'options' => $fieldData['options'] ?? [],
                            'label' => $fieldData['label'] ?? ucwords(str_replace('_', ' ', $fieldName))
                        ];
                    }
                }
            }
        }
        
        $configJson = json_encode($config, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        
        $stmt = $conn->prepare("INSERT INTO field_config_storage (config_key, config_data) VALUES (?, ?) 
                               ON DUPLICATE KEY UPDATE config_data = VALUES(config_data)");
        $key = "main_config";
        $stmt->bind_param("ss", $key, $configJson);
        
        if ($stmt->execute()) {
            return ['success' => true, 'message' => 'Configuration saved successfully'];
        } else {
            return ['success' => false, 'message' => 'Failed to save configuration'];
        }
        
    } catch (Exception $e) {
        error_log("Save config error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Error saving configuration: ' . $e->getMessage()];
    }
}

function syncWithDatabaseDB() {
    global $conn, $email;
    
    try {
        $result = $conn->query("DESCRIBE databasehc");
        if (!$result) {
            return ['success' => false, 'message' => 'Failed to get database structure'];
        }
        
        $dbColumns = [];
        while ($row = $result->fetch_assoc()) {
            $dbColumns[] = $row['Field'];
        }
        
        $config = loadFieldConfigurationDB();
        
        // Get configured fields
        $configuredFields = [];
        foreach ($config as $section) {
            if (isset($section['fields'])) {
                $configuredFields = array_merge($configuredFields, array_keys($section['fields']));
            }
        }
        
        // Find new fields
        $newFields = array_diff($dbColumns, $configuredFields);
        $addedCount = 0;
        
        foreach ($newFields as $fieldName) {
            if (in_array($fieldName, ['ID', 'created_at', 'updated_at'])) continue;
            
            $section = 'additional';
            if (!isset($config[$section])) {
                $config[$section] = [
                    'title' => 'Additional Fields',
                    'icon' => 'fa-plus',
                    'fields' => []
                ];
            }
            
            $config[$section]['fields'][$fieldName] = [
                'enabled' => '1',
                'type' => 'text',
                'col' => '6',
                'icon' => 'fa-edit',
                'source_type' => 'database',
                'options' => [],
                'label' => ucwords(str_replace('_', ' ', $fieldName))
            ];
            
            $addedCount++;
        }
        
        // Save updated config
        $configJson = json_encode($config, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        $stmt = $conn->prepare("INSERT INTO field_config_storage (config_key, config_data) VALUES (?, ?) 
                               ON DUPLICATE KEY UPDATE config_data = VALUES(config_data)");
        $key = "main_config";
        $stmt->bind_param("ss", $key, $configJson);
        $stmt->execute();
        
        return ['success' => true, 'message' => "Successfully synced. Added $addedCount new fields."];
        
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Sync error: ' . $e->getMessage()];
    }
}

function bulkDeleteFieldsDB() {
    global $conn, $email;
    
    try {
        $selectedFields = $_POST['selected_fields'] ?? [];
        
        if (empty($selectedFields)) {
            return ['success' => false, 'message' => 'No fields selected'];
        }
        
        $config = loadFieldConfigurationDB();
        $deletedCount = 0;
        $deletedFields = [];
        
        foreach ($selectedFields as $fieldIdentifier) {
            $parts = explode('|', $fieldIdentifier);
            if (count($parts) !== 2) continue;
            
            $sectionKey = $parts[0];
            $fieldName = $parts[1];
            
            if (isset($config[$sectionKey]['fields'][$fieldName])) {
                unset($config[$sectionKey]['fields'][$fieldName]);
                $deletedFields[] = $fieldName;
                $deletedCount++;
                
                if (empty($config[$sectionKey]['fields'])) {
                    unset($config[$sectionKey]);
                }
            }
        }
        
        if ($deletedCount > 0) {
            $configJson = json_encode($config, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            $stmt = $conn->prepare("INSERT INTO field_config_storage (config_key, config_data) VALUES (?, ?) 
                                   ON DUPLICATE KEY UPDATE config_data = VALUES(config_data)");
            $key = "main_config";
            $stmt->bind_param("ss", $key, $configJson);
            $stmt->execute();
            
            return ['success' => true, 'message' => "Successfully deleted $deletedCount fields: " . implode(', ', $deletedFields)];
        }
        
        return ['success' => false, 'message' => 'No fields were deleted'];
        
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Delete error: ' . $e->getMessage()];
    }
}

function getDefaultConfig() {
    return [
        'basic_info' => [
            'title' => 'Basic Information',
            'icon' => 'fa-user',
            'fields' => []
        ],
        'contact_info' => [
            'title' => 'Contact Information',
            'icon' => 'fa-envelope',
            'fields' => []
        ],
        'work_details' => [
            'title' => 'Work Details',
            'icon' => 'fa-briefcase',
            'fields' => []
        ]
    ];
}

// Handle POST actions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        switch ($action) {
            case 'save_field_config':
                $result = saveFieldConfigurationDB();
                break;
            case 'sync_with_db':
                $result = syncWithDatabaseDB();
                break;
            case 'bulk_delete_fields':
                $result = bulkDeleteFieldsDB();
                break;
            default:
                $result = ['success' => false, 'message' => 'Unknown action'];
        }
        
        if (isset($result)) {
            $message = $result['message'];
            $messageType = $result['success'] ? 'success' : 'danger';
        }
        
    } catch (Exception $e) {
        $message = "Error: " . $e->getMessage();
        $messageType = 'danger';
    }
}

// Load current configuration
$config = loadFieldConfigurationDB();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Working Field Settings</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body { background: #f8f9fa; }
        .card { border: none; border-radius: 15px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .card-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 15px 15px 0 0 !important; }
        .field-item { background: white; padding: 15px; margin: 10px 0; border-radius: 8px; border: 1px solid #dee2e6; }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 4px 8px rgba(0,0,0,0.2); }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h2 class="mb-0">
                            <i class="fas fa-cogs me-2"></i>Working Field Settings
                            <span class="badge bg-success ms-2">Database Storage</span>
                        </h2>
                    </div>
                </div>
            </div>
        </div>

        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                <?php echo htmlspecialchars($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            <!-- Configuration Display -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Current Configuration</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($config)): ?>
                            <p class="text-muted">No configuration found. Click "Sync with Database" to load fields.</p>
                        <?php else: ?>
                            <form method="POST" id="configForm">
                                <input type="hidden" name="action" value="save_field_config">
                                
                                <?php foreach ($config as $sectionKey => $section): ?>
                                    <div class="section-container mb-4">
                                        <h6 class="text-primary">
                                            <i class="fas <?php echo $section['icon']; ?> me-2"></i>
                                            <?php echo htmlspecialchars($section['title']); ?>
                                        </h6>
                                        
                                        <input type="hidden" name="sections[<?php echo $sectionKey; ?>][title]" value="<?php echo htmlspecialchars($section['title']); ?>">
                                        <input type="hidden" name="sections[<?php echo $sectionKey; ?>][icon]" value="<?php echo htmlspecialchars($section['icon']); ?>">
                                        
                                        <?php if (isset($section['fields']) && !empty($section['fields'])): ?>
                                            <?php foreach ($section['fields'] as $fieldName => $fieldConfig): ?>
                                                <div class="field-item">
                                                    <div class="row align-items-center">
                                                        <div class="col-md-1">
                                                            <input type="checkbox" class="form-check-input field-selector" 
                                                                   name="selected_fields[]" 
                                                                   value="<?php echo $sectionKey; ?>|<?php echo $fieldName; ?>"
                                                                   id="select_<?php echo $sectionKey; ?>_<?php echo $fieldName; ?>">
                                                        </div>
                                                        <div class="col-md-3">
                                                            <label class="form-label fw-bold"><?php echo htmlspecialchars($fieldName); ?></label>
                                                        </div>
                                                        <div class="col-md-2">
                                                            <div class="form-check form-switch">
                                                                <input type="checkbox" class="form-check-input" 
                                                                       name="fields[<?php echo $sectionKey; ?>][<?php echo $fieldName; ?>][enabled]"
                                                                       <?php echo ($fieldConfig['enabled'] ?? '0') === '1' ? 'checked' : ''; ?>>
                                                                <label class="form-check-label">Enabled</label>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-2">
                                                            <select name="fields[<?php echo $sectionKey; ?>][<?php echo $fieldName; ?>][type]" class="form-select form-select-sm">
                                                                <option value="text" <?php echo ($fieldConfig['type'] ?? '') === 'text' ? 'selected' : ''; ?>>Text</option>
                                                                <option value="email" <?php echo ($fieldConfig['type'] ?? '') === 'email' ? 'selected' : ''; ?>>Email</option>
                                                                <option value="number" <?php echo ($fieldConfig['type'] ?? '') === 'number' ? 'selected' : ''; ?>>Number</option>
                                                                <option value="date" <?php echo ($fieldConfig['type'] ?? '') === 'date' ? 'selected' : ''; ?>>Date</option>
                                                                <option value="select" <?php echo ($fieldConfig['type'] ?? '') === 'select' ? 'selected' : ''; ?>>Select</option>
                                                                <option value="textarea" <?php echo ($fieldConfig['type'] ?? '') === 'textarea' ? 'selected' : ''; ?>>Textarea</option>
                                                            </select>
                                                        </div>
                                                        <div class="col-md-2">
                                                            <select name="fields[<?php echo $sectionKey; ?>][<?php echo $fieldName; ?>][col]" class="form-select form-select-sm">
                                                                <option value="3" <?php echo ($fieldConfig['col'] ?? '') === '3' ? 'selected' : ''; ?>>25%</option>
                                                                <option value="4" <?php echo ($fieldConfig['col'] ?? '') === '4' ? 'selected' : ''; ?>>33%</option>
                                                                <option value="6" <?php echo ($fieldConfig['col'] ?? '') === '6' ? 'selected' : ''; ?>>50%</option>
                                                                <option value="8" <?php echo ($fieldConfig['col'] ?? '') === '8' ? 'selected' : ''; ?>>66%</option>
                                                                <option value="12" <?php echo ($fieldConfig['col'] ?? '') === '12' ? 'selected' : ''; ?>>100%</option>
                                                            </select>
                                                        </div>
                                                        <div class="col-md-2">
                                                            <input type="text" name="fields[<?php echo $sectionKey; ?>][<?php echo $fieldName; ?>][label]" 
                                                                   class="form-control form-control-sm" 
                                                                   value="<?php echo htmlspecialchars($fieldConfig['label'] ?? $fieldName); ?>"
                                                                   placeholder="Display Label">
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <p class="text-muted ms-3">No fields in this section</p>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; ?>
                                
                                <div class="text-center mt-4">
                                    <button type="submit" class="btn btn-success btn-lg">
                                        <i class="fas fa-save me-2"></i>Save Configuration
                                    </button>
                                </div>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Actions Panel -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Actions</h5>
                    </div>
                    <div class="card-body">
                        <!-- Sync Button -->
                        <form method="POST" class="mb-3">
                            <input type="hidden" name="action" value="sync_with_db">
                            <button type="submit" class="btn btn-info btn-lg w-100">
                                <i class="fas fa-sync me-2"></i>Sync with Database
                            </button>
                        </form>

                        <!-- Bulk Delete Button -->
                        <form method="POST" id="bulkDeleteForm" class="mb-3">
                            <input type="hidden" name="action" value="bulk_delete_fields">
                            <div id="selectedFieldsContainer"></div>
                            <button type="button" class="btn btn-danger btn-lg w-100" onclick="performBulkDelete()">
                                <i class="fas fa-trash me-2"></i>Delete Selected Fields
                            </button>
                        </form>

                        <!-- Navigation -->
                        <hr>
                        <h6>Navigation</h6>
                        <a href="field_settings.php" class="btn btn-outline-primary w-100 mb-2">
                            <i class="fas fa-arrow-left me-2"></i>Original Field Settings
                        </a>
                        <a href="fix_permissions.php" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-tools me-2"></i>Fix Permissions
                        </a>
                    </div>
                </div>

                <!-- Statistics -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Statistics</h5>
                    </div>
                    <div class="card-body">
                        <?php
                        $totalSections = count($config);
                        $totalFields = 0;
                        $enabledFields = 0;
                        
                        foreach ($config as $section) {
                            if (isset($section['fields'])) {
                                $totalFields += count($section['fields']);
                                foreach ($section['fields'] as $field) {
                                    if (($field['enabled'] ?? '0') === '1') {
                                        $enabledFields++;
                                    }
                                }
                            }
                        }
                        ?>
                        
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="border rounded p-2">
                                    <h4 class="text-primary"><?php echo $totalSections; ?></h4>
                                    <small>Sections</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="border rounded p-2">
                                    <h4 class="text-success"><?php echo $totalFields; ?></h4>
                                    <small>Total Fields</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="border rounded p-2">
                                    <h4 class="text-info"><?php echo $enabledFields; ?></h4>
                                    <small>Enabled</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function performBulkDelete() {
            const checkboxes = document.querySelectorAll('.field-selector:checked');
            
            if (checkboxes.length === 0) {
                alert('Please select at least one field to delete');
                return;
            }
            
            if (!confirm(`Delete ${checkboxes.length} selected fields? This cannot be undone.`)) {
                return;
            }
            
            const container = document.getElementById('selectedFieldsContainer');
            container.innerHTML = '';
            
            checkboxes.forEach(checkbox => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'selected_fields[]';
                input.value = checkbox.value;
                container.appendChild(input);
            });
            
            document.getElementById('bulkDeleteForm').submit();
        }

        // Auto-save indication
        document.querySelectorAll('input, select').forEach(element => {
            element.addEventListener('change', function() {
                this.style.borderColor = '#ffc107';
                setTimeout(() => {
                    this.style.borderColor = '';
                }, 2000);
            });
        });
    </script>
</body>
</html>
