/* Create Leaves Page Specific Styles */

/* Form Enhancements */
.unified-form .form-label {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.unified-form .form-label i {
    color: var(--primary-color);
    font-size: 0.9rem;
}

.unified-form .form-control,
.unified-form .form-select {
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.95rem;
    transition: var(--transition-normal);
    background: var(--white);
}

.unified-form .form-control:focus,
.unified-form .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(200, 156, 196, 0.25);
    outline: none;
}

.unified-form .form-control[readonly] {
    background-color: var(--gray-100);
    border-color: var(--gray-300);
    color: var(--text-secondary);
}
        
/* File Input Styling */
.unified-form input[type="file"] {
    padding: var(--spacing-sm);
    border: 2px dashed var(--border-color);
    border-radius: var(--radius-md);
    background: var(--gray-50);
    transition: var(--transition-normal);
}

.unified-form input[type="file"]:hover {
    border-color: var(--primary-color);
    background: var(--primary-light);
}

/* Textarea Styling */
.unified-form textarea {
    resize: vertical;
    min-height: 100px;
}

/* Button Styling */
.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border: none;
    border-radius: var(--radius-lg);
    padding: var(--spacing-md) var(--spacing-xl);
    font-weight: 600;
    font-size: 1rem;
    transition: var(--transition-normal);
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-darker) 100%);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-primary:active {
    transform: translateY(0);
}

.btn-primary:disabled {
    background: var(--gray-400);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Card Enhancements */
.data-card {
    border: none;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    background: var(--white);
}

.data-card .card-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--white);
    border: none;
    padding: var(--spacing-lg) var(--spacing-xl);
}

.data-card .card-header h5 {
    margin: 0;
    font-weight: 600;
    font-size: 1.25rem;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.data-card .card-body {
    padding: var(--spacing-xl);
}

/* Form Grid Enhancements */
.row.g-3 {
    --bs-gutter-x: 1.5rem;
    --bs-gutter-y: 1.5rem;
}

/* Animation Classes */
.animate-fade-in {
    animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Success/Error Message Styling */
.success-message {
    background: linear-gradient(135deg, var(--success-color) 0%, #27ae60 100%);
    color: var(--white);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-md);
    margin: var(--spacing-lg) 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 500;
    box-shadow: var(--shadow-sm);
}

.error-message {
    background: linear-gradient(135deg, var(--danger-color) 0%, #e74c3c 100%);
    color: var(--white);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-md);
    margin: var(--spacing-lg) 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 500;
    box-shadow: var(--shadow-sm);
}

.warning-message {
    background: linear-gradient(135deg, var(--warning-color) 0%, #f39c12 100%);
    color: var(--white);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-md);
    margin: var(--spacing-lg) 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 500;
    box-shadow: var(--shadow-sm);
}

/* Loading State */
.btn-primary .fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Form Validation States */
.form-control.is-valid {
    border-color: var(--success-color);
    background-image: none;
}

.form-control.is-invalid {
    border-color: var(--danger-color);
    background-image: none;
}

/* Flatpickr Customization */
.flatpickr-calendar {
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    border: none;
}

.flatpickr-day.selected {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.flatpickr-day.selected:hover {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
}

/* Responsive Adjustments */

@media (max-width: 768px) {
    .data-card .card-header,
    .data-card .card-body {
        padding: var(--spacing-lg);
    }

    .btn-primary {
        width: 100%;
        padding: var(--spacing-lg);
        font-size: 1.1rem;
    }

    .unified-form .form-control,
    .unified-form .form-select {
        font-size: 16px; /* Prevent zoom on iOS */
    }

    .row.g-3 {
        --bs-gutter-x: 1rem;
        --bs-gutter-y: 1rem;
    }
}

@media (max-width: 576px) {
    .data-card .card-header,
    .data-card .card-body {
        padding: var(--spacing-md);
    }

    .unified-form .form-label {
        font-size: 0.9rem;
    }

    .btn-primary {
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: 1rem;
    }
}

/* Toast Notification Enhancements */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.toast {
    min-width: 300px;
    background: var(--white);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    opacity: 0;
    transform: translateX(100%);
    transition: var(--transition-normal);
}

.toast.show {
    opacity: 1;
    transform: translateX(0);
}

.toast-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--gray-100);
    border-bottom: 1px solid var(--border-color);
}

.toast-body {
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-primary);
}

.toast-success .toast-header {
    background: var(--success-light);
    color: var(--success-dark);
}

.toast-error .toast-header {
    background: var(--danger-light);
    color: var(--danger-dark);
}

.toast-warning .toast-header {
    background: var(--warning-light);
    color: var(--warning-dark);
}
