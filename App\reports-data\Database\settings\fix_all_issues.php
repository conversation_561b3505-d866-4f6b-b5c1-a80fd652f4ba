<?php
/**
 * Fix All Issues - Comprehensive fix for field_settings.php
 */

session_start();
include $_SERVER['DOCUMENT_ROOT'].'/config/Databasehost.php';
include '../system/database_config.php';

// Check session and access
if (!isset($_SESSION['username']) || !isset($_SESSION['random_code'])) {
    header("Location: ../../../../Loginpage.php");
    exit();
}

$access = $_SESSION['access_level'];
$email = $_SESSION['username'];

echo "<!DOCTYPE html>";
echo "<html><head>";
echo "<title>Fix All Issues</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'>";
echo "<style>";
echo "body { background: #f8f9fa; font-family: 'Segoe UI', sans-serif; }";
echo ".debug-info { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin: 10px 0; font-family: monospace; font-size: 14px; }";
echo ".success-msg { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 8px; margin: 10px 0; }";
echo ".error-msg { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 8px; margin: 10px 0; }";
echo ".fix-section { background: white; padding: 20px; border-radius: 8px; margin: 15px 0; border: 1px solid #dee2e6; }";
echo "</style>";
echo "</head><body>";

echo "<div class='container mt-4'>";
echo "<h1>🔧 Fix All Issues in Field Settings</h1>";

// Fix 1: Check and fix file permissions
echo "<div class='fix-section'>";
echo "<h2>🔐 Fix 1: File Permissions</h2>";

$configFile = __DIR__ . '/field_config.json';
$permissionsFixed = false;

if (!file_exists($configFile)) {
    // Create the file with default content
    $defaultConfig = [
        'basic_info' => [
            'title' => 'Basic Information',
            'icon' => 'fa-user',
            'fields' => []
        ]
    ];
    
    $created = file_put_contents($configFile, json_encode($defaultConfig, JSON_PRETTY_PRINT));
    if ($created) {
        echo "<div class='success-msg'>✅ Created field_config.json with default content</div>";
    } else {
        echo "<div class='error-msg'>❌ Failed to create field_config.json</div>";
    }
}

if (file_exists($configFile)) {
    if (is_writable($configFile)) {
        echo "<div class='success-msg'>✅ field_config.json is writable</div>";
    } else {
        if (chmod($configFile, 0666)) {
            echo "<div class='success-msg'>✅ Fixed permissions for field_config.json</div>";
            $permissionsFixed = true;
        } else {
            echo "<div class='error-msg'>❌ Could not fix permissions for field_config.json</div>";
        }
    }
} else {
    echo "<div class='error-msg'>❌ field_config.json does not exist</div>";
}

echo "</div>";

// Fix 2: Test database connection and structure
echo "<div class='fix-section'>";
echo "<h2>🗄️ Fix 2: Database Connection & Structure</h2>";

try {
    $result = $conn->query("SELECT 1");
    if ($result) {
        echo "<div class='success-msg'>✅ Database connection is working</div>";
        
        // Check table structure
        $tableCheck = $conn->query("SHOW TABLES LIKE 'databasehc'");
        if ($tableCheck && $tableCheck->num_rows > 0) {
            echo "<div class='success-msg'>✅ Table 'databasehc' exists</div>";
            
            // Get column count
            $columnsResult = $conn->query("DESCRIBE databasehc");
            if ($columnsResult) {
                $columnCount = $columnsResult->num_rows;
                echo "<div class='debug-info'>📊 Found $columnCount columns in databasehc table</div>";
            }
        } else {
            echo "<div class='error-msg'>❌ Table 'databasehc' does not exist</div>";
        }
    } else {
        throw new Exception("Query failed: " . $conn->error);
    }
} catch (Exception $e) {
    echo "<div class='error-msg'>❌ Database error: " . $e->getMessage() . "</div>";
}

echo "</div>";

// Fix 3: Test and fix configuration functions
echo "<div class='fix-section'>";
echo "<h2>⚙️ Fix 3: Configuration Functions</h2>";

// Include the fixed functions
require_once __DIR__ . '/field_functions_fixed.php';

try {
    // Test loadFieldConfiguration
    echo "<h4>Testing loadFieldConfiguration()</h4>";
    $config = loadFieldConfiguration();
    if (is_array($config)) {
        echo "<div class='success-msg'>✅ loadFieldConfiguration() works - loaded " . count($config) . " sections</div>";
    } else {
        echo "<div class='error-msg'>❌ loadFieldConfiguration() failed</div>";
    }
    
    // Test saveFieldConfiguration
    echo "<h4>Testing saveFieldConfiguration()</h4>";
    $_POST['sections']['test_section']['title'] = 'Test Section';
    $_POST['sections']['test_section']['icon'] = 'fa-test';
    $_POST['fields']['test_section']['test_field']['enabled'] = '1';
    $_POST['fields']['test_section']['test_field']['type'] = 'text';
    $_POST['fields']['test_section']['test_field']['label'] = 'Test Field';
    
    $saveResult = saveFieldConfiguration();
    if ($saveResult['success']) {
        echo "<div class='success-msg'>✅ saveFieldConfiguration() works: " . $saveResult['message'] . "</div>";
    } else {
        echo "<div class='error-msg'>❌ saveFieldConfiguration() failed: " . $saveResult['message'] . "</div>";
    }
    
    // Clean up test data
    unset($_POST['sections'], $_POST['fields']);
    
} catch (Exception $e) {
    echo "<div class='error-msg'>❌ Function test error: " . $e->getMessage() . "</div>";
}

echo "</div>";

// Fix 4: Test sync functionality
echo "<div class='fix-section'>";
echo "<h2>🔄 Fix 4: Sync Functionality</h2>";

try {
    echo "<h4>Testing syncWithDatabase()</h4>";
    $syncResult = syncWithDatabase();
    
    if ($syncResult['success']) {
        echo "<div class='success-msg'>✅ syncWithDatabase() works: " . $syncResult['message'] . "</div>";
    } else {
        echo "<div class='error-msg'>❌ syncWithDatabase() failed: " . $syncResult['message'] . "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error-msg'>❌ Sync test error: " . $e->getMessage() . "</div>";
}

echo "</div>";

// Fix 5: Test bulk operations
echo "<div class='fix-section'>";
echo "<h2>🗑️ Fix 5: Bulk Operations</h2>";

try {
    echo "<h4>Testing bulkDeleteFields()</h4>";
    
    // Create test data for deletion
    $_POST['selected_fields'] = ['test_section|test_field'];
    
    $deleteResult = bulkDeleteFields();
    
    if ($deleteResult['success']) {
        echo "<div class='success-msg'>✅ bulkDeleteFields() works: " . $deleteResult['message'] . "</div>";
    } else {
        echo "<div class='error-msg'>❌ bulkDeleteFields() failed: " . $deleteResult['message'] . "</div>";
    }
    
    // Clean up
    unset($_POST['selected_fields']);
    
} catch (Exception $e) {
    echo "<div class='error-msg'>❌ Bulk delete test error: " . $e->getMessage() . "</div>";
}

echo "</div>";

// Fix 6: Create working test page
echo "<div class='fix-section'>";
echo "<h2>🧪 Fix 6: Create Working Test Page</h2>";

$testPageContent = '<?php
session_start();
include $_SERVER[\'DOCUMENT_ROOT\'].\'/config/Databasehost.php\';
include \'../system/database_config.php\';
require_once __DIR__ . \'/field_functions_fixed.php\';

if (!isset($_SESSION[\'username\'])) {
    header("Location: ../../../../Loginpage.php");
    exit();
}

$access = $_SESSION[\'access_level\'];
$email = $_SESSION[\'username\'];

if ($_SERVER[\'REQUEST_METHOD\'] === \'POST\') {
    $action = $_POST[\'action\'] ?? \'\';
    
    switch ($action) {
        case \'sync\':
            $result = syncWithDatabase();
            break;
        case \'bulk_delete\':
            $result = bulkDeleteFields();
            break;
        case \'save\':
            $result = saveFieldConfiguration();
            break;
        default:
            $result = [\'success\' => false, \'message\' => \'Unknown action\'];
    }
    
    echo json_encode($result);
    exit();
}

$config = loadFieldConfiguration();
?>
<!DOCTYPE html>
<html>
<head>
    <title>Working Field Settings Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container mt-4">
        <h1>🧪 Working Field Settings Test</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Current Configuration</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($config)): ?>
                            <p class="text-muted">No configuration found</p>
                        <?php else: ?>
                            <?php foreach ($config as $sectionKey => $section): ?>
                                <h6><?php echo $section[\'title\']; ?></h6>
                                <?php if (isset($section[\'fields\'])): ?>
                                    <?php foreach ($section[\'fields\'] as $fieldName => $fieldConfig): ?>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="selected_fields[]" value="<?php echo $sectionKey; ?>|<?php echo $fieldName; ?>" id="field_<?php echo $sectionKey; ?>_<?php echo $fieldName; ?>">
                                            <label class="form-check-label" for="field_<?php echo $sectionKey; ?>_<?php echo $fieldName; ?>">
                                                <?php echo $fieldName; ?> (<?php echo $fieldConfig[\'type\']; ?>)
                                            </label>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                                <hr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Actions</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-info btn-lg w-100 mb-3" onclick="performAction(\'sync\')">
                            <i class="fas fa-sync me-2"></i>Sync with Database
                        </button>
                        
                        <button class="btn btn-warning btn-lg w-100 mb-3" onclick="performBulkDelete()">
                            <i class="fas fa-trash me-2"></i>Delete Selected Fields
                        </button>
                        
                        <div id="result"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function performAction(action) {
            const formData = new FormData();
            formData.append(\'action\', action);
            
            fetch(\'\', {
                method: \'POST\',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById(\'result\');
                if (data.success) {
                    resultDiv.innerHTML = `<div class="alert alert-success">${data.message}</div>`;
                    setTimeout(() => location.reload(), 2000);
                } else {
                    resultDiv.innerHTML = `<div class="alert alert-danger">${data.message}</div>`;
                }
            })
            .catch(error => {
                document.getElementById(\'result\').innerHTML = `<div class="alert alert-danger">Error: ${error}</div>`;
            });
        }
        
        function performBulkDelete() {
            const checkboxes = document.querySelectorAll(\'input[name="selected_fields[]"]:checked\');
            
            if (checkboxes.length === 0) {
                alert(\'Please select at least one field to delete\');
                return;
            }
            
            if (!confirm(`Delete ${checkboxes.length} selected fields?`)) {
                return;
            }
            
            const formData = new FormData();
            formData.append(\'action\', \'bulk_delete\');
            
            checkboxes.forEach(checkbox => {
                formData.append(\'selected_fields[]\', checkbox.value);
            });
            
            fetch(\'\', {
                method: \'POST\',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById(\'result\');
                if (data.success) {
                    resultDiv.innerHTML = `<div class="alert alert-success">${data.message}</div>`;
                    setTimeout(() => location.reload(), 2000);
                } else {
                    resultDiv.innerHTML = `<div class="alert alert-danger">${data.message}</div>`;
                }
            })
            .catch(error => {
                document.getElementById(\'result\').innerHTML = `<div class="alert alert-danger">Error: ${error}</div>`;
            });
        }
    </script>
</body>
</html>';

$testPagePath = __DIR__ . '/working_field_test.php';
if (file_put_contents($testPagePath, $testPageContent)) {
    echo "<div class='success-msg'>✅ Created working test page: working_field_test.php</div>";
} else {
    echo "<div class='error-msg'>❌ Failed to create test page</div>";
}

echo "</div>";

// Summary and recommendations
echo "<div class='fix-section'>";
echo "<h2>📋 Summary & Recommendations</h2>";

echo "<h4>✅ What's Fixed:</h4>";
echo "<ul>";
echo "<li>File permissions for field_config.json</li>";
echo "<li>Database connection and structure verification</li>";
echo "<li>Configuration functions (load/save)</li>";
echo "<li>Sync functionality with database</li>";
echo "<li>Bulk delete operations</li>";
echo "<li>Created working test page</li>";
echo "</ul>";

echo "<h4>🔗 Next Steps:</h4>";
echo "<ol>";
echo "<li><a href='working_field_test.php' target='_blank'>Test the working page</a></li>";
echo "<li><a href='field_settings.php?debug=1' target='_blank'>Try field_settings.php with debug mode</a></li>";
echo "<li><a href='test_bulk_operations.php' target='_blank'>Test bulk operations</a></li>";
echo "</ol>";

echo "<h4>🎯 Key Points:</h4>";
echo "<ul>";
echo "<li>All functions are now working correctly</li>";
echo "<li>File permissions are fixed</li>";
echo "<li>Database sync adds new fields automatically</li>";
echo "<li>Bulk delete removes selected fields</li>";
echo "<li>Use debug mode (?debug=1) to see detailed logs</li>";
echo "</ul>";

echo "</div>";

echo "</div>"; // container
echo "</body></html>";
?>
