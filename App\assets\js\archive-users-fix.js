/**
 * إصلاح مخصص لأزرار ArchiveUsers.php
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 Archive Users Fix loaded');
    
    // إصلاح زر التصدير
    const exportForm = document.querySelector('.export-form');
    if (exportForm) {
        console.log('✅ Export form found');
        
        // إزالة أي event listeners موجودة
        const newExportForm = exportForm.cloneNode(true);
        exportForm.parentNode.replaceChild(newExportForm, exportForm);
        
        // إضافة event listener جديد
        newExportForm.addEventListener('submit', function(e) {
            console.log('📤 Export form submitted');
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                const originalText = submitBtn.innerHTML;
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Exporting...';
                
                // إعادة تفعيل الزر بعد 15 ثانية في حالة وجود مشكلة
                setTimeout(() => {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = originalText;
                }, 15000);
            }
        });
    } else {
        console.warn('⚠️ Export form not found');
    }
    
    // إصلاح أزرار إعادة التفعيل
    const reactivateForms = document.querySelectorAll('.reactivate-form');
    console.log(`✅ Found ${reactivateForms.length} reactivate forms`);
    
    reactivateForms.forEach((form, index) => {
        // إزالة أي event listeners موجودة
        const newForm = form.cloneNode(true);
        form.parentNode.replaceChild(newForm, form);
        
        // إضافة event listener جديد
        newForm.addEventListener('submit', function(e) {
            console.log(`🔄 Reactivate form ${index + 1} submitted`);
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                const originalText = submitBtn.innerHTML;
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
                
                // إعادة تفعيل الزر بعد 15 ثانية في حالة وجود مشكلة
                setTimeout(() => {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = originalText;
                }, 15000);
            }
        });
    });
    
    // التأكد من وجود دوال التأكيد
    if (typeof window.confirmExport !== 'function') {
        window.confirmExport = function() {
            return confirm('Are you sure you want to export archived users data? This will generate a CSV file with all archived user information.');
        };
        console.log('✅ confirmExport function created');
    }
    
    if (typeof window.confirmReactivate !== 'function') {
        window.confirmReactivate = function(email) {
            return confirm(`Are you sure you want to reactivate the user ${email}?\n\nThis will:\n1. Move the user back to active users\n2. Generate a new password\n3. Send login credentials via email\n\nThis action cannot be undone.`);
        };
        console.log('✅ confirmReactivate function created');
    }
    
    // اختبار الأزرار
    console.log('🧪 Testing buttons...');
    console.log('Export forms:', document.querySelectorAll('.export-form').length);
    console.log('Reactivate forms:', document.querySelectorAll('.reactivate-form').length);
    console.log('CSRF tokens:', document.querySelectorAll('input[name="csrf_token"]').length);
    
    console.log('🎉 Archive Users Fix completed successfully');
});

// دالة تشخيص للمطورين
window.debugArchiveButtons = function() {
    console.log('=== Archive Buttons Debug ===');
    console.log('Export forms:', document.querySelectorAll('.export-form'));
    console.log('Reactivate forms:', document.querySelectorAll('.reactivate-form'));
    console.log('All forms:', document.querySelectorAll('form'));
    console.log('CSRF tokens:', document.querySelectorAll('input[name="csrf_token"]'));
    console.log('confirmExport function:', typeof window.confirmExport);
    console.log('confirmReactivate function:', typeof window.confirmReactivate);
    console.log('============================');
};
