/* ==================== ROOT VARIABLES ==================== */
:root {
    /* ==================== UNIFIED COLOR PALETTE ==================== */

    /* Primary Purple Scale - Based on #c89cc4 */
    --primary-50: #faf8fc;     /* Very light purple-pink */
    --primary-100: #f4f0f7;    /* Light purple-pink */
    --primary-200: #ede4f1;    /* Soft purple */
    --primary-300: #dcc8e0;    /* Medium light purple */
    --primary-400: #c89cc4;    /* Base color - beautiful purple-pink */
    --primary-500: #b580b0;    /* Main Primary - deeper version */
    --primary-600: #a2649c;    /* Medium dark purple */
    --primary-700: #8f4888;    /* Dark purple */
    --primary-800: #7c2c74;    /* Very dark purple */
    --primary-900: #691060;    /* Darkest purple */

    /* Secondary Warm Pink Scale - Complementary to #c89cc4 */
    --secondary-50: #fcf9fb;   /* Very light warm pink */
    --secondary-100: #f8f2f6;  /* Light warm pink */
    --secondary-200: #f1e6ed;  /* Soft warm pink */
    --secondary-300: #e2ccd8;  /* Medium light warm pink */
    --secondary-400: #d3b2c3;  /* Warm pink complement */
    --secondary-500: #c498ae;  /* Main Secondary - warm pink */
    --secondary-600: #b57e99;  /* Medium dark warm pink */
    --secondary-700: #a66484;  /* Dark warm pink */
    --secondary-800: #974a6f;  /* Very dark warm pink */
    --secondary-900: #88305a;  /* Darkest warm pink */

    /* Accent Colors - Soft Lavender Variations */
    --accent-light: #f4f0f7;   /* Light lavender tint */
    --accent-main: #c89cc4;    /* Main base color */
    --accent-dark: #a2649c;    /* Darker lavender */

    /* Status Colors - Harmonious with Logo */
    --success-50: #f0fdf4;
    --success-100: #dcfce7;
    --success-500: #22c55e;    /* Fresh green */
    --success-600: #16a34a;
    --success-700: #15803d;

    --warning-50: #fffbeb;
    --warning-100: #fef3c7;
    --warning-500: #f59e0b;    /* Warm amber */
    --warning-600: #d97706;
    --warning-700: #b45309;

    --danger-50: #fef2f2;
    --danger-100: #fecaca;
    --danger-500: #ef4444;     /* Modern red */
    --danger-600: #dc2626;
    --danger-700: #b91c1c;

    --info-50: #eff6ff;
    --info-100: #dbeafe;
    --info-500: #3b82f6;      /* Clean blue */
    --info-600: #2563eb;
    --info-700: #1d4ed8;

    /* Neutral Gray Scale - Subtle Purple Tint from #c89cc4 */
    --gray-50: #faf8fc;       /* Very light with purple hint */
    --gray-100: #f4f0f7;      /* Light with purple hint */
    --gray-200: #ede4f1;      /* Soft purple-gray */
    --gray-300: #dcc8e0;      /* Medium light purple-gray */
    --gray-400: #c89cc4;      /* Medium purple-gray - base color */
    --gray-500: #b580b0;      /* Balanced purple-gray */
    --gray-600: #a2649c;      /* Medium dark purple-gray */
    --gray-700: #8f4888;      /* Dark purple-gray */
    --gray-800: #7c2c74;      /* Very dark purple-gray */
    --gray-900: #691060;      /* Darkest purple-gray */

    /* Text Colors - Based on #c89cc4 Scale */
    --text-primary: #691060;      /* Darkest purple for main text */
    --text-secondary: #8f4888;    /* Dark purple for secondary text */
    --text-tertiary: #b580b0;     /* Medium purple for tertiary text */
    --text-light: #c89cc4;       /* Light purple for subtle text */
    --text-white: #ffffff;
    --text-black: #000000;

    /* Background Colors - Clean and Harmonious */
    --bg-white: #ffffff;
    --bg-light: #faf8fc;         /* Very light purple tint */
    --bg-gray: #f4f0f7;          /* Light purple tint */
    --bg-dark: #691060;          /* Dark purple from scale */

    /* Border Colors - Subtle and Elegant */
    --border-light: #ede4f1;
    --border-medium: #dcc8e0;
    --border-dark: #c89cc4;

    /* Special Dashboard Status Colors */
    --status-online: #4caf50;
    --status-offline: #f44336;
    --status-break1: #ffc107;
    --status-break2: #ff9800;
    --status-break3: #ff5722;
    --status-technical: #8b5fbf;
    --status-floater: #663399;
    --status-overtime: #7a4fb3;
    --status-restroom: #663399;

    /* ==================== UNIFIED DESIGN TOKENS ==================== */

    /* Spacing Scale */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-2xl: 48px;

    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 6px;
    --radius-lg: 8px;
    --radius-xl: 12px;

    /* Shadows - Soft with #c89cc4 Tint */
    --shadow-sm: 0 1px 2px 0 rgba(200, 156, 196, 0.08);
    --shadow-md: 0 4px 6px -1px rgba(200, 156, 196, 0.12), 0 2px 4px -1px rgba(200, 156, 196, 0.08);
    --shadow-lg: 0 10px 15px -3px rgba(200, 156, 196, 0.12), 0 4px 6px -2px rgba(200, 156, 196, 0.08);
    --shadow-xl: 0 20px 25px -5px rgba(200, 156, 196, 0.12), 0 10px 10px -5px rgba(200, 156, 196, 0.06);
    --shadow-primary: 0 4px 6px -1px rgba(200, 156, 196, 0.18), 0 2px 4px -1px rgba(200, 156, 196, 0.12);

    /* Transitions - Smooth and Natural */
    --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);

    /* Gradients - Logo-Inspired */
    --gradient-primary: linear-gradient(135deg, var(--primary-400), var(--primary-600));
    --gradient-secondary: linear-gradient(135deg, var(--secondary-300), var(--secondary-500));
    --gradient-accent: linear-gradient(135deg, var(--accent-main), var(--accent-dark));
    --gradient-logo: linear-gradient(135deg, var(--secondary-300), var(--primary-500));
    --gradient-subtle: linear-gradient(135deg, var(--gray-50), var(--gray-100));

    /* ==================== DASHBOARD CARD COLORS ==================== */

    /* Card Background Colors */
    --card-bg-primary: var(--bg-white);
    --card-bg-secondary: var(--gray-50);
    --card-bg-accent: var(--secondary-50);

    /* Card Border Colors */
    --card-border: var(--border-light);
    --card-border-hover: var(--border-medium);
    --card-border-active: var(--primary-300);

    /* Card Text Colors */
    --card-title: var(--text-primary);
    --card-subtitle: var(--text-secondary);
    --card-value: var(--primary-600);
    --card-description: var(--text-tertiary);

    /* Stats Card Specific Colors - Harmonious with #c89cc4 */
    --stats-schedule-color: var(--primary-500);    /* Beautiful purple */
    --stats-absent-color: var(--danger-500);       /* Modern red */
    --stats-vacation-color: var(--warning-500);    /* Warm amber */
    --stats-online-color: var(--success-500);      /* Fresh green */

    /* ==================== LEGACY ALIASES (for backward compatibility) ==================== */

    /* Main Colors */
    --primary-color: var(--primary-500);
    --primary-light: var(--primary-300);
    --primary-dark: var(--primary-600);
    --primary-darker: var(--primary-700);
    --primary-lighter: var(--primary-100);

    --secondary-color: var(--secondary-100);
    --accent-color: var(--accent-main);

    /* Status Colors */
    --success-color: var(--success-500);
    --success-light: var(--success-50);
    --warning-color: var(--warning-500);
    --warning-light: var(--warning-50);
    --danger-color: var(--danger-500);
    --danger-light: var(--danger-50);
    --info-color: var(--info-500);

    /* Neutral Colors */
    --white: var(--bg-white);
    --black: var(--text-black);
    --dark-color: var(--gray-800);
    --gray-color: var(--gray-500);
    --gray-light: var(--gray-200);
    --light-color: var(--gray-100);
    --light-bg: var(--bg-light);
    --text-dark: var(--text-primary);
    --text-medium: var(--text-secondary);
    --text-light: var(--text-tertiary);
    --text-color: var(--gray-800);
    --border-color: var(--border-light);

    /* Design Tokens */
    --border-radius: var(--radius-lg);
    --border-radius-sm: var(--radius-sm);
    --border-radius-lg: var(--radius-xl);
    --shadow: var(--shadow-md);
    --shadow-lg: var(--shadow-lg);
    --transition: var(--transition-normal);

    /* ==================== PAGE-SPECIFIC ALIASES ==================== */

    /* All page-specific variables now use the unified color system */

    /* Settings Page */
    --settings-primary-color: var(--primary-500);
    --settings-primary-light: var(--primary-300);
    --settings-primary-dark: var(--primary-600);
    --settings-secondary-color: var(--secondary-100);
    --settings-accent-color: var(--accent-main);
    --settings-light-color: var(--gray-100);
    --settings-dark-color: var(--gray-800);
    --settings-white: var(--bg-white);
    --settings-success-color: var(--success-500);
    --settings-warning-color: var(--warning-600);
    --settings-danger-color: var(--danger-500);
    --settings-gray-color: var(--gray-400);
    --settings-text-dark: var(--text-primary);
    --settings-transition: var(--transition-normal);

    /* Reports Page */
    --reports-primary-color: var(--primary-500);
    --reports-primary-light: var(--primary-300);
    --reports-primary-dark: var(--primary-600);
    --reports-secondary-color: var(--secondary-100);
    --reports-accent-color: var(--accent-main);
    --reports-light-color: var(--gray-100);
    --reports-dark-color: var(--gray-800);
    --reports-success-color: var(--success-500);
    --reports-warning-color: var(--warning-600);
    --reports-danger-color: var(--danger-500);
    --reports-gray-color: var(--gray-400);
    --reports-white: var(--bg-white);
    --reports-shadow: var(--shadow-primary);
    --reports-transition: var(--transition-normal);
    --reports-border-radius: var(--radius-lg);
    --reports-border-color: var(--border-light);
    --reports-text-dark: var(--text-primary);
    --reports-text-medium: var(--text-secondary);
    --reports-text-light: var(--text-tertiary);

    /* Real-Time Dashboard */
    --rtd-main-color: var(--primary-500);
    --rtd-secondary-color: var(--primary-200);
    --rtd-online-color: var(--status-online);
    --rtd-offline-color: var(--status-offline);
    --rtd-break1-color: var(--status-break1);
    --rtd-break2-color: var(--status-break2);
    --rtd-break3-color: var(--status-break3);
    --rtd-technical-color: var(--status-technical);
    --rtd-floater-color: var(--status-floater);
    --rtd-overtime-color: var(--status-overtime);
    --rtd-restroom-color: var(--status-restroom);

    /* Interpreter Page */
    --interpreter-primary-color: var(--primary-500);
    --interpreter-primary-light: var(--primary-300);
    --interpreter-primary-dark: var(--primary-600);
    --interpreter-primary-darker: var(--primary-700);
    --interpreter-text-color: var(--gray-800);
    --interpreter-light-bg: var(--bg-light);

    /* Interpreter MTD Page */
    --interpreterMTD-primary-color: var(--primary-500);
    --interpreterMTD-primary-light: var(--primary-300);
    --interpreterMTD-primary-dark: var(--primary-600);
    --interpreterMTD-primary-darker: var(--primary-700);
    --interpreterMTD-text-color: var(--gray-800);
    --interpreterMTD-light-bg: var(--bg-light);

    /* Leader Page */
    --leader-primary-color: var(--primary-500);
    --leader-primary-light: var(--primary-300);
    --leader-primary-dark: var(--primary-600);
    --leader-primary-darker: var(--primary-700);
    --leader-text-color: var(--gray-800);
    --leader-light-bg: var(--bg-light);

    /* Fulfillment Page */
    --fulfillmentpage-primary-color: var(--primary-500);
    --fulfillmentpage-primary-light: var(--primary-200);
    --fulfillmentpage-primary-dark: var(--primary-600);
    --fulfillmentpage-primary-darker: var(--primary-700);
    --fulfillmentpage-primary-darkest: var(--primary-800);
    --fulfillmentpage-text-dark: var(--text-primary);
    --fulfillmentpage-text-light: var(--text-tertiary);
    --fulfillmentpage-bg-light: var(--secondary-100);
    --fulfillmentpage-bg-card: var(--bg-white);

    /* Leaves Page */
    --leaves-primary-color: var(--primary-500);
    --leaves-primary-light: var(--primary-300);
    --leaves-primary-dark: var(--primary-600);
    --leaves-secondary-color: var(--secondary-100);
    --leaves-accent-color: var(--accent-main);
    --leaves-light-color: var(--bg-light);
    --leaves-dark-color: var(--gray-700);
    --leaves-success-color: var(--success-500);
    --leaves-warning-color: var(--warning-600);
    --leaves-danger-color: var(--danger-500);
    --leaves-gray-color: var(--gray-400);
    --leaves-white: var(--bg-white);
    --leaves-shadow: var(--shadow-sm);
    --leaves-transition: var(--transition-normal);
    --leaves-border-radius: var(--radius-md);
    --leaves-border-color: var(--border-light);
    --leaves-text-dark: var(--gray-700);
    --leaves-text-medium: var(--text-secondary);
    --leaves-text-light: var(--text-tertiary);
    --leaves-bg-gradient: var(--gradient-primary);


}
