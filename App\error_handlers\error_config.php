<?php
/**
 * Error Handling Configuration
 * تكوين معالجة الأخطاء المركزي
 * 
 * @version 2.0
 * <AUTHOR> CX Security Team
 */

// منع الوصول المباشر
if (!defined('ERROR_CONFIG_LOADED')) {
    define('ERROR_CONFIG_LOADED', true);
} else {
    http_response_code(403);
    exit('Direct access not allowed');
}

/**
 * إعدادات البيئة والأمان
 */
class ErrorConfig {
    
    // إعدادات البيئة
    const ENVIRONMENT_PRODUCTION = 'production';
    const ENVIRONMENT_DEVELOPMENT = 'development';
    const ENVIRONMENT_TESTING = 'testing';
    
    // مستويات الخطورة
    const SEVERITY_CRITICAL = 'critical';
    const SEVERITY_ERROR = 'error';
    const SEVERITY_WARNING = 'warning';
    const SEVERITY_NOTICE = 'notice';
    const SEVERITY_INFO = 'info';
    
    // أنواع الأخطاء
    const ERROR_TYPE_PHP = 'php_error';
    const ERROR_TYPE_DATABASE = 'database_error';
    const ERROR_TYPE_SECURITY = 'security_error';
    const ERROR_TYPE_VALIDATION = 'validation_error';
    const ERROR_TYPE_FILE = 'file_error';
    const ERROR_TYPE_NETWORK = 'network_error';
    
    /**
     * الحصول على البيئة الحالية
     */
    public static function getCurrentEnvironment() {
        $env = getenv('ENVIRONMENT');
        if (!$env) {
            $env = $_SERVER['ENVIRONMENT'] ?? self::ENVIRONMENT_PRODUCTION;
        }
        return strtolower($env);
    }
    
    /**
     * التحقق من كون البيئة إنتاج
     */
    public static function isProduction() {
        return self::getCurrentEnvironment() === self::ENVIRONMENT_PRODUCTION;
    }
    
    /**
     * التحقق من كون البيئة تطوير
     */
    public static function isDevelopment() {
        return self::getCurrentEnvironment() === self::ENVIRONMENT_DEVELOPMENT;
    }
    
    /**
     * الحصول على إعدادات معالجة الأخطاء حسب البيئة
     */
    public static function getErrorSettings() {
        $environment = self::getCurrentEnvironment();
        
        switch ($environment) {
            case self::ENVIRONMENT_DEVELOPMENT:
                return [
                    'display_errors' => 1,
                    'display_startup_errors' => 1,
                    'error_reporting' => E_ALL,
                    'log_errors' => 1,
                    'log_errors_max_len' => 0,
                    'ignore_repeated_errors' => 0,
                    'ignore_repeated_source' => 0,
                    'html_errors' => 1,
                    'track_errors' => 1,
                    'show_detailed_errors' => true,
                    'show_file_paths' => true,
                    'show_stack_trace' => true
                ];
                
            case self::ENVIRONMENT_TESTING:
                return [
                    'display_errors' => 0,
                    'display_startup_errors' => 0,
                    'error_reporting' => E_ALL & ~E_NOTICE,
                    'log_errors' => 1,
                    'log_errors_max_len' => 1024,
                    'ignore_repeated_errors' => 1,
                    'ignore_repeated_source' => 1,
                    'html_errors' => 0,
                    'track_errors' => 1,
                    'show_detailed_errors' => true,
                    'show_file_paths' => false,
                    'show_stack_trace' => true
                ];
                
            case self::ENVIRONMENT_PRODUCTION:
            default:
                return [
                    'display_errors' => 0,
                    'display_startup_errors' => 0,
                    'error_reporting' => E_ALL & ~E_NOTICE & ~E_STRICT & ~E_DEPRECATED,
                    'log_errors' => 1,
                    'log_errors_max_len' => 1024,
                    'ignore_repeated_errors' => 1,
                    'ignore_repeated_source' => 1,
                    'html_errors' => 0,
                    'track_errors' => 0,
                    'show_detailed_errors' => false,
                    'show_file_paths' => false,
                    'show_stack_trace' => false
                ];
        }
    }
    
    /**
     * تطبيق إعدادات معالجة الأخطاء
     */
    public static function applyErrorSettings() {
        $settings = self::getErrorSettings();
        
        foreach ($settings as $key => $value) {
            if (in_array($key, ['show_detailed_errors', 'show_file_paths', 'show_stack_trace'])) {
                // هذه إعدادات مخصصة، لا نطبقها على PHP
                continue;
            }
            
            ini_set($key, $value);
        }
    }
    
    /**
     * الحصول على مسارات اللوجات
     */
    public static function getLogPaths() {
        $base_path = __DIR__ . '/logs/';
        
        return [
            'error_log' => $base_path . 'php_errors.log',
            'security_log' => $base_path . 'security.log',
            'database_log' => $base_path . 'database.log',
            'access_log' => $base_path . 'access.log',
            'debug_log' => $base_path . 'debug.log',
            'performance_log' => $base_path . 'performance.log'
        ];
    }
    
    /**
     * الحصول على إعدادات تدوير اللوجات
     */
    public static function getLogRotationSettings() {
        return [
            'max_file_size' => 10 * 1024 * 1024, // 10MB
            'max_files' => 5,
            'compress_old_files' => true,
            'delete_old_files_after_days' => 30
        ];
    }
    
    /**
     * الحصول على الأنماط الحساسة للبيانات
     */
    public static function getSensitivePatterns() {
        return [
            // كلمات المرور
            '/password[=:]\s*[^\s\'"]+/i',
            '/passwd[=:]\s*[^\s\'"]+/i',
            '/pwd[=:]\s*[^\s\'"]+/i',
            
            // معلومات قاعدة البيانات
            '/mysql[^:]*:[^@]*@[^\/]+/i',
            '/mysqli[^:]*:[^@]*@[^\/]+/i',
            '/pgsql[^:]*:[^@]*@[^\/]+/i',
            
            // معلومات الجلسة والتوكنز
            '/session[_\s]*id[=:]\s*[^\s\'"]+/i',
            '/csrf[_\s]*token[=:]\s*[^\s\'"]+/i',
            '/api[_\s]*key[=:]\s*[^\s\'"]+/i',
            '/access[_\s]*token[=:]\s*[^\s\'"]+/i',
            '/bearer[_\s]*token[=:]\s*[^\s\'"]+/i',
            
            // مفاتيح التشفير
            '/secret[_\s]*key[=:]\s*[^\s\'"]+/i',
            '/private[_\s]*key[=:]\s*[^\s\'"]+/i',
            '/encryption[_\s]*key[=:]\s*[^\s\'"]+/i',
            
            // معلومات البريد الإلكتروني
            '/smtp[_\s]*password[=:]\s*[^\s\'"]+/i',
            '/mail[_\s]*password[=:]\s*[^\s\'"]+/i',
            
            // معلومات الدفع
            '/credit[_\s]*card[=:]\s*[^\s\'"]+/i',
            '/card[_\s]*number[=:]\s*[^\s\'"]+/i',
            '/cvv[=:]\s*[^\s\'"]+/i',
            
            // معلومات شخصية
            '/ssn[=:]\s*[^\s\'"]+/i',
            '/social[_\s]*security[=:]\s*[^\s\'"]+/i'
        ];
    }
    
    /**
     * الحصول على رسائل الأخطاء الآمنة للمستخدمين
     */
    public static function getUserFriendlyMessages() {
        return [
            self::ERROR_TYPE_PHP => [
                self::SEVERITY_CRITICAL => 'حدث خطأ في النظام. يرجى المحاولة مرة أخرى لاحقاً.',
                self::SEVERITY_ERROR => 'حدث خطأ أثناء معالجة طلبك. يرجى المحاولة مرة أخرى.',
                self::SEVERITY_WARNING => 'تم اكتشاف مشكلة بسيطة. العملية قد تكون غير مكتملة.',
                self::SEVERITY_NOTICE => 'تم إكمال العملية مع ملاحظات.'
            ],
            
            self::ERROR_TYPE_DATABASE => [
                self::SEVERITY_CRITICAL => 'خطأ في قاعدة البيانات. يرجى المحاولة لاحقاً.',
                self::SEVERITY_ERROR => 'فشل في حفظ البيانات. يرجى المحاولة مرة أخرى.',
                self::SEVERITY_WARNING => 'تم حفظ البيانات مع تحذيرات.',
                self::SEVERITY_NOTICE => 'تم حفظ البيانات بنجاح.'
            ],
            
            self::ERROR_TYPE_SECURITY => [
                self::SEVERITY_CRITICAL => 'تم رفض الوصول لأسباب أمنية.',
                self::SEVERITY_ERROR => 'فشل في التحقق من الهوية.',
                self::SEVERITY_WARNING => 'تحذير أمني: يرجى مراجعة بياناتك.',
                self::SEVERITY_NOTICE => 'تم التحقق من الأمان بنجاح.'
            ],
            
            self::ERROR_TYPE_VALIDATION => [
                self::SEVERITY_ERROR => 'البيانات المدخلة غير صحيحة.',
                self::SEVERITY_WARNING => 'بعض البيانات تحتاج مراجعة.',
                self::SEVERITY_NOTICE => 'تم التحقق من البيانات بنجاح.'
            ],
            
            self::ERROR_TYPE_FILE => [
                self::SEVERITY_CRITICAL => 'فشل في معالجة الملف.',
                self::SEVERITY_ERROR => 'خطأ في رفع الملف.',
                self::SEVERITY_WARNING => 'تم رفع الملف مع تحذيرات.',
                self::SEVERITY_NOTICE => 'تم رفع الملف بنجاح.'
            ],
            
            self::ERROR_TYPE_NETWORK => [
                self::SEVERITY_CRITICAL => 'فشل في الاتصال بالخدمة.',
                self::SEVERITY_ERROR => 'خطأ في الشبكة.',
                self::SEVERITY_WARNING => 'اتصال بطيء بالشبكة.',
                self::SEVERITY_NOTICE => 'تم الاتصال بنجاح.'
            ]
        ];
    }
    
    /**
     * الحصول على إعدادات الإشعارات
     */
    public static function getNotificationSettings() {
        return [
            'email_on_critical' => true,
            'email_on_error' => false,
            'email_on_warning' => false,
            'admin_emails' => [
                '<EMAIL>',
                '<EMAIL>'
            ],
            'max_emails_per_hour' => 10,
            'email_template_path' => __DIR__ . '/templates/error_notification.html'
        ];
    }
    
    /**
     * تهيئة نظام معالجة الأخطاء
     */
    public static function initialize() {
        // تطبيق إعدادات الأخطاء
        self::applyErrorSettings();
        
        // إنشاء مجلدات اللوجات
        $log_paths = self::getLogPaths();
        $log_dir = dirname(reset($log_paths));
        
        if (!is_dir($log_dir)) {
            mkdir($log_dir, 0750, true);
            
            // حماية مجلد اللوجات
            file_put_contents($log_dir . '/.htaccess', "Order Deny,Allow\nDeny from all");
            file_put_contents($log_dir . '/index.php', '<?php http_response_code(403); exit("Access denied"); ?>');
        }
        
        // تعيين مسار لوج الأخطاء الرئيسي
        ini_set('error_log', $log_paths['error_log']);
        
        return true;
    }
}

// تهيئة تلقائية عند تضمين الملف
if (!defined('ERROR_CONFIG_INITIALIZED')) {
    define('ERROR_CONFIG_INITIALIZED', true);
    ErrorConfig::initialize();
}

?>
