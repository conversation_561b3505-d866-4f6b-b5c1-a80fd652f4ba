@import url('root-variables.css');

/* Schedule and Breaks Styles */
.ibs-body {
    font-family: 'Roboto', sans-serif;
    background-color: var(--light-color);
    margin: 0;
    padding: 20px;
    color: var(--dark-color);
}

.ibs-main-title {
    text-align: center;
    color: var(--primary-darker);
    margin-bottom: 25px;
    font-size: 2rem;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

/* Filter Form */
.ibs-filter-form {
    background: var(--secondary-color);
    padding: 15px;
    border-radius: var(--border-radius);
    margin-bottom: 25px;
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    align-items: center;
    box-shadow: var(--shadow-light);
}

.ibs-filter-label {
    font-weight: bold;
    color: var(--primary-darker);
    margin-right: 5px;
}

.ibs-filter-input, .ibs-filter-select {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 14px;
    transition: var(--transition);
    background-color: var(--white);
}

.ibs-filter-input:focus, .ibs-filter-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 5px rgba(197, 154, 197, 0.3);
    outline: none;
}

.ibs-filter-btn {
    padding: 8px 16px;
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 14px;
    transition: var(--transition);
}

.ibs-filter-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-1px);
}

.ibs-remove-filter-btn {
    background-color: var(--danger-color);
}

.ibs-remove-filter-btn:hover {
    background-color: var(--danger-dark);
}

.ibs-export-btn {
    background-color: var(--success-color);
}

.ibs-export-btn:hover {
    background-color: #3a7a3a;
}

.ibs-add-breaks-btn {
    background-color: var(--accent-color);
}

.ibs-add-breaks-btn:hover {
    background-color: #7b1fa2;
}

/* Main Table */
.ibs-data-table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    box-shadow: var(--shadow);
    font-size: 0.95rem;
}

.ibs-table-header {
    background-color: var(--primary-dark);
    color: var(--white);
    font-weight: bold;
    text-align: center;
    padding: 12px;
    border: 1px solid var(--primary-dark);
}

.ibs-table-cell {
    border: 1px solid var(--border-color);
    padding: 12px;
    text-align: center;
    transition: var(--transition);
}

.ibs-table-row:nth-child(even) {
    background-color: var(--secondary-color);
}

.ibs-table-row:hover {
    background-color: var(--primary-light);
}

/* Action Buttons */
.ibs-action-form {
    display: inline;
    margin: 0 3px;
}

.ibs-action-btn {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    transition: var(--transition);
}

.ibs-edit-btn {
    background-color: var(--accent-color);
    color: var(--white);
}

.ibs-edit-btn:hover {
    background-color: #7b1fa2;
}

.ibs-delete-btn {
    background-color: var(--danger-color);
    color: var(--white);
}

.ibs-delete-btn:hover {
    background-color: var(--danger-dark);
}

/* Back Button */
.ibs-back-btn {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 10px 20px;
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: 30px;
    cursor: pointer;
    font-size: 16px;
    text-decoration: none;
    box-shadow: var(--shadow);
    transition: var(--transition);
    z-index: 9999;

}

.ibs-back-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}

/* Pagination */
.ibs-pagination-container {
    position: fixed;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    width: 100%;
    background-color: rgba(255,255,255,0.9);
    padding: 10px 0;
    border-top: 1px solid var(--border-color);
}

.ibs-page-info {
    font-size: 14px;
    color: var(--primary-dark);
    margin-bottom: 8px;
}

.ibs-pagination {
    display: flex;
    justify-content: center;
    gap: 6px;
}

.ibs-page-link {
    display: inline-block;
    padding: 6px 12px;
    text-decoration: none;
    color: var(--primary-dark);
    background: var(--secondary-color);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 14px;
    transition: var(--transition);
}

.ibs-page-link:hover {
    background: var(--primary-color);
    color: var(--white);
}

.ibs-active-page {
    background: var(--primary-color);
    color: var(--white);
    font-weight: bold;
}

.ibs-first-page, .ibs-last-page {
    background: var(--primary-dark);
    color: var(--white);
    font-weight: bold;
}

.ibs-first-page:hover, .ibs-last-page:hover {
    background: var(--primary-darker);
}

.ibs-prev-page, .ibs-next-page {
    font-weight: bold;
    background: var(--success-color);
    color: var(--white);
}

.ibs-prev-page:hover, .ibs-next-page:hover {
    background: #388e3c;
}

/* No Data Message */
.ibs-no-data {
    text-align: center;
    padding: 20px;
    color: var(--gray-color);
    font-style: italic;
}

/* ========== صفحة Schedule (بتصميم أهدأ) ========== */
body {
    font-family: 'Roboto', sans-serif;
    background-color: var(--primary-lighter);
    margin: 0;
    padding: 20px;
    color: var(--primary-darker);
}

.schedule-container {
    max-width: 95%;
    margin: 0 auto;
    background: var(--white);
    padding: 20px;
    border-radius: var(--border-radius);
    box-shadow: 0 0 10px rgba(197, 154, 197, 0.1);
    animation: schedule-fadeIn 0.5s ease-out;
}

.schedule-title {
    text-align: center;
    color: var(--primary-darker);
    margin-bottom: 30px;
    font-weight: 300;
    letter-spacing: 1px;
}

.schedule-alert {
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 4px;
    font-size: 16px;
    animation: schedule-fadeIn 0.3s ease-out;
}

.schedule-alert-success {
    background-color: var(--success-light);
    border-color: #c8e6c9;
    color: #2e7d32;
}

.schedule-alert-danger {
    background-color: #ffebee;
    border-color: #ffcdd2;
    color: #c62828;
}

.schedule-filter-form {
    background: var(--primary-lighter);
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;
    box-shadow: none;
    border: 1px solid var(--gray-light);
}

.schedule-filter-buttons {
    width: 100%;
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 10px;
}

.schedule-filter-label {
    font-weight: 500;
    margin-right: 5px;
    color: var(--primary-darker);
}

.schedule-filter-input, 
.schedule-filter-select {
    padding: 8px 12px;
    border: 1px solid var(--gray-light);
    border-radius: 4px;
    font-size: 14px;
    transition: var(--transition);
    background-color: var(--white);
}

.schedule-filter-input:focus, 
.schedule-filter-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 3px rgba(197, 154, 197, 0.2);
    outline: none;
}

.schedule-filter-btn {
    padding: 8px 15px;
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: var(--transition);
    opacity: 0.9;
}

.schedule-filter-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(197, 154, 197, 0.1);
    opacity: 1;
}

.schedule-remove-filter {
    background-color: var(--danger-color);
}

.schedule-remove-filter:hover {
    background-color: var(--danger-dark);
}

.schedule-export-btn {
    background-color: var(--primary-dark);
}

.schedule-export-btn:hover {
    background-color: var(--primary-darker);
}

.schedule-action-btns {
    margin-bottom: 15px;
    display: flex;
    gap: 10px;
}

.schedule-action-btn {
    padding: 8px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: var(--transition);
    font-weight: 500;
}

.schedule-action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.schedule-action-btn:active {
    transform: translateY(1px);
}

.schedule-delete-selected {
    background-color: var(--danger-color);
    color: var(--white);
}

.schedule-delete-selected:hover {
    background-color: var(--danger-dark);
}

.schedule-edit-selected {
    background-color: var(--primary-color);
    color: var(--white);
}

.schedule-edit-selected:hover {
    background-color: var(--primary-dark);
}

.schedule-table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    font-size: 14px;
    box-shadow: none;
    border: 1px solid var(--gray-light);
}

.schedule-table th, 
.schedule-table td {
    border: 1px solid var(--gray-light);
    padding: 12px;
    text-align: center;
    transition: var(--transition);
}

.schedule-table th {
    background-color: var(--primary-light);
    color: var(--primary-darker);
    font-weight: 500;
    border-bottom: 2px solid var(--primary-color);
}

.schedule-table tr:nth-child(even) {
    background-color: var(--primary-lighter);
}

.schedule-table tr:hover {
    background-color: rgba(197, 154, 197, 0.05);
    transform: none;
}

.schedule-row-checkbox:checked + td {
    background-color: rgba(197, 154, 197, 0.05);
}

.schedule-pagination {
    margin-top: 20px;
    text-align: center;
}

.schedule-pagination a {
    display: inline-block;
    padding: 8px 12px;
    margin: 0 3px;
    border: 1px solid var(--gray-light);
    border-radius: 4px;
    text-decoration: none;
    color: var(--primary-dark);
    transition: var(--transition);
}

.schedule-pagination a:hover {
    background-color: var(--primary-light);
    transform: translateY(-1px);
}

.schedule-pagination strong {
    display: inline-block;
    padding: 8px 12px;
    margin: 0 3px;
    background-color: var(--primary-color);
    color: var(--white);
    border-radius: 4px;
    font-weight: 500;
}

.schedule-row-count {
    text-align: right;
    margin-top: 10px;
    font-weight: 500;
    color: var(--primary-dark);
}

.schedule-back-btn {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 12px 24px;
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: 30px;
    cursor: pointer;
    font-size: 16px;
    text-decoration: none;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    z-index: 1000;
    transition: var(--transition);
    opacity: 0.9;
}

.schedule-back-btn:hover {
    background-color: var(--primary-dark);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    transform: translateY(-2px);
    opacity: 1;
}

.schedule-checkbox-cell {
    width: 30px;
}

.schedule-edit-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.4);
}

.schedule-edit-modal-content {
    background-color: var(--white);
    margin: 2% auto;
    padding: 20px;
    border: 1px solid var(--gray-light);
    width: 80%;
    max-width: 800px;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    animation: schedule-fadeIn 0.3s ease-out;
}

.schedule-edit-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--gray-light);
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.schedule-edit-modal-title {
    font-size: 1.5em;
    font-weight: 500;
    color: var(--primary-color);
}

.schedule-edit-close {
    color: var(--gray-color);
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: var(--transition);
}

.schedule-edit-close:hover {
    color: var(--primary-color);
    transform: rotate(90deg);
}

.schedule-edit-form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 15px;
}

.schedule-form-group {
    margin-bottom: 15px;
}

.schedule-form-label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: var(--primary-dark);
}

.schedule-form-input, 
.schedule-form-select {
    width: 100%;
    padding: 8px;
    border: 1px solid var(--gray-light);
    border-radius: 4px;
    transition: var(--transition);
    background-color: var(--white);
}

.schedule-form-input:focus, 
.schedule-form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 3px rgba(197, 154, 197, 0.2);
    outline: none;
}

.schedule-edit-modal-footer {
    margin-top: 20px;
    text-align: right;
}

.schedule-edit-modal-btn {
    padding: 8px 15px;
    margin-left: 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 500;
}

.schedule-edit-modal-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.schedule-btn-save {
    background-color: var(--primary-color);
    color: var(--white);
}

.schedule-btn-save:hover {
    background-color: var(--primary-dark);
}

.schedule-btn-cancel {
    background-color: var(--gray-color);
    color: var(--white);
}

.schedule-btn-cancel:hover {
    background-color: #7f8c8d;
}

.schedule-selected-count {
    font-weight: 500;
    color: var(--primary-color);
    margin-left: 10px;
}

/* Animations */
@keyframes schedule-fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes schedule-pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

.schedule-simple-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    margin-top: 20px;
}

.schedule-simple-pagination a {
    padding: 8px 16px;
    background-color: var(--primary-color);
    color: var(--white);
    text-decoration: none;
    border-radius: 4px;
    transition: var(--transition);
    opacity: 0.9;
}

.schedule-simple-pagination a:hover {
    background-color: var(--primary-dark);
    transform: translateY(-1px);
    opacity: 1;
}

.schedule-current-page {
    color: var(--primary-dark);
    font-weight: 500;
}

.schedule-prev-btn {
    background-color: var(--primary-dark);
}

.schedule-next-btn {
    background-color: var(--primary-dark);
}
/*==========new==========*/
