/* متغيرات الألوان المشتركة لجميع الصفحات */
 @import url('root-variables.css'); 

/* ===== تنسيقات مشتركة لجميع الصفحات ===== */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f9f9f9;
    color: var(--text-color);
    overflow-x: hidden;
}

/* ===== تنسيقات صفحة Database الرئيسية ===== */
.database-main .navbar-brand img {
    height: 40px;
    margin-right: 10px;
    transition: transform 0.3s ease;
}

.database-main .navbar-brand img:hover {
    transform: rotate(15deg);
}

.database-main .main-header {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-darker));
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
    border-radius: 0 0 20px 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    position: relative;
    overflow: hidden;
}

/* باقي تنسيقات صفحة Database الرئيسية */
.database-main .main-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
    animation: pulse-gradient 8s infinite linear;
}

@keyframes pulse-gradient {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.database-main .card {
    border-radius: 15px;
    box-shadow: 0 6px 15px rgba(0,0,0,0.1);
    border: none;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    margin-bottom: 20px;
    background-color: white;
    overflow: hidden;
}

.database-main .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 20px rgba(0,0,0,0.15);
}

.database-main .card-header {
    background: linear-gradient(to right, var(--primary-dark), var(--primary-darker));
    color: white;
    border-radius: 15px 15px 0 0 !important;
    font-weight: 600;
    position: relative;
    overflow: hidden;
}

.database-main .card-header::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to right, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
}

/* Improved table scrolling */
.database-main .table-container {
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    margin-bottom: 20px;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0,0,0,0.05);
    background-color: white;
}

.database-main .table-container::-webkit-scrollbar {
    height: 10px;
    background-color: #f5f5f5;
}

.database-main .table-container::-webkit-scrollbar-thumb {
    background: linear-gradient(to right, var(--primary-color), var(--primary-dark));
    border-radius: 10px;
}

.database-main .table-container::-webkit-scrollbar-thumb:hover {
    background: var(--primary-darker);
}

.database-main #dataTable {
    width: max-content;
    min-width: 100%;
    white-space: nowrap;
    margin-bottom: 0;
}

/* Sticky table header */
.database-main #dataTable thead th {
    position: sticky;
    top: 0;
    background: linear-gradient(to bottom, var(--primary-dark), var(--primary-darker));
    color: white;
    z-index: 10;
    border-bottom: none;
    font-weight: 500;
    padding: 12px 15px;
    text-align: center;
    transition: all 0.3s ease;
}

.database-main #dataTable thead th:hover {
    background: var(--primary-darker);
}

.database-main #dataTable td {
    vertical-align: middle;
    padding: 12px 15px;
    border: 1px solid #e9ecef;
    transition: background-color 0.2s ease;
}

.database-main #dataTable tr:nth-child(even) {
    background-color: rgba(208, 164, 208, 0.05);
}

.database-main #dataTable tr:hover {
    background-color: rgba(208, 164, 208, 0.1);
}

.database-main #dataTable tr:hover td {
    background-color: transparent;
}

.database-main .search-box {
    position: relative;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.database-main .search-box:hover {
    transform: translateY(-2px);
}

.database-main .search-box input {
    padding-left: 40px;
    border-radius: 50px;
    border: 1px solid #ddd;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.database-main .search-box input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 2px 15px rgba(208, 164, 208, 0.2);
}

.database-main .search-box i {
    position: absolute;
    left: 15px;
    top: 12px;
    color: var(--primary-dark);
    transition: all 0.3s ease;
}

.database-main .search-box:hover i {
    color: var(--primary-darker);
}

.database-main .action-buttons .btn {
    margin-right: 10px;
    border-radius: 50px;
    padding: 8px 20px;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    position: relative;
    overflow: hidden;
}

.database-main .action-buttons .btn::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.2);
    transform: translateX(-100%);
    transition: transform 0.3s ease;
}

.database-main .action-buttons .btn:hover::after {
    transform: translateX(0);
}

.database-main .action-buttons .btn i {
    margin-right: 5px;
    transition: transform 0.3s ease;
}

.database-main .action-buttons .btn:hover i {
    transform: scale(1.1);
}

.database-main .btn-primary {
    background-color: var(--primary-dark);
    border-color: var(--primary-darker);
    color: white;
}

.database-main .btn-primary:hover {
    background-color: var(--primary-darker);
    border-color: var(--primary-darker);
    color: white;
}

.database-main .btn-outline-primary {
    color: var(--primary-dark);
    border-color: var(--primary-dark);
}

.database-main .btn-outline-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    color: white;
}

.database-main .btn-success {
    background-color: var(--success-color);
    border-color: #388e3c;
}

.database-main .btn-success:hover {
    background-color: #388e3c;
    border-color: #2e7d32;
}

.database-main .btn-info {
    background-color: var(--info-color);
    border-color: #1976d2;
}

.database-main .btn-info:hover {
    background-color: #1976d2;
    border-color: #1565c0;
}

.database-main .btn-warning {
    background-color: var(--primary-color);
    border-color: var(--primary-dark);
    color: white;
}

.database-main .btn-warning:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-darker);
    color: white;
}

.database-main .btn-danger {
    background-color: var(--danger-color);
    border-color: #d32f2f;
}

.database-main .btn-danger:hover {
    background-color: #d32f2f;
    border-color: #b71c1c;
}

.database-main .btn-outline-danger {
    color: var(--danger-color);
    border-color: var(--danger-color);
}

.database-main .btn-outline-danger:hover {
    background-color: var(--danger-color);
    color: white;
}

.database-main .delete-btn {
    margin-left: 5px;
}

/* Floating action button */
.database-main .floating-btn {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(45deg, var(--primary-dark), var(--primary-darker));
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    box-shadow: 0 4px 20px rgba(184, 121, 184, 0.5);
    z-index: 100;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    animation: pulse 2s infinite;
}

.database-main .floating-btn:hover {
    transform: scale(1.1) rotate(10deg);
    box-shadow: 0 8px 25px rgba(184, 121, 184, 0.6);
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(184, 121, 184, 0.7);
        transform: scale(1);
    }
    70% {
        box-shadow: 0 0 0 15px rgba(184, 121, 184, 0);
        transform: scale(1.05);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(184, 121, 184, 0);
        transform: scale(1);
    }
}

/* تحسين مجموعة الأزرار */
.database-main .action-buttons-container {
    background: linear-gradient(135deg, rgba(208, 164, 208, 0.1), rgba(255, 255, 255, 0.2));
    border-radius: 15px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
    border: 1px solid rgba(208, 164, 208, 0.1);
    transition: all 0.3s ease;
}

.database-main .action-buttons-container:hover {
    box-shadow: 0 6px 20px rgba(208, 164, 208, 0.15);
    transform: translateY(-2px);
}

.database-main .button-group {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;
}

/* تحسين أزرار الفلاتر */
.database-main .filter-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
    border: 1px solid rgba(208, 164, 208, 0.2);
    transition: all 0.3s ease;
}

.database-main .filter-section:hover {
    box-shadow: 0 6px 20px rgba(208, 164, 208, 0.15);
}

.database-main .filter-row {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: flex-end;
}

.database-main .filter-group {
    flex: 1;
    min-width: 200px;
}

.database-main .filter-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: var(--primary-darker);
}

.database-main .filter-group select, .database-main .filter-group input {
    width: 100%;
    border-radius: 8px;
    border: 1px solid #ddd;
    padding: 8px 12px;
    transition: all 0.3s ease;
}

.database-main .filter-group select:focus, .database-main .filter-group input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(208, 164, 208, 0.1);
}

/* تعديلات قائمة المستخدم */
.database-main .dropdown-menu {
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border: none;
    padding: 5px;
}

.database-main .dropdown-item {
    border-radius: 5px;
    padding: 8px 15px;
    margin: 2px 0;
    transition: all 0.2s ease;
}

.database-main .dropdown-item:hover {
    background-color: rgba(208, 164, 208, 0.1);
    color: var(--primary-darker);
}

.database-main .dropdown-divider {
    margin: 5px 0;
}

/* Toast notifications */
.database-main .toast {
    z-index: 1100;
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Empty state styles */
.database-main .empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.database-main .empty-state i {
    font-size: 3rem;
    color: var(--primary-light);
    margin-bottom: 15px;
}

/* ===== تنسيقات صفحة addindatabase ===== */
.add-database body {
    background-color: #faf5fa;
    color: var(--text-color);
}

.add-database .logo-container {
    background-color: white;
    padding: 15px;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    text-align: center;
}

.add-database .logo-container img {
    max-height: 80px;
    transition: transform 0.3s ease;
}

.add-database .logo-container img:hover {
    transform: scale(1.05);
}

.add-database .card {
    border-radius: 15px;
    border: none;
    box-shadow: 0 6px 15px rgba(0,0,0,0.1);
    margin-bottom: 30px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.add-database .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 20px rgba(0,0,0,0.15);
}

.add-database .card-header {
    background-color: var(--primary-color);
    color: white;
    font-weight: 600;
    padding: 15px 20px;
    border-bottom: none;
}

.add-database .form-section {
    margin-bottom: 25px;
    padding: 20px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.add-database .form-label {
    font-weight: 500;
    color: var(--primary-dark);
    margin-bottom: 8px;
}

.add-database .form-control, .add-database .form-select {
    border-radius: 8px;
    padding: 10px 15px;
    border: 1px solid #ddd;
    transition: all 0.3s ease;
}

.add-database .form-control:focus, .add-database .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(208, 164, 208, 0.25);
}

.add-database .btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    padding: 10px 25px;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.add-database .btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-2px);
}

/* ===== تنسيقات صفحة editdatabase ===== */
.edit-database .logo-container {
    background-color: white;
    padding: 15px;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    text-align: center;
}

.edit-database .logo-container img {
    max-height: 80px;
    transition: transform 0.3s ease;
}

.edit-database .logo-container img:hover {
    transform: scale(1.05);
}

.edit-database .card {
    border-radius: 15px;
    border: none;
    box-shadow: 0 6px 15px rgba(0,0,0,0.1);
    margin-bottom: 30px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.edit-database .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 20px rgba(0,0,0,0.15);
}

.edit-database .card-header {
    background-color: var(--primary-color);
    color: white;
    font-weight: 600;
    padding: 15px 20px;
    border-bottom: none;
}

.edit-database .form-section {
    margin-bottom: 25px;
    padding: 20px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.edit-database .other-input {
    display: none;
    margin-top: 10px;
}

.edit-database .toggle-container {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.edit-database .toggle-label {
    margin-left: 10px;
    font-weight: 500;
    color: var(--primary-dark);
}

.edit-database .current-value {
    margin-left: auto;
    padding: 3px 8px;
    background-color: #f0f0f0;
    border-radius: 4px;
    font-size: 0.85rem;
}

/* ===== تنسيقات صفحة databaseanalysis ===== */
.database-analysis body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    padding-bottom: 50px;
}

.database-analysis .dashboard-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    padding: 1rem 0;
    margin-bottom: 2rem;
    border-radius: 0 0 20px 20px;
}

.database-analysis .dashboard-header h1 {
    font-size: 1.5rem;
    font-weight: 500;
}

.database-analysis .dashboard-header .logo-text {
    margin-left: 0;
    padding-left: 0;
}

.database-analysis .card {
    border-radius: 15px;
    box-shadow: 0 6px 15px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.database-analysis .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 20px rgba(0,0,0,0.15);
}

.database-analysis .card-header {
    background-color: var(--primary-color);
    color: white;
    border-radius: 15px 15px 0 0 !important;
}

.database-analysis .stat-card {
    text-align: center;
    padding: 20px;
    height: 100%;
}

.database-analysis .stat-value {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--primary-color);
}

.database-analysis .filter-container {
    background: white;
    padding: 15px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    margin-bottom: 20px;
}

.database-analysis .chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

/* ===== تنسيقات أزرار التبديل وعناصر التحكم المشتركة ===== */
#columnContainer {
    display: none;
    position: absolute;
    top: 50px;
    right: 199px;
    width: 600px;
    padding: 15px;
    background-color: #f9f9f9;
    border: 2px solid #ddd;
    border-radius: 5px;
    box-shadow: 0 4px 10px rgba(0,0,0,0.1);
    z-index: 9999;
    transition: all 0.3s ease;
    flex-wrap: wrap;
    justify-content: space-between;
}

#columnContainer ul {
    list-style-type: none;
    padding-left: 0;
    margin: 10px 0;
}

#columnContainer li {
    padding: 5px 0;
    font-size: 14px;
}

#toggleButton {
    margin: 10px;
    cursor: pointer;
    position: relative;
    top: 50px;
    right: 0;
    background-color: #18b010;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 5px;
    font-size: 14px;
    z-index: 10000;
    transition: background-color 0.3s ease;
    left: 1300px;
    top: -160px;
}

#toggleButton:hover {
    background-color: #18a206;
}

.columnName {
    display: inline-block;
    padding: 6px 12px;
    margin: 5px 0;
    cursor: pointer;
    background-color: #e0e0e0;
    border: none;
    width: 23%;
    text-align: center;
    border-radius: 3px;
    font-size: 12px;
    box-sizing: border-box;
}

.columnName:hover {
    background-color: #ccc;
}

/* ===== تنسيقات أزرار خاصة ===== */
.button {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 9px 12px;
    gap: 8px;
    height: 45px;
    width: 150px;
    border: none;
    background: rgb(74, 180, 50);
    border-radius: 50px;
    cursor: pointer;
    position: relative;
    left: 190px;
    top: -92px;
}

.lable {
    line-height: 22px;
    font-size: 17px;
    color: #fff;
    margin-left: 20px;
    font-family: sans-serif;
    letter-spacing: 1px;
}

.button .span {
    border-radius: 50%;
    background-color: rgb(48, 129, 29);
    padding: 10px;
    position: absolute;
    left: 0;
}

.button:hover {
    background: rgb(48, 129, 29);
}

.button:hover .svg-icon {
    animation: slope 0.8s linear infinite;
}

@keyframes slope {
    0% {}
    50% { transform: rotate(15deg); }
    100% {}       
}

/* تحسينات للعرض على الأجهزة الصغيرة */
@media (max-width: 768px) {
    .filter-group {
        min-width: 100%;
    }
    
    .button-group .btn {
        flex: 1;
        min-width: 120px;
    }
        }
 /* Custom styles for dropdown checkbox filters */
        .filter-group {
            margin-bottom: 1rem;
            position: relative;
        }

        .filter-group label {
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #495057;
        }

        .dropdown-checkbox {
            position: relative;
            width: 100%;
            z-index: 1;
        }

        .dropdown-checkbox.active {
            z-index: 99998;
        }

        .dropdown-toggle-custom {
            width: 100%;
            text-align: left;
            background-color: #fff;
            border: 1px solid #ced4da;
            border-radius: 0.375rem;
            padding: 0.375rem 0.75rem;
            font-size: 1rem;
            line-height: 1.5;
            color: #495057;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            min-height: 38px;
        }

        .dropdown-toggle-custom:hover {
            border-color: #86b7fe;
        }

        .dropdown-toggle-custom:focus {
            border-color: #86b7fe;
            outline: 0;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        }

        .dropdown-menu-custom {
            position: absolute;
            top: 100%;
            left: 0;
            z-index: 99999;
            display: none;
            width: 100%;
            min-width: 200px;
            max-height: 250px;
            overflow-y: auto;
            background-color: #fff;
            border: 1px solid #ced4da;
            border-radius: 0.375rem;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.2);
            margin-top: 2px;
        }

        .dropdown-menu-custom.show {
            display: block !important;
            z-index: 99999 !important;
        }

        .dropdown-item-custom {
            display: flex;
            align-items: center;
            padding: 0.6rem 0.75rem;
            cursor: pointer;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
            justify-content: flex-start;
            min-height: 40px;
            transition: background-color 0.15s ease-in-out;
        }

        .dropdown-item-custom:hover {
            background-color: #f8f9fa;
        }

        .dropdown-item-custom input[type="checkbox"] {
            margin-left: 0;
            margin-right: 0.75rem;
            cursor: pointer;
            width: 16px;
            height: 16px;
            flex-shrink: 0;
            accent-color: #0d6efd;
            transform: scale(1.1);
        }

        .dropdown-item-custom input[type="checkbox"]:focus {
            outline: 2px solid #86b7fe;
            outline-offset: 2px;
        }

        .dropdown-item-custom span {
            flex: 1;
            text-align: left;
            font-size: 0.9rem;
            line-height: 1.4;
            color: #495057;
            font-weight: 400;
            user-select: none;
        }

        .dropdown-item-custom:active {
            background-color: #e9ecef;
        }

        .dropdown-item-custom input[type="checkbox"]:checked + span {
            color: #0d6efd;
            font-weight: 500;
        }

        .dropdown-item-custom:has(input[type="checkbox"]:checked) {
            background-color: #f8f9ff;
        }

        .dropdown-item-custom:has(input[type="checkbox"]:checked):hover {
            background-color: #e7f1ff;
        }

        .selected-count {
            background-color: #0d6efd;
            color: white;
            border-radius: 0.25rem;
            padding: 0.125rem 0.375rem;
            font-size: 0.75rem;
            margin-left: 0.5rem;
        }

        .dropdown-arrow {
            transition: transform 0.2s;
        }

        .dropdown-arrow.rotated {
            transform: rotate(180deg);
        }

        .clear-all-btn {
            border-top: 1px solid #dee2e6;
            padding: 0.5rem 0.75rem;
            background-color: #f8f9fa;
            color: #6c757d;
            font-size: 0.875rem;
            cursor: pointer;
            text-align: center;
        }

        .clear-all-btn:hover {
            background-color: #e9ecef;
        }

        /* CRITICAL: Force dropdown to appear above everything */
        .dropdown-menu-custom {
            z-index: 999999 !important;
            position: absolute !important;
        }

        .dropdown-menu-custom.show {
            z-index: 999999 !important;
            position: absolute !important;
            display: block !important;
        }

        .dropdown-checkbox.active {
            z-index: 999998 !important;
            position: relative !important;
        }

        /* Override Bootstrap and other CSS that might interfere */
        .card, .card-body, .card-header, .table-container, .table,
        .table-responsive, .container, .row, .col-md-12 {
            z-index: auto !important;
            position: relative !important;
        }

        /* Ensure filter section has proper stacking context */
        .filter-section {
            position: relative !important;
            z-index: 999997 !important;
        }

        /* Force any potential overlapping elements to lower z-index */
        .main-content, .database-content, .table-wrapper {
            z-index: 1 !important;
        }