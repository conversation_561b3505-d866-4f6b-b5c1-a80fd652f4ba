<?php
/**
 * PHP Error Handler Integration
 * تكامل معالج الأخطاء مع PHP
 * 
 * يمكن تضمين هذا الملف في بداية ملفات PHP لمعالجة الأخطاء تلقائياً
 */

/**
 * Custom PHP error handler
 */
function customErrorHandler($errno, $errstr, $errfile, $errline) {
    // Don't handle errors that are suppressed with @
    if (!(error_reporting() & $errno)) {
        return false;
    }

    $error_types = [
        E_ERROR => 'Fatal Error',
        E_WARNING => 'Warning',
        E_PARSE => 'Parse Error',
        E_NOTICE => 'Notice',
        E_CORE_ERROR => 'Core Error',
        E_CORE_WARNING => 'Core Warning',
        E_COMPILE_ERROR => 'Compile Error',
        E_COMPILE_WARNING => 'Compile Warning',
        E_USER_ERROR => 'User Error',
        E_USER_WARNING => 'User Warning',
        E_USER_NOTICE => 'User Notice',
        E_STRICT => 'Strict Notice',
        E_RECOVERABLE_ERROR => 'Recoverable Error',
        E_DEPRECATED => 'Deprecated',
        E_USER_DEPRECATED => 'User Deprecated'
    ];

    $error_type = $error_types[$errno] ?? 'Unknown Error';
    
    // Log the error
    $log_message = "[$error_type] $errstr in $errfile on line $errline";
    error_log($log_message);

    // For fatal errors, redirect to error handler
    if (in_array($errno, [E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR, E_USER_ERROR, E_RECOVERABLE_ERROR])) {
        // Check if headers haven't been sent
        if (!headers_sent()) {
            header("Location: /App/error_handlers/error_handler.php?error=500");
            exit();
        }
    }

    return true;
}

/**
 * Custom exception handler
 */
function customExceptionHandler($exception) {
    $message = $exception->getMessage();
    $file = $exception->getFile();
    $line = $exception->getLine();
    
    // Log the exception
    error_log("Uncaught Exception: $message in $file on line $line");
    
    // Check if headers haven't been sent
    if (!headers_sent()) {
        header("Location: /App/error_handlers/error_handler.php?error=500");
        exit();
    } else {
        // If headers are sent, show a simple error message
        echo "<script>window.location.href='/App/error_handlers/error_handler.php?error=500';</script>";
    }
}

/**
 * Custom fatal error handler
 */
function customFatalErrorHandler() {
    $error = error_get_last();
    
    if ($error && in_array($error['type'], [E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR, E_USER_ERROR])) {
        // Log the fatal error
        error_log("Fatal Error: {$error['message']} in {$error['file']} on line {$error['line']}");
        
        // Try to redirect to error handler
        if (!headers_sent()) {
            header("Location: /App/error_handlers/error_handler.php?error=500");
            exit();
        }
    }
}

/**
 * Initialize error handlers
 */
function initializeErrorHandlers() {
    // Set custom error handler
    set_error_handler('customErrorHandler');
    
    // Set custom exception handler
    set_exception_handler('customExceptionHandler');
    
    // Set fatal error handler
    register_shutdown_function('customFatalErrorHandler');
}

/**
 * Handle permission denied errors specifically
 */
function handlePermissionDenied($message = "Access denied") {
    // Start session if not started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    // Log the permission denial
    logSecurityEvent('permission_denied', [
        'message' => $message,
        'url' => $_SERVER['REQUEST_URI'] ?? 'unknown',
        'user' => $_SESSION['username'] ?? 'anonymous',
        'access_level' => $_SESSION['access_level'] ?? 'none'
    ]);
    
    // Redirect to 403 handler
    if (!headers_sent()) {
        header("Location: /App/error_handlers/403_handler.php");
        exit();
    }
}

/**
 * Log security events
 */
function logSecurityEvent($event_type, $details = []) {
    try {
        // Include database connection
        include_once $_SERVER['DOCUMENT_ROOT'].'/config/Databasehost.php';
        
        if (!isset($conn) || $conn === null) {
            error_log("Database connection not available for security logging");
            return;
        }

        // Check if security_logs table exists
        $check_table = $conn->query("SHOW TABLES LIKE 'security_logs'");
        if ($check_table->num_rows == 0) {
            $create_table = "CREATE TABLE IF NOT EXISTS security_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                event_type VARCHAR(100) NOT NULL,
                user_email VARCHAR(255),
                ip_address VARCHAR(45),
                event_data TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )";
            $conn->query($create_table);
        }

        $sql = "INSERT INTO security_logs (event_type, user_email, ip_address, event_data, created_at)
                VALUES (?, ?, ?, ?, NOW())";
        $stmt = $conn->prepare($sql);
        if ($stmt) {
            $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
            $user = $_SESSION['username'] ?? 'anonymous';
            $details_json = json_encode($details);
            $stmt->bind_param("ssss", $event_type, $user, $ip, $details_json);
            $stmt->execute();
            $stmt->close();
        }
    } catch (Exception $e) {
        error_log("Security logging failed: " . $e->getMessage());
    }
}

/**
 * Check if user has permission for current page
 */
function checkCurrentPagePermission() {
    // Start session if not started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    // Check if user is logged in
    if (!isset($_SESSION['username'])) {
        handlePermissionDenied("User not logged in");
        return false;
    }
    
    return true;
}

/**
 * Safe redirect function
 */
function safeRedirect($url) {
    if (!headers_sent()) {
        header("Location: $url");
        exit();
    } else {
        echo "<script>window.location.href='$url';</script>";
        echo "<noscript><meta http-equiv='refresh' content='0;url=$url'></noscript>";
        exit();
    }
}

// Auto-initialize error handlers when this file is included
if (!defined('ERROR_HANDLERS_INITIALIZED')) {
    define('ERROR_HANDLERS_INITIALIZED', true);
    initializeErrorHandlers();
}
?>
