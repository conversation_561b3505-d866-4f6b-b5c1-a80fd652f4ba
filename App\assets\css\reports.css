 @import url('root-variables.css');

        /* تنسيقات فريدة خاصة بصفحة التقارير */
        .reports-quick-access {
            background-color: var(--reports-secondary-color);
            border-radius: var(--reports-border-radius);
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: var(--reports-shadow);
        }

        .reports-category-title {
            color: var(--reports-text-dark);
            font-weight: 600;
            border-bottom: 2px solid var(--reports-primary-color);
            padding-bottom: 8px;
            margin-bottom: 15px;
        }

        .reports-quick-btn {
            margin: 5px;
            border-radius: var(--reports-border-radius);
            transition: var(--reports-transition);
            font-weight: 500;
        }

        .reports-quick-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .reports-search-box {
            background-color: var(--reports-white);
            padding: 15px;
            border-radius: var(--reports-border-radius);
            margin-bottom: 20px;
            box-shadow: var(--reports-shadow);
        }

        /* Enhanced Tab Navigation Styling */
        .reports-tabs {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 12px;
            padding: 8px;
            margin-bottom: 25px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            border: none;
            overflow-x: auto;
            white-space: nowrap;
        }

        .reports-tabs .nav-item {
            margin: 0 2px;
        }

        .reports-tabs .nav-link {
            font-weight: 600;
            color: var(--reports-text-medium);
            background: transparent;
            border: none;
            border-radius: 8px;
            padding: 12px 20px;
            margin: 0;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 120px;
            white-space: nowrap;
            font-size: 0.95rem;
        }

        .reports-tabs .nav-link:hover {
            color: var(--reports-primary-color);
            background: rgba(255, 255, 255, 0.8);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .reports-tabs .nav-link.active {
            color: white;
            background: linear-gradient(135deg, var(--reports-primary-color) 0%, var(--reports-primary-dark) 100%);
            border: none;
            box-shadow: 0 4px 12px rgba(var(--reports-primary-color), 0.3);
            transform: translateY(-2px);
        }

        .reports-tabs .nav-link.active:hover {
            background: linear-gradient(135deg, var(--reports-primary-dark) 0%, var(--reports-primary-color) 100%);
            transform: translateY(-2px);
        }

        .reports-tabs .nav-link i {
            font-size: 1rem;
            margin-right: 8px;
            transition: transform 0.3s ease;
        }

        .reports-tabs .nav-link:hover i {
            transform: scale(1.1);
        }

        .reports-tabs .nav-link.active i {
            transform: scale(1.05);
        }

        /* Focus states for accessibility */
        .reports-tabs .nav-link:focus {
            outline: 2px solid var(--reports-primary-color);
            outline-offset: 2px;
            box-shadow: 0 0 0 3px rgba(var(--reports-primary-color), 0.2);
        }

        /* Responsive design for tabs */
        @media (max-width: 768px) {
            .reports-tabs {
                padding: 6px;
                margin-bottom: 20px;
            }

            .reports-tabs .nav-link {
                padding: 10px 16px;
                font-size: 0.9rem;
                min-width: 100px;
            }

            .reports-tabs .nav-link i {
                font-size: 0.9rem;
                margin-right: 6px;
            }
        }

        @media (max-width: 480px) {
            .reports-tabs {
                padding: 4px;
            }

            .reports-tabs .nav-link {
                padding: 8px 12px;
                font-size: 0.85rem;
                min-width: 80px;
            }

            .reports-tabs .nav-link span {
                display: none; /* Hide text on very small screens, show only icons */
            }

            .reports-tabs .nav-link i {
                margin-right: 0;
                font-size: 1.1rem;
            }
        }

        .reports-card {
            background-color: var(--reports-white);
            border-radius: var(--reports-border-radius);
            margin-bottom: 20px;
            box-shadow: var(--reports-shadow);
            transition: var(--reports-transition);
            overflow: hidden;
            width: 100%;
            max-width: 100%;
        }

        .reports-card:hover {
            transform: translateY(-5px);
        }

        .reports-card-header {
            background-color: var(--reports-primary-color);
            color: var(--reports-white);
            padding: 15px;
            font-weight: 600;
            border-radius: var(--reports-border-radius) var(--reports-border-radius) 0 0;
        }

        .reports-card-body {
            padding: 20px;
        }

        .reports-link-btn {
            display: block;
            padding: 12px 15px;
            margin-bottom: 10px;
            background-color: var(--reports-light-color);
            color: var(--reports-text-medium);
            border-radius: var(--reports-border-radius);
            text-decoration: none;
            transition: var(--reports-transition);
        }

        .reports-link-btn:hover {
            background-color: var(--reports-primary-light);
            color: var(--reports-primary-dark);
            padding-left: 20px;
        }

        .reports-link-btn i {
            margin-right: 10px;
            color: var(--reports-primary-color);
        }

        .dashboard-header {
            background: linear-gradient(135deg, var(--reports-primary-color), var(--reports-primary-dark));
            color: var(--reports-white);
            padding: 20px 0;
            margin-bottom: 30px;
            box-shadow: var(--reports-shadow);
        }

        /* Enhanced Tab Content Styling */
        .tab-content {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            margin-top: -5px;
            position: relative;
            z-index: 1;
        }

        .tab-pane {
            opacity: 0;
            transform: translateY(10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .tab-pane.show.active {
            opacity: 1;
            transform: translateY(0);
        }

        /* Tab indicator line animation */
        .reports-tabs::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            height: 3px;
            background: linear-gradient(135deg, var(--reports-primary-color) 0%, var(--reports-primary-dark) 100%);
            border-radius: 2px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 1;
        }

        /* Loading state for tab content */
        .tab-pane.loading {
            opacity: 0.6;
            pointer-events: none;
            position: relative;
        }

        .tab-pane.loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid var(--reports-primary-color);
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        /* Enhanced card hover effects */
        .reports-card {
            position: relative;
            overflow: hidden;
        }

        .reports-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .reports-card:hover::before {
            left: 100%;
        }

        /* إصلاح مشكلة عرض قسم Dashboards - حل قوي */
        .tab-content .row {
            margin-left: 0 !important;
            margin-right: 0 !important;
            width: 100% !important;
            max-width: 100% !important;
        }

        .tab-content .col-md-6 {
            padding-left: 15px !important;
            padding-right: 15px !important;
            flex: 0 0 50% !important;
            max-width: 50% !important;
            width: 50% !important;
            box-sizing: border-box !important;
        }

        /* تأكيد أن الكارتات لا تتجاوز العرض المحدد */
        #dashboards .reports-card,
        .tab-pane .reports-card {
            width: 100% !important;
            max-width: 100% !important;
            box-sizing: border-box !important;
            margin-left: 0 !important;
            margin-right: 0 !important;
        }

        /* إصلاح مشكلة Bootstrap grid */
        .container {
            max-width: 1200px !important;
            width: 100% !important;
            padding-left: 15px !important;
            padding-right: 15px !important;
        }

        /* إصلاح خاص لقسم Dashboards */
        #dashboards {
            width: 100% !important;
            max-width: 100% !important;
            overflow: hidden !important;
        }

        #dashboards .row {
            display: flex !important;
            flex-wrap: wrap !important;
            margin-left: 0 !important;
            margin-right: 0 !important;
        }

        #dashboards .col-md-6 {
            flex: 0 0 50% !important;
            max-width: 50% !important;
            padding-left: 15px !important;
            padding-right: 15px !important;
        }

        @media (max-width: 768px) {
            .tab-content .col-md-6 {
                flex: 0 0 100%;
                max-width: 100%;
            }
        }