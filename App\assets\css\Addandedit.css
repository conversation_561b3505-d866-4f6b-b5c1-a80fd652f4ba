@import url('root-variables.css');

/*=====Add break== edit break == add schudele == edit schedule ==*/

/* أنماط عامة */
body {
  background-color: var(--primary-lighter);
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rem;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: var(--text-dark);
}

/* أنماط مشتركة بين جميع المكونات */
.form-label {
  font-weight: 500;
  color: var(--text-medium);
  margin-bottom: 0.5rem;
  display: block;
}

.form-control, .form-select {
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  padding: 0.75rem 1rem;
  transition: var(--transition);
  background-color: var(--white);
  width: 100%;
  font-size: 1rem;
  line-height: 1.5;
  color: var(--text-dark);
}

.form-control:focus, .form-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.25rem rgba(199, 154, 199, 0.25);
  outline: 0;
}

.form-control[readonly] {
  background-color: var(--gray-light);
  cursor: not-allowed;
}

.input-icon {
  position: relative;
  width: 100%;
}

.input-icon i {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--primary-color);
  z-index: 2;
}

.input-icon input {
  padding-left: 40px !important;
}

.back-link {
  display: inline-block;
  text-align: center;
  margin-top: 1.5rem;
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition);
  font-weight: 500;
}

.back-link:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

/* أنماط العنوان المشتركة */
.section-title, 
.break-sched-title, 
.edit-break-title, 
.schedule-edit-title {
  color: var(--primary-darker);
  font-weight: 600;
  margin-bottom: 1.5rem;
  position: relative;
  padding-bottom: 0.5rem;
  font-size: 1.5rem;
}

.section-title::after, 
.break-sched-title::after, 
.edit-break-title::after, 
.schedule-edit-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  height: 3px;
  background: var(--primary-color);
  border-radius: 3px;
}

/* أنماط الأزرار المشتركة */
.btn-primary,
.break-sched-button,
.edit-break-button,
.edit-schedule-button {
  background-color: var(--primary-color);
  border: none;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  font-weight: 600;
  transition: var(--transition);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: 1.5rem;
  box-shadow: var(--shadow-light);
  cursor: pointer;
  font-size: 0.9rem;
  display: inline-block;
  text-align: center;
}

.btn-primary:hover,
.break-sched-button:hover,
.edit-break-button:hover,
.edit-schedule-button:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(199, 154, 199, 0.3);
}

.btn-primary:active,
.break-sched-button:active,
.edit-break-button:active,
.edit-schedule-button:active {
  transform: translateY(0);
}

/* أنماط خاصة بكل مكون */

/* break-sched-container */
.break-sched-container {
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  padding: 2rem;
  width: 100%;
  max-width: 700px;
  border: 1px solid var(--border-color);
  transition: var(--transition);
  margin: 0 auto;
}

.break-sched-container:hover {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.break-sched-title {
  text-align: center;
}

.break-sched-title::after {
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
}

.break-sched-button {
  width: 100%;
}

/* shift-container */
.shift-container {
  max-width: 900px;
  margin: 0 auto;
  width: 100%;
  padding: 1rem;
}

.shift-card {
  background: var(--white);
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--shadow);
  color: var(--text-dark);
  border: 1px solid var(--border-color);
  transition: var(--transition);
  margin-bottom: 2rem;
}

.shift-card:hover {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.shift-divider {
  border-top: 2px solid var(--border-color);
  margin: 2rem 0;
  opacity: 0.5;
}

.section-title::after {
  left: 0;
  width: 50px;
}

.input-group-text {
  background-color: var(--primary-lighter);
  color: var(--primary-darker);
  border: 1px solid var(--border-color);
  padding: 0.75rem 1rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  text-align: center;
  white-space: nowrap;
}

.floating-label {
  position: relative;
  margin-bottom: 1.5rem;
}

.floating-label label {
  position: absolute;
  top: -0.6rem;
  left: 0.75rem;
  background: var(--white);
  padding: 0 0.25rem;
  font-size: 0.8rem;
  color: var(--primary-dark);
  z-index: 10;
}

/* edit-break-container */
.edit-break-container {
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  padding: 2rem;
  width: 100%;
  max-width: 800px;
  border: 1px solid var(--border-color);
  transition: var(--transition);
  margin: 0 auto;
}

.edit-break-container:hover {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.edit-break-title {
  text-align: center;
}

.edit-break-title::after {
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
}

.edit-break-button {
  width: 100%;
}

/* schedule-edit-container */
.schedule-edit-container {
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  padding: 2rem;
  width: 100%;
  max-width: 900px;
  border: 1px solid var(--border-color);
  transition: var(--transition);
  margin: 0 auto;
}

.schedule-edit-container:hover {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.schedule-edit-title {
  text-align: center;
}

.schedule-edit-title::after {
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
}

.edit-schedule-button {
  width: 100%;
}

/* تنسيقات الشبكة والعناصر العامة */
.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -0.75rem;
}

.col-md-6 {
  flex: 0 0 50%;
  max-width: 50%;
  padding: 0 0.75rem;
  margin-bottom: 1rem;
}

.mb-3 {
  margin-bottom: 1rem;
}

.mt-3 {
  margin-top: 1rem;
}

.text-center {
  text-align: center;
}

/* أنماط التنسيق للشاشات الصغيرة */
@media (max-width: 768px) {
  body {
    padding: 1rem;
    display: block;
  }
  
  .break-sched-container,
  .edit-break-container,
  .schedule-edit-container,
  .shift-card {
    padding: 1.5rem;
  }
  
  .col-md-6 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  
  .section-title, 
  .break-sched-title, 
  .edit-break-title, 
  .schedule-edit-title {
    font-size: 1.3rem;
  }
}

@media (max-width: 576px) {
  .break-sched-container,
  .edit-break-container,
  .schedule-edit-container {
    padding: 1.25rem;
  }
  
  .form-control, .form-select {
    padding: 0.6rem 0.75rem;
  }
  
  .btn-primary,
  .break-sched-button,
  .edit-break-button,
  .edit-schedule-button {
    padding: 0.65rem 1rem;
    font-size: 0.8rem;
  }
}
