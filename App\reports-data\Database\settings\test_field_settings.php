<?php
/**
 * Test Field Settings - Comprehensive test for field_settings.php functionality
 */

session_start();
include $_SERVER['DOCUMENT_ROOT'].'/config/Databasehost.php';
include '../system/database_config.php';
require_once __DIR__ . '/../core/dynamic_column_helper.php';

// Check session and access
if (!isset($_SESSION['username']) || !isset($_SESSION['random_code'])) {
    header("Location: ../../../../Loginpage.php");
    exit();
}

$access = $_SESSION['access_level'];
$email = $_SESSION['username'];

echo "<!DOCTYPE html>";
echo "<html><head>";
echo "<title>Field Settings Test</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'>";
echo "<style>";
echo "body { background: #f8f9fa; font-family: 'Segoe UI', sans-serif; }";
echo ".debug-info { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin: 10px 0; font-family: monospace; font-size: 14px; }";
echo ".success-msg { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 8px; margin: 10px 0; }";
echo ".error-msg { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 8px; margin: 10px 0; }";
echo ".test-section { background: white; padding: 20px; border-radius: 8px; margin: 15px 0; border: 1px solid #dee2e6; }";
echo "</style>";
echo "</head><body>";

echo "<div class='container mt-4'>";
echo "<h1>🧪 Field Settings Test</h1>";

// Test 1: Check required files and functions
echo "<div class='test-section'>";
echo "<h2>📁 Test 1: File Dependencies</h2>";

$requiredFiles = [
    'Database config' => $_SERVER['DOCUMENT_ROOT'].'/config/Databasehost.php',
    'System config' => '../system/database_config.php',
    'Dynamic helper' => __DIR__ . '/../core/dynamic_column_helper.php'
];

$allFilesExist = true;
foreach ($requiredFiles as $name => $path) {
    $exists = file_exists($path);
    $color = $exists ? 'success' : 'danger';
    $icon = $exists ? '✅' : '❌';
    
    echo "<div class='alert alert-$color py-2'>";
    echo "$icon <strong>$name:</strong> " . ($exists ? 'Found' : 'Missing') . " - $path";
    echo "</div>";
    
    if (!$exists) $allFilesExist = false;
}

if ($allFilesExist) {
    echo "<div class='success-msg'>✅ All required files are present</div>";
} else {
    echo "<div class='error-msg'>❌ Some required files are missing</div>";
}
echo "</div>";

// Test 2: Check database connection and functions
echo "<div class='test-section'>";
echo "<h2>🔗 Test 2: Database Connection & Functions</h2>";

try {
    // Test database connection
    $result = $conn->query("SELECT 1");
    if ($result) {
        echo "<div class='success-msg'>✅ Database connection is working</div>";
    } else {
        throw new Exception("Query failed: " . $conn->error);
    }
    
    // Test getDatabaseColumns function
    if (function_exists('getDatabaseColumns')) {
        echo "<div class='success-msg'>✅ getDatabaseColumns function exists</div>";
        
        $columns = getDatabaseColumns($conn);
        echo "<div class='debug-info'>";
        echo "<strong>Database Columns Found:</strong> " . count($columns) . "<br>";
        echo "<details><summary>Show columns</summary>";
        echo "<ul>";
        foreach (array_slice($columns, 0, 10) as $name => $info) {
            echo "<li><strong>$name</strong> - {$info['type']}</li>";
        }
        if (count($columns) > 10) {
            echo "<li>... and " . (count($columns) - 10) . " more</li>";
        }
        echo "</ul>";
        echo "</details>";
        echo "</div>";
    } else {
        echo "<div class='error-msg'>❌ getDatabaseColumns function not found</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error-msg'>❌ Database error: " . $e->getMessage() . "</div>";
}
echo "</div>";

// Test 3: Check field configuration functions
echo "<div class='test-section'>";
echo "<h2>⚙️ Test 3: Field Configuration Functions</h2>";

$functions = [
    'loadFieldConfiguration',
    'saveFieldConfiguration', 
    'resetFieldConfiguration',
    'syncWithDatabase',
    'addNewField',
    'deleteField',
    'moveField',
    'addNewSection',
    'deleteSection',
    'bulkDeleteFields',
    'bulkEnableFields',
    'bulkDisableFields'
];

$functionsExist = 0;
foreach ($functions as $func) {
    $exists = function_exists($func);
    $color = $exists ? 'success' : 'warning';
    $icon = $exists ? '✅' : '⚠️';
    
    echo "<div class='alert alert-$color py-1'>";
    echo "$icon <strong>$func()</strong> " . ($exists ? 'exists' : 'not found');
    echo "</div>";
    
    if ($exists) $functionsExist++;
}

echo "<div class='debug-info'>";
echo "<strong>Functions Status:</strong> $functionsExist/" . count($functions) . " functions available";
echo "</div>";
echo "</div>";

// Test 4: Test field configuration loading
echo "<div class='test-section'>";
echo "<h2>📋 Test 4: Field Configuration Loading</h2>";

try {
    if (function_exists('loadFieldConfiguration')) {
        $config = loadFieldConfiguration();
        
        if (is_array($config)) {
            echo "<div class='success-msg'>✅ Field configuration loaded successfully</div>";
            
            echo "<div class='debug-info'>";
            echo "<strong>Configuration Summary:</strong><br>";
            echo "Sections: " . count($config) . "<br>";
            
            $totalFields = 0;
            foreach ($config as $sectionName => $section) {
                $fieldCount = isset($section['fields']) ? count($section['fields']) : 0;
                $totalFields += $fieldCount;
                echo "- <strong>$sectionName:</strong> $fieldCount fields<br>";
            }
            echo "<strong>Total Fields:</strong> $totalFields";
            echo "</div>";
        } else {
            echo "<div class='error-msg'>❌ Configuration is not an array</div>";
        }
    } else {
        echo "<div class='error-msg'>❌ loadFieldConfiguration function not available</div>";
    }
} catch (Exception $e) {
    echo "<div class='error-msg'>❌ Error loading configuration: " . $e->getMessage() . "</div>";
}
echo "</div>";

// Test 5: Test POST handling simulation
echo "<div class='test-section'>";
echo "<h2>🔄 Test 5: POST Action Simulation</h2>";

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    echo "<div class='debug-info'>";
    echo "<strong>🔄 Processing POST Action:</strong> " . htmlspecialchars($action) . "<br>";
    echo "<strong>User:</strong> " . $email . "<br>";
    echo "<strong>Access:</strong> " . $access . "<br>";
    echo "<strong>Time:</strong> " . date('Y-m-d H:i:s') . "<br>";
    echo "</div>";
    
    try {
        switch ($action) {
            case 'test_save':
                echo "<div class='success-msg'>✅ Test save action processed successfully</div>";
                break;
            case 'test_sync':
                echo "<div class='success-msg'>✅ Test sync action processed successfully</div>";
                break;
            case 'test_add':
                echo "<div class='success-msg'>✅ Test add action processed successfully</div>";
                break;
            default:
                echo "<div class='error-msg'>⚠️ Unknown test action: " . htmlspecialchars($action) . "</div>";
        }
    } catch (Exception $e) {
        echo "<div class='error-msg'>❌ Error processing test action: " . $e->getMessage() . "</div>";
    }
}

// Test action buttons
echo "<h3>🎮 Test Action Buttons</h3>";
echo "<div class='d-flex gap-2 flex-wrap'>";

echo "<form method='POST' class='d-inline'>";
echo "<input type='hidden' name='action' value='test_save'>";
echo "<button type='submit' class='btn btn-success'>";
echo "<i class='fas fa-save me-1'></i>Test Save";
echo "</button>";
echo "</form>";

echo "<form method='POST' class='d-inline'>";
echo "<input type='hidden' name='action' value='test_sync'>";
echo "<button type='submit' class='btn btn-info'>";
echo "<i class='fas fa-sync me-1'></i>Test Sync";
echo "</button>";
echo "</form>";

echo "<form method='POST' class='d-inline'>";
echo "<input type='hidden' name='action' value='test_add'>";
echo "<button type='submit' class='btn btn-warning'>";
echo "<i class='fas fa-plus me-1'></i>Test Add";
echo "</button>";
echo "</form>";

echo "</div>";
echo "</div>";

// Test 6: Access control test
echo "<div class='test-section'>";
echo "<h2>🔐 Test 6: Access Control</h2>";

$allowed_roles = ['Admin', 'Super Admin', 'Editor', 'admin', 'super admin', 'editor'];
$hasAccess = in_array($access, $allowed_roles);

if ($hasAccess) {
    echo "<div class='success-msg'>✅ User has required access level: $access</div>";
} else {
    echo "<div class='error-msg'>❌ User does not have required access level. Current: $access</div>";
}

echo "<div class='debug-info'>";
echo "<strong>Current User:</strong> $email<br>";
echo "<strong>Access Level:</strong> $access<br>";
echo "<strong>Allowed Roles:</strong> " . implode(', ', $allowed_roles);
echo "</div>";
echo "</div>";

// Navigation and recommendations
echo "<div class='test-section'>";
echo "<h2>🔗 Navigation & Recommendations</h2>";

echo "<h3>Quick Links:</h3>";
echo "<div class='d-flex gap-2 flex-wrap mb-3'>";
echo "<a href='field_settings.php' class='btn btn-primary'>🔙 Field Settings</a>";
echo "<a href='field_settings.php?debug=1' class='btn btn-info'>🔍 Field Settings (Debug)</a>";
echo "<a href='field_diagnostics.php' class='btn btn-secondary'>📊 Field Diagnostics</a>";
echo "</div>";

echo "<h3>🎯 Recommendations:</h3>";
echo "<ul>";
echo "<li>If all tests pass, the field_settings.php should work correctly</li>";
echo "<li>Use debug mode (?debug=1) to see detailed processing information</li>";
echo "<li>Check browser console for any JavaScript errors</li>";
echo "<li>Ensure proper file permissions for configuration files</li>";
echo "</ul>";
echo "</div>";

echo "</div>"; // container

echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js'></script>";
echo "<script>";
echo "document.addEventListener('DOMContentLoaded', function() {";
echo "  const buttons = document.querySelectorAll('button[type=\"submit\"]');";
echo "  buttons.forEach(button => {";
echo "    button.addEventListener('click', function() {";
echo "      const original = this.innerHTML;";
echo "      this.innerHTML = '<i class=\"fas fa-spinner fa-spin me-1\"></i>Processing...';";
echo "      this.disabled = true;";
echo "      setTimeout(() => { this.innerHTML = original; this.disabled = false; }, 5000);";
echo "    });";
echo "  });";
echo "});";
echo "</script>";
echo "</body></html>";
?>
