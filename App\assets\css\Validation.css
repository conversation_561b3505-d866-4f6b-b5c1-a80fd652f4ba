/* @import url('root-variables.css'); - تم تعطيله لتجنب مشاكل المسارات */

/* styles.css - الملف المشترك لجميع الصفحات */

body {
    font-family: 'Rubik', sans-serif;
    background-color: white;
    color: var(--text-color);
    line-height: 1.6;
    margin: 0;
    padding: 0;
}

/* أنماط النافبار المشتركة */
.navbar {
    background-color: white;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
    padding: 0.8rem 2rem;
    transition: all 0.3s ease;
}

.navbar.scrolled {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    padding: 0.5rem 2rem;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.3rem;
    color: var(--primary-darker);
    transition: all 0.3s ease;
}

.nav-link {
    font-weight: 500;
    padding: 0.5rem 1.2rem;
    margin: 0 0.2rem;
    border-radius: 6px;
    color: var(--text-color);
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.nav-link:hover, .nav-link.active {
    background-color: var(--primary-light);
    color: var(--primary-darker);
    transform: translateY(-2px);
}

.nav-link.active {
    font-weight: 600;
}

/* أنماط رأس الصفحة المشتركة */
.page-header {
    background-color: var(--primary-color);
    color: white;
    padding: 1.2rem 2rem;
    margin-bottom: 0;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    align-items: center;
    animation: fadeInDown 0.6s ease-out;
}

/* أنماط الفلاتر المشتركة */
.filters-container {
    padding: 1rem 2rem;
    background-color: var(--light-gray);
    border-bottom: 1px solid var(--border-color);
    animation: fadeIn 0.6s ease-out;
}

.summary-badge {
    background-color: var(--primary-darker);
    color: white;
    font-size: 1rem;
    padding: 0.7rem 1.3rem;
    border-radius: 6px;
    font-weight: 600;
    transition: all 0.3s ease;
    animation: pulse 2s infinite;
}

.summary-badge:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}

.badge-filter {
    background-color: var(--primary-light);
    color: var(--text-color);
    font-weight: 500;
    padding: 0.5rem 0.8rem;
    border-radius: 6px;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
    display: inline-block;
    font-size: 0.85rem;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.badge-filter:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* أنماط الجداول المشتركة */
.dataTables_wrapper {
    padding: 0 2rem;
    animation: fadeIn 0.8s ease-out;
}

.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_processing,
.dataTables_wrapper .dataTables_paginate {
    color: var(--text-color);
    padding: 1rem;
}

.dataTables_wrapper .dataTables_filter input {
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 0.3rem 0.6rem;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    color: var(--primary-darker) !important;
    border: 1px solid var(--border-color) !important;
    border-radius: 6px !important;
    margin: 0 3px !important;
    transition: all 0.3s ease !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current,
.dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
    background-color: var(--primary-darker) !important;
    border-color: var(--primary-darker) !important;
    color: white !important;
    transform: scale(1.05);
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background-color: rgba(203, 169, 203, 0.1) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1) !important;
}

.dt-buttons .btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    margin-right: 0.5rem;
    transition: all 0.3s ease;
}

.dt-buttons .btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* أنماط الجداول الأساسية */
table.dataTable {
    margin-top: 0 !important;
    margin-bottom: 1rem !important;
    border-collapse: collapse !important;
}

table.dataTable thead th {
    background-color: var(--primary-color) !important;
    color: white !important;
    font-weight: 600 !important;
    position: sticky !important;
    top: 0 !important;
    padding: 1rem !important;
    border-bottom: 2px solid var(--primary-dark) !important;
}

table.dataTable tbody td {
    padding: 0.8rem 1rem !important;
    vertical-align: middle !important;
    border-bottom: 1px solid var(--border-color) !important;
}

table.dataTable tbody tr:last-child td {
    border-bottom: none !important;
}

.mismatch-row {
    background-color: rgba(203, 169, 203, 0.1) !important;
}

.reason-cell {
    max-width: none !important;
    word-wrap: break-word !important;
    font-weight: 500 !important;
}

/* تأثيرات hover */
table.dataTable tbody tr {
    transition: all 0.3s ease !important;
}

table.dataTable tbody tr:hover {
    background-color: rgba(203, 169, 203, 0.15) !important;
    transform: translateX(5px) !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.08) !important;
}

/* أنماط خاصة بالصفحة الرئيسية */
.hero-section {
    background-color: var(--primary-color);
    color: white;
    padding: 5rem 0;
    text-align: center;
}

.feature-card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    margin-bottom: 2rem;
    height: 100%;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.12);
}

.feature-icon {
    font-size: 3rem;
    color: var(--primary-darker);
    margin-bottom: 1.5rem;
}

footer {
    background-color: var(--primary-darker);
    color: white;
    padding: 2rem 0;
    margin-top: 3rem;
}

/* الرسوم المتحركة */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.03); }
    100% { transform: scale(1); }
}

/* شريط التمرير المخصص */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--light-gray);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background-color: var(--primary-color);
    border-radius: 10px;
    transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
    background-color: var(--primary-dark);
}

/* التكيف مع الأجهزة المختلفة */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.1rem;
    }
    
    .nav-link {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
    }
    
    .page-header {
        padding: 1rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .dataTables_wrapper {
        padding: 0 1rem;
    }
    
    .filters-container {
        padding: 0.8rem 1rem;
    }
    
    table.dataTable thead th, 
    table.dataTable tbody td {
        padding: 0.6rem !important;
        font-size: 0.85rem !important;
    }
    
    .hero-section {
        padding: 3rem 0;
    }
    
    .feature-card {
        margin-bottom: 1.5rem;
    }
}