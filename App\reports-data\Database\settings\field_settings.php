<?php
/**
 * Field Settings Management System
 * Manage form fields for addindatabse.php and editdatabase.php
 */

session_start();
include $_SERVER['DOCUMENT_ROOT'].'/config/Databasehost.php';
include '../system/database_config.php';
require_once __DIR__ . '/../core/dynamic_column_helper.php';

// Check session and access
if (!isset($_SESSION['username']) || !isset($_SESSION['random_code'])) {
    header("Location: ../../../../Loginpage.php");
    exit();
}

$access = $_SESSION['access_level'];
$email = $_SESSION['username'];

// Debug mode
$debug = isset($_GET['debug']) ? true : false;

if ($debug) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
}

// Check access level
$allowed_roles = ['Admin', 'Super Admin', 'Editor', 'admin', 'super admin', 'editor'];
if (!in_array($access, $allowed_roles)) {
    header("Location: /App/access_denied.php?page=Field Settings&required_role=Admin/Super Admin/Editor");
    exit();
}

$message = '';
$messageType = '';

// Get current database columns
$columns = getDatabaseColumns($conn);

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    if ($debug) {
        echo "<div style='background: #fff3cd; padding: 10px; margin: 10px 0; border-radius: 5px; font-family: monospace;'>";
        echo "<strong>🔍 Debug Mode - POST Processing</strong><br>";
        echo "Action: " . htmlspecialchars($action) . "<br>";
        echo "User: " . $email . "<br>";
        echo "Access: " . $access . "<br>";
        echo "Time: " . date('Y-m-d H:i:s') . "<br>";
        if (!empty($_POST)) {
            echo "POST Data: <pre>" . print_r($_POST, true) . "</pre>";
        }
        echo "</div>";
    }

    try {
        switch ($action) {
            case 'save_field_config':
                if ($debug) echo "<p>🔄 Calling saveFieldConfiguration()...</p>";
                $result = saveFieldConfiguration();
                break;
            case 'reset_config':
                if ($debug) echo "<p>🔄 Calling resetFieldConfiguration()...</p>";
                $result = resetFieldConfiguration();
                break;
            case 'sync_with_db':
                if ($debug) echo "<p>🔄 Calling syncWithDatabase()...</p>";
                $result = syncWithDatabase();
                break;
            case 'add_field':
                if ($debug) echo "<p>🔄 Calling addNewField()...</p>";
                $result = addNewField();
                break;
            case 'delete_field':
                if ($debug) echo "<p>🔄 Calling deleteField()...</p>";
                $result = deleteField();
                break;
            case 'move_field':
                if ($debug) echo "<p>🔄 Calling moveField()...</p>";
                $result = moveField();
                break;
            case 'add_section':
                if ($debug) echo "<p>🔄 Calling addNewSection()...</p>";
                $result = addNewSection();
                break;
            case 'delete_section':
                if ($debug) echo "<p>🔄 Calling deleteSection()...</p>";
                $result = deleteSection();
                break;
            case 'bulk_delete_fields':
                if ($debug) echo "<p>🔄 Calling bulkDeleteFields()...</p>";
                $result = bulkDeleteFields();
                break;
            case 'bulk_enable_fields':
                if ($debug) echo "<p>🔄 Calling bulkEnableFields()...</p>";
                $result = bulkEnableFields();
                break;
            case 'bulk_disable_fields':
                if ($debug) echo "<p>🔄 Calling bulkDisableFields()...</p>";
                $result = bulkDisableFields();
                break;
            default:
                if ($debug) echo "<p style='color: orange;'>⚠️ Unknown action: " . htmlspecialchars($action) . "</p>";
                $result = ['success' => false, 'message' => 'Unknown action: ' . htmlspecialchars($action)];
                break;
        }

        if (isset($result)) {
            $message = $result['message'];
            $messageType = $result['success'] ? 'success' : 'danger';

            // Add JavaScript to log the result
            echo "<script>";
            echo "document.addEventListener('DOMContentLoaded', function() {";
            echo "console.group('📊 Field Settings Operation Result');";
            echo "console.log('⏰ Completion time:', new Date().toLocaleString());";
            echo "console.log('🎯 Action completed:', '" . htmlspecialchars($action) . "');";
            echo "console.log('✅ Success:', " . ($result['success'] ? 'true' : 'false') . ");";
            echo "console.log('💬 Message:', '" . htmlspecialchars($result['message']) . "');";
            if (isset($result['details'])) {
                echo "console.log('📋 Details:', " . json_encode($result['details']) . ");";
            }
            echo "console.groupEnd();";
            echo "});";
            echo "</script>";

            if ($debug) {
                $color = $result['success'] ? 'green' : 'red';
                echo "<p style='color: $color;'>Result: " . $message . "</p>";
            }
        }

    } catch (Exception $e) {
        $message = "❌ Error processing action: " . $e->getMessage();
        $messageType = 'danger';
        error_log("Field Settings Error: " . $e->getMessage());

        // Add JavaScript to log the error
        echo "<script>";
        echo "document.addEventListener('DOMContentLoaded', function() {";
        echo "console.group('❌ Field Settings Operation Error');";
        echo "console.log('⏰ Error time:', new Date().toLocaleString());";
        echo "console.log('🎯 Failed action:', '" . htmlspecialchars($action ?? 'unknown') . "');";
        echo "console.error('💥 Error message:', '" . htmlspecialchars($e->getMessage()) . "');";
        echo "console.log('📁 Error file:', '" . addslashes($e->getFile()) . "');";
        echo "console.log('📍 Error line:', " . $e->getLine() . ");";
        echo "console.groupEnd();";
        echo "});";
        echo "</script>";

        if ($debug) {
            echo "<div style='background: #f8d7da; padding: 10px; margin: 10px 0; border-radius: 5px; color: #721c24;'>";
            echo "<strong>❌ Exception Details:</strong><br>";
            echo "Message: " . $e->getMessage() . "<br>";
            echo "File: " . $e->getFile() . "<br>";
            echo "Line: " . $e->getLine() . "<br>";
            echo "<pre>" . $e->getTraceAsString() . "</pre>";
            echo "</div>";
        }
    }
}

// Load current field configuration
$fieldConfig = loadFieldConfiguration();

function saveFieldConfiguration() {
    global $conn, $email;

    try {
        // Build config from POST data (compatible with working version)
        $config = [];

        if (isset($_POST['sections'])) {
            foreach ($_POST['sections'] as $sectionKey => $sectionData) {
                $config[$sectionKey] = [
                    'title' => $sectionData['title'] ?? ucwords(str_replace('_', ' ', $sectionKey)),
                    'icon' => $sectionData['icon'] ?? 'fa-cog',
                    'fields' => []
                ];

                if (isset($_POST['fields'][$sectionKey])) {
                    foreach ($_POST['fields'][$sectionKey] as $fieldName => $fieldData) {
                        $config[$sectionKey]['fields'][$fieldName] = [
                            'enabled' => isset($fieldData['enabled']) ? '1' : '0',
                            'type' => $fieldData['type'] ?? 'text',
                            'col' => $fieldData['col'] ?? '6',
                            'icon' => $fieldData['icon'] ?? 'fa-edit',
                            'source_type' => $fieldData['source_type'] ?? 'custom',
                            'options' => $fieldData['options'] ?? [],
                            'label' => $fieldData['label'] ?? ucwords(str_replace('_', ' ', $fieldName))
                        ];
                    }
                }
            }
        } else {
            // Fallback to old format
            $config = $_POST['field_config'] ?? [];

            // Process dropdown options
            foreach ($config as $sectionKey => &$section) {
                if (isset($section['fields'])) {
                    foreach ($section['fields'] as $fieldKey => &$field) {
                        if (isset($field['options']) && is_string($field['options'])) {
                            // Convert string options to array (only for custom source type)
                            if (!isset($field['source_type']) || $field['source_type'] === 'custom') {
                                $optionsArray = array_filter(array_map('trim', explode("\n", $field['options'])));
                                $field['options'] = $optionsArray;
                            }
                        }
                    }
                }
            }
        }

        $configJson = json_encode($config, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

        // Try to save to file first
        $configFile = __DIR__ . '/field_config.json';
        $fileSaved = false;

        if (is_writable(dirname($configFile))) {
            $result = file_put_contents($configFile, $configJson);
            if ($result !== false) {
                $fileSaved = true;
            }
        }

        // If file save failed, use database fallback
        if (!$fileSaved) {
            // Create table if not exists
            $conn->query("CREATE TABLE IF NOT EXISTS field_config_storage (
                id INT PRIMARY KEY AUTO_INCREMENT,
                config_key VARCHAR(255) UNIQUE,
                config_data TEXT,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )");

            $stmt = $conn->prepare("INSERT INTO field_config_storage (config_key, config_data) VALUES (?, ?)
                                   ON DUPLICATE KEY UPDATE config_data = VALUES(config_data)");
            $key = "main_config";
            $stmt->bind_param("ss", $key, $configJson);

            if (!$stmt->execute()) {
                throw new Exception("Failed to save to database: " . $conn->error);
            }
        }

        // Log the change
        try {
            $auditQuery = "INSERT INTO audit_log (
                record_id, changed_by, change_date, field_name,
                old_value, new_value, action, updated_from
            ) VALUES (
                'field_config', ?, NOW(), 'Field Configuration',
                '', ?, 'Update', 'field_settings'
            )";
            $stmt = $conn->prepare($auditQuery);
            $message = $fileSaved ? 'Updated field configuration (file)' : 'Updated field configuration (database)';
            $stmt->bind_param("ss", $email, $message);
            $stmt->execute();
        } catch (Exception $e) {
            // Log error but don't fail the save
            error_log("Audit log error: " . $e->getMessage());
        }

        $saveMethod = $fileSaved ? 'file' : 'database';
        return ['success' => true, 'message' => "Field configuration saved successfully to $saveMethod!"];

    } catch (Exception $e) {
        error_log("Save config error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Error saving configuration: ' . $e->getMessage()];
    }
}

function resetFieldConfiguration() {
    global $conn, $email;
    
    try {
        $configFile = __DIR__ . '/field_config.json';
        if (file_exists($configFile)) {
            unlink($configFile);
        }
        
        // Log the change
        $auditQuery = "INSERT INTO audit_log (
            record_id, changed_by, change_date, field_name,
            old_value, new_value, action, updated_from
        ) VALUES (
            'field_config', '$email', NOW(), 'Field Configuration',
            'Custom config', 'Reset to default', 'Reset', 'field_settings'
        )";
        $conn->query($auditQuery);
        
        return ['success' => true, 'message' => 'Field configuration reset to default!'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Error resetting configuration: ' . $e->getMessage()];
    }
}

function syncWithDatabase() {
    global $conn, $email;

    try {
        // Get current database columns
        $result = $conn->query("DESCRIBE databasehc");
        if (!$result) {
            return ['success' => false, 'message' => 'Failed to get database structure: ' . $conn->error];
        }

        $dbColumns = [];
        while ($row = $result->fetch_assoc()) {
            $dbColumns[$row['Field']] = [
                'type' => $row['Type'],
                'null' => $row['Null'],
                'key' => $row['Key'],
                'default' => $row['Default'],
                'extra' => $row['Extra']
            ];
        }

        // Load current configuration
        $config = loadFieldConfiguration();

        // Get all currently configured fields
        $configuredFields = [];
        foreach ($config as $sectionKey => $section) {
            if (isset($section['fields'])) {
                foreach ($section['fields'] as $fieldName => $fieldConfig) {
                    $configuredFields[] = $fieldName;
                }
            }
        }

        // Find new fields (in database but not in config)
        $newFields = array_diff(array_keys($dbColumns), $configuredFields);

        // Add new fields to appropriate sections
        $addedCount = 0;
        foreach ($newFields as $fieldName) {
            // Skip system fields
            if (in_array($fieldName, ['ID', 'created_at', 'updated_at'])) {
                continue;
            }

            $section = determineBestSection($fieldName, $dbColumns[$fieldName]);

            // Ensure section exists
            if (!isset($config[$section])) {
                $config[$section] = [
                    'title' => ucwords(str_replace('_', ' ', $section)),
                    'icon' => 'fa-cog',
                    'fields' => []
                ];
            }

            // Add field to section
            $config[$section]['fields'][$fieldName] = [
                'enabled' => '1',
                'type' => mapDatabaseTypeToInputType($dbColumns[$fieldName]['type']),
                'col' => '6',
                'icon' => getFieldIcon($fieldName),
                'source_type' => 'database',
                'options' => [],
                'label' => ucwords(str_replace('_', ' ', $fieldName))
            ];

            $addedCount++;
        }

        // Save updated configuration
        $configFile = __DIR__ . '/field_config.json';
        $result = file_put_contents($configFile, json_encode($config, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

        if ($result === false) {
            return ['success' => false, 'message' => 'Failed to save updated configuration'];
        }

        // Log the sync
        if (isset($email)) {
            $auditQuery = "INSERT INTO audit_log (
                record_id, changed_by, change_date, field_name,
                old_value, new_value, action, updated_from
            ) VALUES (
                'field_config', ?, NOW(), 'Field Configuration',
                '', ?, 'Sync', 'field_settings'
            )";

            $stmt = $conn->prepare($auditQuery);
            if ($stmt) {
                $message = "Synced with database: added $addedCount new fields";
                $stmt->bind_param("ss", $email, $message);
                $stmt->execute();
            }
        }

        return ['success' => true, 'message' => "Successfully synced with database. Added $addedCount new fields."];

    } catch (Exception $e) {
        error_log("Error syncing with database: " . $e->getMessage());
        return ['success' => false, 'message' => 'Error syncing with database: ' . $e->getMessage()];
    }
}

// Helper functions for sync
function determineBestSection($fieldName, $fieldInfo) {
    $fieldLower = strtolower($fieldName);

    if (strpos($fieldLower, 'email') !== false) return 'contact_info';
    if (strpos($fieldLower, 'phone') !== false) return 'contact_info';
    if (strpos($fieldLower, 'address') !== false) return 'contact_info';
    if (strpos($fieldLower, 'date') !== false) return 'dates';
    if (strpos($fieldLower, 'status') !== false) return 'work_details';
    if (strpos($fieldLower, 'language') !== false) return 'language_info';
    if (strpos($fieldLower, 'name') !== false) return 'basic_info';

    return 'additional';
}

function mapDatabaseTypeToInputType($dbType) {
    $dbType = strtolower($dbType);

    if (strpos($dbType, 'text') !== false) return 'textarea';
    if (strpos($dbType, 'date') !== false) return 'date';
    if (strpos($dbType, 'time') !== false) return 'time';
    if (strpos($dbType, 'email') !== false) return 'email';
    if (strpos($dbType, 'int') !== false) return 'number';
    if (strpos($dbType, 'decimal') !== false) return 'number';
    if (strpos($dbType, 'float') !== false) return 'number';
    if (strpos($dbType, 'enum') !== false) return 'select';

    return 'text';
}

function getFieldIcon($fieldName) {
    $fieldLower = strtolower($fieldName);

    if (strpos($fieldLower, 'email') !== false) return 'fa-envelope';
    if (strpos($fieldLower, 'phone') !== false) return 'fa-phone';
    if (strpos($fieldLower, 'date') !== false) return 'fa-calendar';
    if (strpos($fieldLower, 'time') !== false) return 'fa-clock';
    if (strpos($fieldLower, 'language') !== false) return 'fa-globe';
    if (strpos($fieldLower, 'name') !== false) return 'fa-user';
    if (strpos($fieldLower, 'status') !== false) return 'fa-info-circle';
    if (strpos($fieldLower, 'password') !== false) return 'fa-key';

    return 'fa-edit';
}

function loadFieldConfiguration() {
    global $conn;

    $configFile = __DIR__ . '/field_config.json';

    // Try file first
    if (file_exists($configFile) && is_readable($configFile)) {
        $content = file_get_contents($configFile);
        $config = json_decode($content, true);

        if (json_last_error() === JSON_ERROR_NONE && !empty($config)) {
            return $config;
        }
    }

    // Fallback to database storage
    try {
        // Create table if not exists
        $conn->query("CREATE TABLE IF NOT EXISTS field_config_storage (
            id INT PRIMARY KEY AUTO_INCREMENT,
            config_key VARCHAR(255) UNIQUE,
            config_data TEXT,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");

        $stmt = $conn->prepare("SELECT config_data FROM field_config_storage WHERE config_key = ?");
        $key = "main_config";
        $stmt->bind_param("s", $key);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($row = $result->fetch_assoc()) {
            $config = json_decode($row['config_data'], true);
            if (!empty($config)) {
                return $config;
            }
        }
    } catch (Exception $e) {
        error_log("Database config load error: " . $e->getMessage());
    }

    // Return default configuration based on current database schema
    return getFieldConfigurations($conn);
}

function addNewField() {
    global $conn, $email;

    try {
        $fieldName = $_POST['field_name'] ?? '';
        $fieldLabel = $_POST['field_label'] ?? '';
        $fieldType = $_POST['field_type'] ?? 'text';
        $fieldIcon = $_POST['field_icon'] ?? 'fa-edit';
        $fieldSection = $_POST['field_section'] ?? 'additional';
        $fieldCol = (int)($_POST['field_col'] ?? 6);
        $fieldRequired = isset($_POST['field_required']);
        $fieldOther = isset($_POST['field_other']);

        if (empty($fieldName) || empty($fieldLabel)) {
            return ['success' => false, 'message' => 'Field name and label are required'];
        }

        // Load current configuration
        $config = loadFieldConfiguration();

        // Check if field already exists
        foreach ($config as $sectionKey => $section) {
            if (isset($section['fields'][$fieldName])) {
                return ['success' => false, 'message' => 'Field already exists'];
            }
        }

        // Create new field configuration
        $newField = [
            'type' => $fieldType,
            'label' => $fieldLabel,
            'icon' => $fieldIcon,
            'col' => $fieldCol,
            'required' => $fieldRequired,
            'enabled' => true
        ];

        if ($fieldType === 'select' || $fieldType === 'dropdown') {
            $newField['other'] = $fieldOther;
        }

        // Handle dropdown options
        if ($fieldType === 'dropdown') {
            $sourceType = $_POST['field_source_type'] ?? 'custom';
            $newField['source_type'] = $sourceType;

            if ($sourceType === 'custom' && isset($_POST['field_options'])) {
                $options = $_POST['field_options'];
                if (!empty($options)) {
                    // Split by lines and clean up
                    $optionsArray = array_filter(array_map('trim', explode("\n", $options)));
                    $newField['options'] = $optionsArray;
                }
            }
            // For database source, options will be loaded dynamically
        }

        // Ensure section exists
        if (!isset($config[$fieldSection])) {
            $config[$fieldSection] = [
                'title' => ucwords(str_replace('_', ' ', $fieldSection)),
                'icon' => 'fa-cog',
                'fields' => []
            ];
        }

        // Add field to section
        $config[$fieldSection]['fields'][$fieldName] = $newField;

        // Save configuration
        $configFile = __DIR__ . '/field_config.json';
        file_put_contents($configFile, json_encode($config, JSON_PRETTY_PRINT));

        // Log the change
        $auditQuery = "INSERT INTO audit_log (
            record_id, changed_by, change_date, field_name,
            old_value, new_value, action, updated_from
        ) VALUES (
            'field_config', '$email', NOW(), 'Add Field',
            '', '$fieldName added to $fieldSection', 'Add', 'field_settings'
        )";
        $conn->query($auditQuery);

        return ['success' => true, 'message' => "Field '$fieldName' added successfully!"];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Error adding field: ' . $e->getMessage()];
    }
}

function deleteField() {
    global $conn, $email;

    try {
        $fieldName = $_POST['field_name'] ?? '';
        $sectionKey = $_POST['section_key'] ?? '';

        if (empty($fieldName) || empty($sectionKey)) {
            return ['success' => false, 'message' => 'Field name and section are required'];
        }

        // Load current configuration
        $config = loadFieldConfiguration();

        // Check if field exists
        if (!isset($config[$sectionKey]['fields'][$fieldName])) {
            return ['success' => false, 'message' => 'Field not found'];
        }

        // Remove field
        unset($config[$sectionKey]['fields'][$fieldName]);

        // Remove section if empty
        if (empty($config[$sectionKey]['fields'])) {
            unset($config[$sectionKey]);
        }

        // Save configuration
        $configFile = __DIR__ . '/field_config.json';
        file_put_contents($configFile, json_encode($config, JSON_PRETTY_PRINT));

        // Log the change
        $auditQuery = "INSERT INTO audit_log (
            record_id, changed_by, change_date, field_name,
            old_value, new_value, action, updated_from
        ) VALUES (
            'field_config', '$email', NOW(), 'Delete Field',
            '$fieldName from $sectionKey', '', 'Delete', 'field_settings'
        )";
        $conn->query($auditQuery);

        return ['success' => true, 'message' => "Field '$fieldName' deleted successfully!"];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Error deleting field: ' . $e->getMessage()];
    }
}

function moveField() {
    global $conn, $email;

    try {
        $fieldName = $_POST['field_name'] ?? '';
        $fromSection = $_POST['from_section'] ?? '';
        $toSection = $_POST['to_section'] ?? '';

        if (empty($fieldName) || empty($fromSection) || empty($toSection)) {
            return ['success' => false, 'message' => 'All parameters are required'];
        }

        if ($fromSection === $toSection) {
            return ['success' => false, 'message' => 'Source and destination sections are the same'];
        }

        // Load current configuration
        $config = loadFieldConfiguration();

        // Check if field exists in source section
        if (!isset($config[$fromSection]['fields'][$fieldName])) {
            return ['success' => false, 'message' => 'Field not found in source section'];
        }

        // Get field configuration
        $fieldConfig = $config[$fromSection]['fields'][$fieldName];

        // Ensure destination section exists
        if (!isset($config[$toSection])) {
            $config[$toSection] = [
                'title' => ucwords(str_replace('_', ' ', $toSection)),
                'icon' => 'fa-cog',
                'fields' => []
            ];
        }

        // Move field
        $config[$toSection]['fields'][$fieldName] = $fieldConfig;
        unset($config[$fromSection]['fields'][$fieldName]);

        // Remove source section if empty
        if (empty($config[$fromSection]['fields'])) {
            unset($config[$fromSection]);
        }

        // Save configuration
        $configFile = __DIR__ . '/field_config.json';
        file_put_contents($configFile, json_encode($config, JSON_PRETTY_PRINT));

        // Log the change
        $auditQuery = "INSERT INTO audit_log (
            record_id, changed_by, change_date, field_name,
            old_value, new_value, action, updated_from
        ) VALUES (
            'field_config', '$email', NOW(), 'Move Field',
            '$fieldName from $fromSection', '$fieldName to $toSection', 'Move', 'field_settings'
        )";
        $conn->query($auditQuery);

        return ['success' => true, 'message' => "Field '$fieldName' moved successfully!"];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Error moving field: ' . $e->getMessage()];
    }
}

function addNewSection() {
    global $conn, $email;

    try {
        $sectionKey = $_POST['section_key'] ?? '';
        $sectionTitle = $_POST['section_title'] ?? '';
        $sectionIcon = $_POST['section_icon'] ?? 'fa-cog';

        if (empty($sectionKey) || empty($sectionTitle)) {
            return ['success' => false, 'message' => 'Section key and title are required'];
        }

        // Validate section key format
        if (!preg_match('/^[a-z_]+$/', $sectionKey)) {
            return ['success' => false, 'message' => 'Section key must contain only lowercase letters and underscores'];
        }

        // Load current configuration
        $config = loadFieldConfiguration();

        // Check if section already exists
        if (isset($config[$sectionKey])) {
            return ['success' => false, 'message' => 'Section already exists'];
        }

        // Create new section
        $config[$sectionKey] = [
            'title' => $sectionTitle,
            'icon' => $sectionIcon,
            'fields' => []
        ];

        // Save configuration
        $configFile = __DIR__ . '/field_config.json';
        file_put_contents($configFile, json_encode($config, JSON_PRETTY_PRINT));

        // Log the change
        $auditQuery = "INSERT INTO audit_log (
            record_id, changed_by, change_date, field_name,
            old_value, new_value, action, updated_from
        ) VALUES (
            'field_config', '$email', NOW(), 'Add Section',
            '', '$sectionKey: $sectionTitle', 'Add', 'field_settings'
        )";
        $conn->query($auditQuery);

        return ['success' => true, 'message' => "Section '$sectionTitle' added successfully!"];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Error adding section: ' . $e->getMessage()];
    }
}

function deleteSection() {
    global $conn, $email;

    try {
        $sectionKey = $_POST['section_key'] ?? '';

        if (empty($sectionKey)) {
            return ['success' => false, 'message' => 'Section key is required'];
        }

        // Load current configuration
        $config = loadFieldConfiguration();

        // Check if section exists
        if (!isset($config[$sectionKey])) {
            return ['success' => false, 'message' => 'Section not found'];
        }

        // Check if section has fields
        if (!empty($config[$sectionKey]['fields'])) {
            return ['success' => false, 'message' => 'Cannot delete section with fields. Move or delete fields first.'];
        }

        $sectionTitle = $config[$sectionKey]['title'];

        // Remove section
        unset($config[$sectionKey]);

        // Save configuration
        $configFile = __DIR__ . '/field_config.json';
        file_put_contents($configFile, json_encode($config, JSON_PRETTY_PRINT));

        // Log the change
        $auditQuery = "INSERT INTO audit_log (
            record_id, changed_by, change_date, field_name,
            old_value, new_value, action, updated_from
        ) VALUES (
            'field_config', '$email', NOW(), 'Delete Section',
            '$sectionKey: $sectionTitle', '', 'Delete', 'field_settings'
        )";
        $conn->query($auditQuery);

        return ['success' => true, 'message' => "Section '$sectionTitle' deleted successfully!"];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Error deleting section: ' . $e->getMessage()];
    }
}

function bulkDeleteFields() {
    global $conn, $email;

    try {
        $selectedFields = $_POST['selected_fields'] ?? [];

        if (empty($selectedFields)) {
            return ['success' => false, 'message' => 'No fields selected for deletion'];
        }

        // Load current configuration
        $config = loadFieldConfiguration();

        $deletedCount = 0;
        $deletedFields = [];

        foreach ($selectedFields as $fieldIdentifier) {
            // Parse field identifier (format: sectionKey|fieldName)
            $parts = explode('|', $fieldIdentifier);
            if (count($parts) !== 2) continue;

            $sectionKey = $parts[0];
            $fieldName = $parts[1];

            if (isset($config[$sectionKey]['fields'][$fieldName])) {
                unset($config[$sectionKey]['fields'][$fieldName]);
                $deletedFields[] = $fieldName;
                $deletedCount++;

                // Remove section if empty
                if (empty($config[$sectionKey]['fields'])) {
                    unset($config[$sectionKey]);
                }
            }
        }

        if ($deletedCount > 0) {
            $configJson = json_encode($config, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

            // Try to save to file first
            $configFile = __DIR__ . '/field_config.json';
            $fileSaved = false;

            if (is_writable(dirname($configFile))) {
                $result = file_put_contents($configFile, $configJson);
                if ($result !== false) {
                    $fileSaved = true;
                }
            }

            // If file save failed, use database fallback
            if (!$fileSaved) {
                $stmt = $conn->prepare("INSERT INTO field_config_storage (config_key, config_data) VALUES (?, ?)
                                       ON DUPLICATE KEY UPDATE config_data = VALUES(config_data)");
                $key = "main_config";
                $stmt->bind_param("ss", $key, $configJson);

                if (!$stmt->execute()) {
                    throw new Exception("Failed to save to database: " . $conn->error);
                }
            }

            // Log the change
            try {
                $deletedFieldsList = implode(', ', $deletedFields);
                $auditQuery = "INSERT INTO audit_log (
                    record_id, changed_by, change_date, field_name,
                    old_value, new_value, action, updated_from
                ) VALUES (
                    'field_config', ?, NOW(), 'Bulk Delete Fields',
                    ?, '', 'Bulk Delete', 'field_settings'
                )";
                $stmt = $conn->prepare($auditQuery);
                $stmt->bind_param("ss", $email, $deletedFieldsList);
                $stmt->execute();
            } catch (Exception $e) {
                error_log("Audit log error: " . $e->getMessage());
            }

            $saveMethod = $fileSaved ? 'file' : 'database';
            return ['success' => true, 'message' => "Successfully deleted $deletedCount fields (saved to $saveMethod): " . implode(', ', $deletedFields)];
        }

        return ['success' => false, 'message' => 'No fields were deleted'];
    } catch (Exception $e) {
        error_log("Bulk delete error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Error deleting fields: ' . $e->getMessage()];
    }
}

function bulkEnableFields() {
    global $conn, $email;

    try {
        $selectedFields = $_POST['selected_fields'] ?? [];

        if (empty($selectedFields)) {
            return ['success' => false, 'message' => 'No fields selected'];
        }

        // Load current configuration
        $config = loadFieldConfiguration();

        $enabledCount = 0;
        $enabledFields = [];

        foreach ($selectedFields as $fieldIdentifier) {
            $parts = explode('|', $fieldIdentifier);
            if (count($parts) !== 2) continue;

            $sectionKey = $parts[0];
            $fieldName = $parts[1];

            if (isset($config[$sectionKey]['fields'][$fieldName])) {
                $config[$sectionKey]['fields'][$fieldName]['enabled'] = true;
                $enabledFields[] = $fieldName;
                $enabledCount++;
            }
        }

        if ($enabledCount > 0) {
            // Save configuration
            $configFile = __DIR__ . '/field_config.json';
            file_put_contents($configFile, json_encode($config, JSON_PRETTY_PRINT));

            // Log the change
            $enabledFieldsList = implode(', ', $enabledFields);
            $auditQuery = "INSERT INTO audit_log (
                record_id, changed_by, change_date, field_name,
                old_value, new_value, action, updated_from
            ) VALUES (
                'field_config', '$email', NOW(), 'Bulk Enable Fields',
                '', '$enabledFieldsList enabled', 'Bulk Enable', 'field_settings'
            )";
            $conn->query($auditQuery);
        }

        return ['success' => true, 'message' => "Successfully enabled $enabledCount fields"];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Error enabling fields: ' . $e->getMessage()];
    }
}

function bulkDisableFields() {
    global $conn, $email;

    try {
        $selectedFields = $_POST['selected_fields'] ?? [];

        if (empty($selectedFields)) {
            return ['success' => false, 'message' => 'No fields selected'];
        }

        // Load current configuration
        $config = loadFieldConfiguration();

        $disabledCount = 0;
        $disabledFields = [];

        foreach ($selectedFields as $fieldIdentifier) {
            $parts = explode('|', $fieldIdentifier);
            if (count($parts) !== 2) continue;

            $sectionKey = $parts[0];
            $fieldName = $parts[1];

            if (isset($config[$sectionKey]['fields'][$fieldName])) {
                $config[$sectionKey]['fields'][$fieldName]['enabled'] = false;
                $disabledFields[] = $fieldName;
                $disabledCount++;
            }
        }

        if ($disabledCount > 0) {
            // Save configuration
            $configFile = __DIR__ . '/field_config.json';
            file_put_contents($configFile, json_encode($config, JSON_PRETTY_PRINT));

            // Log the change
            $disabledFieldsList = implode(', ', $disabledFields);
            $auditQuery = "INSERT INTO audit_log (
                record_id, changed_by, change_date, field_name,
                old_value, new_value, action, updated_from
            ) VALUES (
                'field_config', '$email', NOW(), 'Bulk Disable Fields',
                '$disabledFieldsList disabled', '', 'Bulk Disable', 'field_settings'
            )";
            $conn->query($auditQuery);
        }

        return ['success' => true, 'message' => "Successfully disabled $disabledCount fields"];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Error disabling fields: ' . $e->getMessage()];
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Field Settings Management</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome 6 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS -->
    
    <style>
        .field-config-section {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 20px;
            background: #f8f9fa;
        }
        
        .field-config-header {
            background: linear-gradient(135deg, #8a5f8a, #6a4c93);
            color: white;
            padding: 15px;
            border-radius: 8px 8px 0 0;
            cursor: pointer;
        }
        
        .field-config-body {
            padding: 20px;
        }
        
        .field-item {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 10px;
        }
        
        .field-controls {
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .field-controls label {
            margin: 0;
            font-weight: 500;
        }
        
        .field-controls input,
        .field-controls select {
            flex: 1;
            min-width: 120px;
        }
        
        .toggle-section {
            background: #e9ecef;
            border-radius: 4px;
            padding: 5px 10px;
        }

        .field-config-section {
            transition: all 0.3s ease;
        }

        .field-config-section:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .field-item {
            transition: all 0.2s ease;
        }

        .field-item:hover {
            background: #f8f9fa;
            border-color: #8a5f8a;
        }

        .btn-group-sm .btn {
            transition: all 0.2s ease;
        }

        .btn-group-sm .btn:hover {
            transform: scale(1.05);
        }

        .modal-header {
            background: linear-gradient(135deg, #8a5f8a, #6a4c93);
            color: white;
        }

        .modal-header .btn-close {
            filter: invert(1);
        }

        .badge {
            font-size: 0.75em;
        }

        .field-config-header {
            transition: all 0.3s ease;
        }

        .field-config-header:hover {
            background: linear-gradient(135deg, #9a6f9a, #7a5ca3);
        }

        /* Bulk Selection Styles */
        .field-selector, .section-selector {
            transform: scale(1.2);
            cursor: pointer;
        }

        .field-selector:checked, .section-selector:checked {
            background-color: #8a5f8a;
            border-color: #8a5f8a;
        }

        /* Debug Mode Styles */
        .debug-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            border-left: 4px solid #ffc107;
        }

        .debug-info strong {
            color: #856404;
        }

        .debug-info pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
        }

        .save-indicator {
            margin-left: 5px;
            font-size: 12px;
        }

        .field-item.selected {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-color: #8a5f8a;
            box-shadow: 0 2px 8px rgba(138, 95, 138, 0.2);
        }

        #bulkActionsBar {
            animation: slideDown 0.3s ease-out;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .btn-group .btn:hover {
            z-index: 1;
        }

        /* Indeterminate checkbox styling */
        input[type="checkbox"]:indeterminate {
            background-color: #8a5f8a;
            border-color: #8a5f8a;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/%3e%3c/svg%3e");
        }

        /* Dropdown options section styling */
        .dropdown-options-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin-top: 10px;
        }

        .dropdown-options-section textarea {
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-0">
                            <i class="fas fa-cogs me-2"></i>
                            Field Settings Management
                        </h1>
                        <p class="text-muted mb-0">Configure form fields for Add and Edit Database pages</p>
                    </div>
                    <div>
                        <a href="../index.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i> Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Messages -->
        <?php if ($message): ?>
            <div class="alert alert-<?= $messageType ?> alert-dismissible fade show">
                <i class="fas fa-<?= $messageType === 'success' ? 'check-circle' : 'exclamation-circle' ?> me-2"></i>
                <?= htmlspecialchars($message) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- System Status -->
        <div class="alert alert-info alert-dismissible fade show">
            <i class="fas fa-rocket me-2"></i>
            <strong>System Enhanced!</strong> This page now uses database storage as fallback if file permissions are restricted.
            All functions (Save, Sync, Delete) work seamlessly.
            <?php
            $configFile = __DIR__ . '/field_config.json';
            $fileWritable = is_writable($configFile) || is_writable(dirname($configFile));
            if ($fileWritable) {
                echo '<span class="badge bg-success ms-2">File Storage Available</span>';
            } else {
                echo '<span class="badge bg-warning ms-2">Using Database Storage</span>';
            }
            ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <!-- Action Buttons -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex gap-2 flex-wrap">
                            <form method="POST" class="d-inline" id="syncForm">
                                <input type="hidden" name="action" value="sync_with_db">
                                <button type="submit" class="btn btn-info" id="syncButton">
                                    <i class="fas fa-sync me-1"></i> Sync with Database
                                </button>
                            </form>

                            <form method="POST" class="d-inline" id="resetForm" onsubmit="return confirmReset()">
                                <input type="hidden" name="action" value="reset_config">
                                <button type="submit" class="btn btn-warning" id="resetButton">
                                    <i class="fas fa-undo me-1"></i> Reset to Default
                                </button>
                            </form>
                            
                            <a href="../management/manage_columns.php" class="btn btn-primary">
                                <i class="fas fa-columns me-1"></i> Manage Database Columns
                            </a>

                            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addFieldModal">
                                <i class="fas fa-plus me-1"></i> Add New Field
                            </button>

                            <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#addSectionModal">
                                <i class="fas fa-layer-group me-1"></i> Add New Section
                            </button>

                            <a href="field_diagnostics.php" class="btn btn-outline-info">
                                <i class="fas fa-stethoscope me-1"></i> Field Diagnostics
                            </a>

                            <div class="form-check d-inline-block ms-3">
                                <input type="checkbox" class="form-check-input" id="selectAllFields" onchange="toggleAllFields(this.checked)">
                                <label class="form-check-label" for="selectAllFields" title="Ctrl+A to select all, Escape to clear, Delete to remove selected">
                                    <strong>Select All Fields</strong>
                                    <small class="text-muted d-block">Ctrl+A, Esc, Del</small>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bulk Actions Bar -->
        <div class="row mb-4" id="bulkActionsBar" style="display: none;">
            <div class="col-12">
                <div class="card border-primary">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-0">
                                    <i class="fas fa-check-square me-2"></i>
                                    <span id="selectedCount">0</span> fields selected
                                </h6>
                                <small class="text-muted">
                                    <span id="selectedStats"></span>
                                </small>
                            </div>
                            <div class="btn-group">
                                <button type="button" class="btn btn-success btn-sm" onclick="bulkAction('enable')">
                                    <i class="fas fa-check me-1"></i> Enable Selected
                                </button>
                                <button type="button" class="btn btn-warning btn-sm" onclick="bulkAction('disable')">
                                    <i class="fas fa-times me-1"></i> Disable Selected
                                </button>
                                <button type="button" class="btn btn-danger btn-sm" onclick="bulkAction('delete')">
                                    <i class="fas fa-trash me-1"></i> Delete Selected
                                </button>
                                <button type="button" class="btn btn-secondary btn-sm" onclick="clearSelection()">
                                    <i class="fas fa-times me-1"></i> Clear Selection
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Field Configuration Form -->
        <form method="POST" id="fieldConfigForm">
            <input type="hidden" name="action" value="save_field_config">
            
            <?php foreach ($fieldConfig as $sectionKey => $section): ?>
                <div class="field-config-section">
                    <div class="field-config-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center flex-1">
                                <!-- Select All Checkbox for Section -->
                                <?php if (!empty($section['fields'])): ?>
                                    <input type="checkbox"
                                           class="form-check-input me-3 section-selector"
                                           data-section="<?= $sectionKey ?>"
                                           onchange="toggleSectionFields('<?= $sectionKey ?>', this.checked)"
                                           title="Select all fields in this section">
                                <?php endif; ?>

                                <div data-bs-toggle="collapse" data-bs-target="#section-<?= $sectionKey ?>" style="cursor: pointer; flex: 1;">
                                    <i class="fas <?= $section['icon'] ?? 'fa-cog' ?> me-2"></i>
                                    <?= htmlspecialchars($section['title']) ?>
                                    <span class="badge bg-light text-dark ms-2"><?= count($section['fields']) ?> fields</span>
                                </div>
                            </div>

                            <div class="d-flex align-items-center gap-2">
                                <?php if (empty($section['fields'])): ?>
                                    <button type="button" class="btn btn-sm btn-outline-light"
                                            onclick="deleteSection('<?= $sectionKey ?>', '<?= htmlspecialchars($section['title']) ?>')"
                                            title="Delete Empty Section">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                <?php endif; ?>
                                <i class="fas fa-chevron-down" data-bs-toggle="collapse" data-bs-target="#section-<?= $sectionKey ?>" style="cursor: pointer;"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="collapse show" id="section-<?= $sectionKey ?>">
                        <div class="field-config-body">
                            <?php foreach ($section['fields'] as $fieldKey => $field): ?>
                                <div class="field-item">
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <div class="d-flex align-items-center">
                                            <!-- Selection Checkbox -->
                                            <input type="checkbox"
                                                   class="form-check-input me-3 field-selector"
                                                   value="<?= $sectionKey ?>|<?= $fieldKey ?>"
                                                   onchange="updateBulkActions()">

                                            <h6 class="mb-0">
                                                <i class="fas <?= $field['icon'] ?? 'fa-edit' ?> me-2"></i>
                                                <?= htmlspecialchars($field['label'] ?? $fieldKey) ?>
                                                <small class="text-muted">(<?= $fieldKey ?>)</small>

                                                <!-- Status Badge -->
                                                <?php if (!($field['enabled'] ?? true)): ?>
                                                    <span class="badge bg-secondary ms-2">Disabled</span>
                                                <?php endif; ?>
                                            </h6>
                                        </div>

                                        <div class="d-flex align-items-center gap-2">
                                            <div class="toggle-section">
                                                <input type="checkbox"
                                                       name="field_config[<?= $sectionKey ?>][fields][<?= $fieldKey ?>][enabled]"
                                                       value="1"
                                                       <?= ($field['enabled'] ?? true) ? 'checked' : '' ?>
                                                       class="form-check-input me-1">
                                                <label class="form-check-label">Enabled</label>
                                            </div>

                                            <!-- Field Actions -->
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-outline-primary btn-sm"
                                                        onclick="moveField('<?= $fieldKey ?>', '<?= $sectionKey ?>')"
                                                        title="Move Field">
                                                    <i class="fas fa-arrows-alt"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-danger btn-sm"
                                                        onclick="deleteField('<?= $fieldKey ?>', '<?= $sectionKey ?>')"
                                                        title="Delete Field">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="row g-3">
                                        <div class="col-md-3">
                                            <label class="form-label">Field Type</label>
                                            <select name="field_config[<?= $sectionKey ?>][fields][<?= $fieldKey ?>][type]" class="form-select" onchange="toggleDropdownOptions(this, '<?= $sectionKey ?>', '<?= $fieldKey ?>')">
                                                <option value="text" <?= ($field['type'] ?? '') === 'text' ? 'selected' : '' ?>>Text</option>
                                                <option value="select" <?= ($field['type'] ?? '') === 'select' ? 'selected' : '' ?>>Select (Auto)</option>
                                                <option value="dropdown" <?= ($field['type'] ?? '') === 'dropdown' ? 'selected' : '' ?>>Dropdown (Custom)</option>
                                                <option value="textarea" <?= ($field['type'] ?? '') === 'textarea' ? 'selected' : '' ?>>Textarea</option>
                                                <option value="date" <?= ($field['type'] ?? '') === 'date' ? 'selected' : '' ?>>Date</option>
                                                <option value="number" <?= ($field['type'] ?? '') === 'number' ? 'selected' : '' ?>>Number</option>
                                                <option value="email" <?= ($field['type'] ?? '') === 'email' ? 'selected' : '' ?>>Email</option>
                                                <option value="checkbox" <?= ($field['type'] ?? '') === 'checkbox' ? 'selected' : '' ?>>Checkbox</option>
                                            </select>
                                        </div>
                                        
                                        <div class="col-md-3">
                                            <label class="form-label">Column Width</label>
                                            <select name="field_config[<?= $sectionKey ?>][fields][<?= $fieldKey ?>][col]" class="form-select">
                                                <option value="3" <?= ($field['col'] ?? 6) == 3 ? 'selected' : '' ?>>3 (25%)</option>
                                                <option value="4" <?= ($field['col'] ?? 6) == 4 ? 'selected' : '' ?>>4 (33%)</option>
                                                <option value="6" <?= ($field['col'] ?? 6) == 6 ? 'selected' : '' ?>>6 (50%)</option>
                                                <option value="8" <?= ($field['col'] ?? 6) == 8 ? 'selected' : '' ?>>8 (66%)</option>
                                                <option value="12" <?= ($field['col'] ?? 6) == 12 ? 'selected' : '' ?>>12 (100%)</option>
                                            </select>
                                        </div>
                                        
                                        <div class="col-md-3">
                                            <label class="form-label">Icon</label>
                                            <input type="text" 
                                                   name="field_config[<?= $sectionKey ?>][fields][<?= $fieldKey ?>][icon]" 
                                                   value="<?= htmlspecialchars($field['icon'] ?? 'fa-edit') ?>" 
                                                   class="form-control"
                                                   placeholder="fa-edit">
                                        </div>
                                        
                                        <div class="col-md-3">
                                            <div class="form-check">
                                                <input type="checkbox"
                                                       name="field_config[<?= $sectionKey ?>][fields][<?= $fieldKey ?>][required]"
                                                       value="1"
                                                       <?= ($field['required'] ?? false) ? 'checked' : '' ?>
                                                       class="form-check-input">
                                                <label class="form-check-label">Required</label>
                                            </div>

                                            <div class="form-check">
                                                <input type="checkbox"
                                                       name="field_config[<?= $sectionKey ?>][fields][<?= $fieldKey ?>][other]"
                                                       value="1"
                                                       <?= ($field['other'] ?? false) ? 'checked' : '' ?>
                                                       class="form-check-input">
                                                <label class="form-check-label">Allow "Other"</label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Dropdown Options Section -->
                                    <div class="row g-3 mt-2 dropdown-options-section" id="dropdown_options_<?= $sectionKey ?>_<?= $fieldKey ?>" style="display: <?= ($field['type'] ?? '') === 'dropdown' ? 'block' : 'none' ?>;">
                                        <div class="col-12">
                                            <label class="form-label">
                                                <i class="fas fa-cog me-1"></i>
                                                Dropdown Source
                                            </label>
                                            <div class="btn-group w-100 mb-3" role="group">
                                                <input type="radio" class="btn-check" name="field_config[<?= $sectionKey ?>][fields][<?= $fieldKey ?>][source_type]"
                                                       id="source_custom_<?= $sectionKey ?>_<?= $fieldKey ?>" value="custom"
                                                       <?= (!isset($field['source_type']) || $field['source_type'] === 'custom') ? 'checked' : '' ?>
                                                       onchange="toggleDropdownSource('<?= $sectionKey ?>', '<?= $fieldKey ?>', 'custom')">
                                                <label class="btn btn-outline-primary" for="source_custom_<?= $sectionKey ?>_<?= $fieldKey ?>">
                                                    <i class="fas fa-edit me-1"></i>Custom Options
                                                </label>

                                                <input type="radio" class="btn-check" name="field_config[<?= $sectionKey ?>][fields][<?= $fieldKey ?>][source_type]"
                                                       id="source_database_<?= $sectionKey ?>_<?= $fieldKey ?>" value="database"
                                                       <?= (isset($field['source_type']) && $field['source_type'] === 'database') ? 'checked' : '' ?>
                                                       onchange="toggleDropdownSource('<?= $sectionKey ?>', '<?= $fieldKey ?>', 'database')">
                                                <label class="btn btn-outline-success" for="source_database_<?= $sectionKey ?>_<?= $fieldKey ?>">
                                                    <i class="fas fa-database me-1"></i>Database Values
                                                </label>
                                            </div>
                                        </div>

                                        <!-- Custom Options -->
                                        <div class="col-12" id="custom_options_<?= $sectionKey ?>_<?= $fieldKey ?>" style="display: <?= (!isset($field['source_type']) || $field['source_type'] === 'custom') ? 'block' : 'none' ?>;">
                                            <label class="form-label">
                                                <i class="fas fa-list me-1"></i>
                                                Custom Options (one per line)
                                            </label>
                                            <textarea name="field_config[<?= $sectionKey ?>][fields][<?= $fieldKey ?>][options]"
                                                      class="form-control"
                                                      rows="4"
                                                      placeholder="Option 1&#10;Option 2&#10;Option 3"><?= isset($field['options']) ? (is_array($field['options']) ? implode("\n", $field['options']) : $field['options']) : '' ?></textarea>
                                            <small class="text-muted">Enter each option on a new line. These will be the available choices in the dropdown.</small>
                                        </div>

                                        <!-- Database Options Info -->
                                        <div class="col-12" id="database_options_<?= $sectionKey ?>_<?= $fieldKey ?>" style="display: <?= (isset($field['source_type']) && $field['source_type'] === 'database') ? 'block' : 'none' ?>;">
                                            <div class="alert alert-info">
                                                <i class="fas fa-info-circle me-2"></i>
                                                <strong>Database Values:</strong> Options will be automatically loaded from existing values in the database column "<?= $fieldKey ?>".
                                                <br><small>The system will fetch distinct values from the column and display them as dropdown options.</small>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Hidden fields to preserve section info -->
                                    <input type="hidden" name="field_config[<?= $sectionKey ?>][title]" value="<?= htmlspecialchars($section['title']) ?>">
                                    <input type="hidden" name="field_config[<?= $sectionKey ?>][icon]" value="<?= htmlspecialchars($section['icon'] ?? 'fa-cog') ?>">
                                    <input type="hidden" name="field_config[<?= $sectionKey ?>][fields][<?= $fieldKey ?>][label]" value="<?= htmlspecialchars($field['label'] ?? $fieldKey) ?>">
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
            
            <!-- Save Button -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body text-center">
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="fas fa-save me-2"></i>
                                Save Field Configuration
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- Add Field Modal -->
    <div class="modal fade" id="addFieldModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-plus me-2"></i>Add New Field
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <input type="hidden" name="action" value="add_field">
                    <div class="modal-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label">Field Name (Database Column)</label>
                                <input type="text" name="field_name" class="form-control" required
                                       placeholder="e.g., NewField" pattern="[A-Za-z_][A-Za-z0-9_]*">
                                <small class="text-muted">Must match database column name exactly</small>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Display Label</label>
                                <input type="text" name="field_label" class="form-control" required
                                       placeholder="e.g., New Field">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Field Type</label>
                                <select name="field_type" class="form-select" required onchange="toggleAddFieldDropdownOptions(this)">
                                    <option value="text">Text</option>
                                    <option value="select">Select (Auto)</option>
                                    <option value="dropdown">Dropdown (Custom)</option>
                                    <option value="textarea">Textarea</option>
                                    <option value="date">Date</option>
                                    <option value="number">Number</option>
                                    <option value="email">Email</option>
                                    <option value="checkbox">Checkbox</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Section</label>
                                <select name="field_section" class="form-select" required>
                                    <?php foreach ($fieldConfig as $sectionKey => $section): ?>
                                        <option value="<?= $sectionKey ?>"><?= htmlspecialchars($section['title']) ?></option>
                                    <?php endforeach; ?>
                                    <option value="additional">Additional Information</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Column Width</label>
                                <select name="field_col" class="form-select">
                                    <option value="3">3 (25%)</option>
                                    <option value="4">4 (33%)</option>
                                    <option value="6" selected>6 (50%)</option>
                                    <option value="8">8 (66%)</option>
                                    <option value="12">12 (100%)</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Icon (FontAwesome)</label>
                                <input type="text" name="field_icon" class="form-control"
                                       placeholder="fa-edit" value="fa-edit">
                            </div>
                            <div class="col-md-6">
                                <div class="form-check mt-4">
                                    <input type="checkbox" name="field_required" class="form-check-input">
                                    <label class="form-check-label">Required Field</label>
                                </div>
                                <div class="form-check">
                                    <input type="checkbox" name="field_other" class="form-check-input">
                                    <label class="form-check-label">Allow "Other" Option (for select fields)</label>
                                </div>
                            </div>

                            <!-- Dropdown Options for Add Field Modal -->
                            <div class="col-12" id="addFieldDropdownOptions" style="display: none;">
                                <label class="form-label">
                                    <i class="fas fa-cog me-1"></i>
                                    Dropdown Source
                                </label>
                                <div class="btn-group w-100 mb-3" role="group">
                                    <input type="radio" class="btn-check" name="field_source_type" id="add_source_custom" value="custom" checked
                                           onchange="toggleAddFieldDropdownSource('custom')">
                                    <label class="btn btn-outline-primary" for="add_source_custom">
                                        <i class="fas fa-edit me-1"></i>Custom Options
                                    </label>

                                    <input type="radio" class="btn-check" name="field_source_type" id="add_source_database" value="database"
                                           onchange="toggleAddFieldDropdownSource('database')">
                                    <label class="btn btn-outline-success" for="add_source_database">
                                        <i class="fas fa-database me-1"></i>Database Values
                                    </label>
                                </div>

                                <!-- Custom Options -->
                                <div id="addFieldCustomOptions">
                                    <label class="form-label">
                                        <i class="fas fa-list me-1"></i>
                                        Custom Options (one per line)
                                    </label>
                                    <textarea name="field_options" class="form-control" rows="4"
                                              placeholder="Option 1&#10;Option 2&#10;Option 3"></textarea>
                                    <small class="text-muted">Enter each option on a new line. These will be the available choices in the dropdown.</small>
                                </div>

                                <!-- Database Options Info -->
                                <div id="addFieldDatabaseOptions" style="display: none;">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>Database Values:</strong> Options will be automatically loaded from existing values in the database column.
                                        <br><small>The system will fetch distinct values from the column and display them as dropdown options.</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-plus me-1"></i>Add Field
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Move Field Modal -->
    <div class="modal fade" id="moveFieldModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-arrows-alt me-2"></i>Move Field
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <input type="hidden" name="action" value="move_field">
                    <input type="hidden" name="field_name" id="moveFieldName">
                    <input type="hidden" name="from_section" id="moveFromSection">
                    <div class="modal-body">
                        <p>Move field <strong id="moveFieldDisplay"></strong> to:</p>
                        <select name="to_section" class="form-select" required>
                            <?php foreach ($fieldConfig as $sectionKey => $section): ?>
                                <option value="<?= $sectionKey ?>"><?= htmlspecialchars($section['title']) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-arrows-alt me-1"></i>Move Field
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Field Modal -->
    <div class="modal fade" id="deleteFieldModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title text-danger">
                        <i class="fas fa-trash me-2"></i>Delete Field
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <input type="hidden" name="action" value="delete_field">
                    <input type="hidden" name="field_name" id="deleteFieldName">
                    <input type="hidden" name="section_key" id="deleteSectionKey">
                    <div class="modal-body">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Warning!</strong> This will permanently remove the field from the form configuration.
                        </div>
                        <p>Are you sure you want to delete field <strong id="deleteFieldDisplay"></strong>?</p>
                        <p class="text-muted">Note: This only removes the field from forms, not from the database.</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-1"></i>Delete Field
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Add Section Modal -->
    <div class="modal fade" id="addSectionModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-layer-group me-2"></i>Add New Section
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <input type="hidden" name="action" value="add_section">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">Section Key</label>
                            <input type="text" name="section_key" class="form-control" required
                                   placeholder="e.g., custom_section" pattern="[a-z_]+">
                            <small class="text-muted">Lowercase letters and underscores only</small>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Section Title</label>
                            <input type="text" name="section_title" class="form-control" required
                                   placeholder="e.g., Custom Section">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Icon (FontAwesome)</label>
                            <input type="text" name="section_icon" class="form-control"
                                   placeholder="fa-cog" value="fa-cog">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-info">
                            <i class="fas fa-layer-group me-1"></i>Add Section
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Section Modal -->
    <div class="modal fade" id="deleteSectionModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title text-danger">
                        <i class="fas fa-trash me-2"></i>Delete Section
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <input type="hidden" name="action" value="delete_section">
                    <input type="hidden" name="section_key" id="deleteSectionKey2">
                    <div class="modal-body">
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Warning!</strong> This will permanently remove the section.
                        </div>
                        <p>Are you sure you want to delete section <strong id="deleteSectionDisplay"></strong>?</p>
                        <p class="text-muted">Note: You can only delete empty sections.</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-1"></i>Delete Section
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        function moveField(fieldName, sectionKey) {
            document.getElementById('moveFieldName').value = fieldName;
            document.getElementById('moveFromSection').value = sectionKey;
            document.getElementById('moveFieldDisplay').textContent = fieldName;

            // Remove current section from options
            const select = document.querySelector('#moveFieldModal select[name="to_section"]');
            Array.from(select.options).forEach(option => {
                option.disabled = option.value === sectionKey;
            });

            new bootstrap.Modal(document.getElementById('moveFieldModal')).show();
        }

        function deleteField(fieldName, sectionKey) {
            document.getElementById('deleteFieldName').value = fieldName;
            document.getElementById('deleteSectionKey').value = sectionKey;
            document.getElementById('deleteFieldDisplay').textContent = fieldName;

            new bootstrap.Modal(document.getElementById('deleteFieldModal')).show();
        }

        function deleteSection(sectionKey, sectionTitle) {
            document.getElementById('deleteSectionKey2').value = sectionKey;
            document.getElementById('deleteSectionDisplay').textContent = sectionTitle;

            new bootstrap.Modal(document.getElementById('deleteSectionModal')).show();
        }

        // Bulk Actions Functions
        function updateBulkActions() {
            const selectedFields = document.querySelectorAll('.field-selector:checked');
            const bulkActionsBar = document.getElementById('bulkActionsBar');
            const selectedCount = document.getElementById('selectedCount');
            const selectedStats = document.getElementById('selectedStats');

            if (selectedFields.length > 0) {
                bulkActionsBar.style.display = 'block';
                selectedCount.textContent = selectedFields.length;

                // Calculate stats
                let enabledCount = 0;
                let disabledCount = 0;
                const sections = new Set();

                selectedFields.forEach(field => {
                    const fieldItem = field.closest('.field-item');
                    const enabledCheckbox = fieldItem.querySelector('input[name*="[enabled]"]');
                    const sectionKey = field.value.split('|')[0];

                    sections.add(sectionKey);

                    if (enabledCheckbox && enabledCheckbox.checked) {
                        enabledCount++;
                    } else {
                        disabledCount++;
                    }
                });

                selectedStats.textContent = `${enabledCount} enabled, ${disabledCount} disabled • ${sections.size} sections`;
            } else {
                bulkActionsBar.style.display = 'none';
            }

            // Update section checkboxes
            updateSectionCheckboxes();

            // Update master checkbox
            updateMasterCheckbox();
        }

        function toggleAllFields(checked) {
            const fieldSelectors = document.querySelectorAll('.field-selector');
            const sectionSelectors = document.querySelectorAll('.section-selector');

            fieldSelectors.forEach(checkbox => {
                checkbox.checked = checked;
            });

            sectionSelectors.forEach(checkbox => {
                checkbox.checked = checked;
            });

            updateBulkActions();
        }

        function toggleSectionFields(sectionKey, checked) {
            const sectionFields = document.querySelectorAll(`.field-selector[value^="${sectionKey}|"]`);

            sectionFields.forEach(checkbox => {
                checkbox.checked = checked;
            });

            updateBulkActions();
        }

        function updateSectionCheckboxes() {
            const sectionSelectors = document.querySelectorAll('.section-selector');

            sectionSelectors.forEach(sectionCheckbox => {
                const sectionKey = sectionCheckbox.dataset.section;
                const sectionFields = document.querySelectorAll(`.field-selector[value^="${sectionKey}|"]`);
                const checkedFields = document.querySelectorAll(`.field-selector[value^="${sectionKey}|"]:checked`);

                if (sectionFields.length === 0) {
                    sectionCheckbox.indeterminate = false;
                    sectionCheckbox.checked = false;
                } else if (checkedFields.length === sectionFields.length) {
                    sectionCheckbox.indeterminate = false;
                    sectionCheckbox.checked = true;
                } else if (checkedFields.length > 0) {
                    sectionCheckbox.indeterminate = true;
                    sectionCheckbox.checked = false;
                } else {
                    sectionCheckbox.indeterminate = false;
                    sectionCheckbox.checked = false;
                }
            });
        }

        function updateMasterCheckbox() {
            const masterCheckbox = document.getElementById('selectAllFields');
            const allFields = document.querySelectorAll('.field-selector');
            const checkedFields = document.querySelectorAll('.field-selector:checked');

            if (allFields.length === 0) {
                masterCheckbox.indeterminate = false;
                masterCheckbox.checked = false;
            } else if (checkedFields.length === allFields.length) {
                masterCheckbox.indeterminate = false;
                masterCheckbox.checked = true;
            } else if (checkedFields.length > 0) {
                masterCheckbox.indeterminate = true;
                masterCheckbox.checked = false;
            } else {
                masterCheckbox.indeterminate = false;
                masterCheckbox.checked = false;
            }
        }

        function clearSelection() {
            const allCheckboxes = document.querySelectorAll('.field-selector, .section-selector, #selectAllFields');
            allCheckboxes.forEach(checkbox => {
                checkbox.checked = false;
                checkbox.indeterminate = false;
            });
            updateBulkActions();
        }

        function bulkAction(action) {
            console.group('🔥 Bulk Action Started');
            console.log('⏰ Timestamp:', new Date().toLocaleString());
            console.log('🎯 Action type:', action);

            const selectedFields = document.querySelectorAll('.field-selector:checked');
            const selectedValues = Array.from(selectedFields).map(cb => cb.value);

            console.log('📊 Selected fields count:', selectedFields.length);
            console.log('📋 Selected field values:', selectedValues);

            if (selectedValues.length === 0) {
                console.warn('⚠️ No fields selected for bulk action');
                alert('Please select at least one field');
                console.groupEnd();
                return;
            }

            let confirmMessage = '';
            let actionName = '';

            switch (action) {
                case 'delete':
                    confirmMessage = `Are you sure you want to delete ${selectedFields.length} selected fields?`;
                    actionName = 'bulk_delete_fields';
                    console.log('🗑️ Bulk delete operation initiated');
                    break;
                case 'enable':
                    confirmMessage = `Enable ${selectedFields.length} selected fields?`;
                    actionName = 'bulk_enable_fields';
                    console.log('✅ Bulk enable operation initiated');
                    break;
                case 'disable':
                    confirmMessage = `Disable ${selectedFields.length} selected fields?`;
                    actionName = 'bulk_disable_fields';
                    console.log('❌ Bulk disable operation initiated');
                    break;
            }

            console.log('📝 Action name:', actionName);
            console.log('💬 Confirmation message:', confirmMessage);

            if (action === 'delete' && !confirm(confirmMessage)) {
                console.log('❌ User cancelled bulk delete operation');
                console.groupEnd();
                return;
            }

            if (action !== 'delete' && !confirm(confirmMessage)) {
                console.log('❌ User cancelled bulk operation');
                console.groupEnd();
                return;
            }

            console.log('✅ User confirmed bulk operation');

            // Create and submit form
            console.log('📝 Creating form for submission...');
            const form = document.createElement('form');
            form.method = 'POST';
            form.style.display = 'none';

            const actionInput = document.createElement('input');
            actionInput.type = 'hidden';
            actionInput.name = 'action';
            actionInput.value = actionName;
            form.appendChild(actionInput);
            console.log('➕ Added action input:', actionName);

            selectedValues.forEach((value, index) => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'selected_fields[]';
                input.value = value;
                form.appendChild(input);
                console.log(`➕ Added field input ${index + 1}:`, value);
            });

            document.body.appendChild(form);
            console.log('📤 Form created and appended to body');
            console.log('🚀 Submitting form...');
            console.groupEnd();

            form.submit();
        }

        // Enhanced confirmation for reset with logging
        function confirmReset() {
            console.group('🔄 Reset Configuration Action');
            console.log('⏰ Timestamp:', new Date().toLocaleString());
            console.log('⚠️ User attempting to reset configuration');

            const confirmed = confirm('⚠️ Are you sure you want to reset to default configuration? This will remove all custom field settings and cannot be undone.');

            if (confirmed) {
                console.log('✅ User confirmed reset operation');
                console.log('🗑️ All custom configurations will be deleted');
                console.log('🔄 System will revert to default field configuration');
            } else {
                console.log('❌ User cancelled reset operation');
            }

            console.groupEnd();
            return confirmed;
        }

        // Add specific logging for sync and reset buttons
        document.addEventListener('DOMContentLoaded', function() {
            // Sync button logging
            const syncButton = document.getElementById('syncButton');
            if (syncButton) {
                syncButton.addEventListener('click', function(e) {
                    console.group('🔄 Sync with Database Action');
                    console.log('⏰ Timestamp:', new Date().toLocaleString());
                    console.log('🎯 Action: Sync with Database');
                    console.log('📊 This will scan the database for new columns');
                    console.log('➕ New fields will be added to configuration');
                    console.log('🔄 Existing fields will remain unchanged');
                    console.log('📤 Form submission starting...');
                    console.groupEnd();
                });
            }

            // Reset button logging
            const resetButton = document.getElementById('resetButton');
            if (resetButton) {
                resetButton.addEventListener('click', function(e) {
                    console.group('⚠️ Reset Configuration Action');
                    console.log('⏰ Timestamp:', new Date().toLocaleString());
                    console.log('🎯 Action: Reset to Default Configuration');
                    console.log('🗑️ This will delete all custom field settings');
                    console.log('🔄 System will revert to default configuration');
                    console.log('⚠️ This action cannot be undone');
                    console.groupEnd();
                });
            }

            // Field Diagnostics button logging
            const diagnosticsButton = document.querySelector('a[href="field_diagnostics.php"]');
            if (diagnosticsButton) {
                diagnosticsButton.addEventListener('click', function(e) {
                    console.group('📊 Field Diagnostics Navigation');
                    console.log('⏰ Timestamp:', new Date().toLocaleString());
                    console.log('🎯 Action: Navigate to Field Diagnostics');
                    console.log('📊 This will show detailed field analysis');
                    console.log('🔍 Useful for troubleshooting field issues');
                    console.groupEnd();
                });
            }

            // Add New Field button logging
            const addFieldButton = document.querySelector('button[data-bs-target="#addFieldModal"]');
            if (addFieldButton) {
                addFieldButton.addEventListener('click', function(e) {
                    console.group('➕ Add New Field Action');
                    console.log('⏰ Timestamp:', new Date().toLocaleString());
                    console.log('🎯 Action: Open Add Field Modal');
                    console.log('📝 User can create custom fields');
                    console.log('⚙️ Modal will show field configuration options');
                    console.groupEnd();
                });
            }

            // Add New Section button logging
            const addSectionButton = document.querySelector('button[data-bs-target="#addSectionModal"]');
            if (addSectionButton) {
                addSectionButton.addEventListener('click', function(e) {
                    console.group('📁 Add New Section Action');
                    console.log('⏰ Timestamp:', new Date().toLocaleString());
                    console.log('🎯 Action: Open Add Section Modal');
                    console.log('📁 User can create new field sections');
                    console.log('⚙️ Modal will show section configuration options');
                    console.groupEnd();
                });
            }
        });

        // Auto-refresh page after successful operations
        <?php if (isset($result) && $result['success'] && in_array($_POST['action'] ?? '', ['add_field', 'delete_field', 'move_field', 'add_section', 'delete_section', 'bulk_delete_fields', 'bulk_enable_fields', 'bulk_disable_fields'])): ?>
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        <?php endif; ?>

        // Add some visual feedback for form interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Highlight sections when hovering
            document.querySelectorAll('.field-config-section').forEach(section => {
                section.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                    this.style.boxShadow = '0 4px 12px rgba(0,0,0,0.1)';
                });

                section.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = 'none';
                });
            });

            // Add confirmation for dangerous actions
            document.querySelectorAll('button[onclick*="deleteField"], button[onclick*="deleteSection"]').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    // The onclick will handle the modal, but we can add extra styling
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });

            // Add visual feedback for field selection
            document.querySelectorAll('.field-selector').forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const fieldItem = this.closest('.field-item');
                    if (this.checked) {
                        fieldItem.classList.add('selected');
                    } else {
                        fieldItem.classList.remove('selected');
                    }
                });
            });

            // Initialize bulk actions state
            updateBulkActions();

            // Keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                // Ctrl+A to select all
                if (e.ctrlKey && e.key === 'a') {
                    e.preventDefault();
                    document.getElementById('selectAllFields').checked = true;
                    toggleAllFields(true);
                }

                // Escape to clear selection
                if (e.key === 'Escape') {
                    clearSelection();
                }

                // Delete key to delete selected
                if (e.key === 'Delete' && document.querySelectorAll('.field-selector:checked').length > 0) {
                    e.preventDefault();
                    bulkAction('delete');
                }
            });
        });

        // Function to toggle dropdown options visibility
        function toggleDropdownOptions(selectElement, sectionKey, fieldKey) {
            const optionsSection = document.getElementById('dropdown_options_' + sectionKey + '_' + fieldKey);
            if (optionsSection) {
                if (selectElement.value === 'dropdown') {
                    optionsSection.style.display = 'block';
                    optionsSection.style.animation = 'slideDown 0.3s ease-out';
                } else {
                    optionsSection.style.display = 'none';
                }
            }
        }

        // Initialize dropdown options visibility on page load
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('select[name*="[type]"]').forEach(function(select) {
                const parts = select.name.match(/field_config\[([^\]]+)\]\[fields\]\[([^\]]+)\]\[type\]/);
                if (parts) {
                    toggleDropdownOptions(select, parts[1], parts[2]);
                }
            });
        });

        // Function to toggle dropdown options in Add Field modal
        function toggleAddFieldDropdownOptions(selectElement) {
            const optionsSection = document.getElementById('addFieldDropdownOptions');
            if (optionsSection) {
                if (selectElement.value === 'dropdown') {
                    optionsSection.style.display = 'block';
                    optionsSection.style.animation = 'slideDown 0.3s ease-out';
                } else {
                    optionsSection.style.display = 'none';
                }
            }
        }

        // Function to toggle between custom and database options
        function toggleDropdownSource(sectionKey, fieldKey, sourceType) {
            const customOptions = document.getElementById('custom_options_' + sectionKey + '_' + fieldKey);
            const databaseOptions = document.getElementById('database_options_' + sectionKey + '_' + fieldKey);

            if (customOptions && databaseOptions) {
                if (sourceType === 'custom') {
                    customOptions.style.display = 'block';
                    databaseOptions.style.display = 'none';
                } else if (sourceType === 'database') {
                    customOptions.style.display = 'none';
                    databaseOptions.style.display = 'block';
                }
            }
        }

        // Function to toggle dropdown source in Add Field modal
        function toggleAddFieldDropdownSource(sourceType) {
            const customOptions = document.getElementById('addFieldCustomOptions');
            const databaseOptions = document.getElementById('addFieldDatabaseOptions');

            if (customOptions && databaseOptions) {
                if (sourceType === 'custom') {
                    customOptions.style.display = 'block';
                    databaseOptions.style.display = 'none';
                } else if (sourceType === 'database') {
                    customOptions.style.display = 'none';
                    databaseOptions.style.display = 'block';
                }
            }
        }

        // Enhanced form submission with detailed console logging
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Field Settings Console Logger Initialized');
            console.log('📊 Page loaded at:', new Date().toLocaleString());
            console.log('👤 Current user access level:', '<?php echo $access; ?>');

            // Add loading state to all submit buttons with detailed logging
            const submitButtons = document.querySelectorAll('button[type="submit"], input[type="submit"]');
            console.log('🔘 Found', submitButtons.length, 'submit buttons');

            submitButtons.forEach((button, index) => {
                button.addEventListener('click', function(e) {
                    const form = this.closest('form');
                    if (form) {
                        const actionInput = form.querySelector('input[name="action"]');
                        const action = actionInput ? actionInput.value : 'unknown';

                        console.group('🔄 Button Click Event #' + (index + 1));
                        console.log('⏰ Timestamp:', new Date().toLocaleString());
                        console.log('🎯 Action:', action);
                        console.log('🔘 Button text:', this.textContent.trim());
                        console.log('📝 Form data:', new FormData(form));

                        // Log form data details
                        const formData = new FormData(form);
                        const formDataObj = {};
                        for (let [key, value] of formData.entries()) {
                            formDataObj[key] = value;
                        }
                        console.log('📋 Form data object:', formDataObj);

                        // Show confirmation for destructive actions
                        const destructiveActions = ['delete_field', 'delete_section', 'reset_config', 'bulk_delete_fields'];
                        if (destructiveActions.includes(action)) {
                            console.warn('⚠️ Destructive action detected:', action);
                            if (!confirm('⚠️ This action cannot be undone. Are you sure you want to continue?')) {
                                console.log('❌ User cancelled destructive action');
                                console.groupEnd();
                                e.preventDefault();
                                return false;
                            }
                            console.log('✅ User confirmed destructive action');
                        }

                        // Add loading state
                        const originalText = this.innerHTML;
                        this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Processing...';
                        this.disabled = true;
                        console.log('🔄 Button state changed to loading');

                        // Log submission
                        console.log('📤 Form submission started');
                        console.groupEnd();

                        // Re-enable button after 10 seconds (fallback)
                        setTimeout(() => {
                            this.innerHTML = originalText;
                            this.disabled = false;
                            console.log('🔄 Button state reset (timeout fallback)');
                        }, 10000);
                    }
                });
            });

            // Auto-save functionality for field configurations
            const fieldInputs = document.querySelectorAll('input[name^="fields["], select[name^="fields["], textarea[name^="fields["]');
            let saveTimeout;

            fieldInputs.forEach(input => {
                input.addEventListener('change', function() {
                    clearTimeout(saveTimeout);

                    // Show saving indicator
                    const indicator = document.createElement('span');
                    indicator.innerHTML = ' <i class="fas fa-spinner fa-spin text-primary"></i>';
                    indicator.className = 'save-indicator';

                    const existingIndicator = this.parentNode.querySelector('.save-indicator');
                    if (existingIndicator) {
                        existingIndicator.remove();
                    }

                    this.parentNode.appendChild(indicator);

                    // Auto-save after 2 seconds of no changes
                    saveTimeout = setTimeout(() => {
                        // Trigger form submission
                        const form = this.closest('form');
                        if (form) {
                            const saveButton = form.querySelector('button[name="action"][value="save_field_config"]');
                            if (saveButton) {
                                indicator.innerHTML = ' <i class="fas fa-check text-success"></i>';
                                setTimeout(() => indicator.remove(), 2000);

                                // Uncomment to enable auto-save
                                // saveButton.click();
                            }
                        }
                    }, 2000);
                });
            });

            // Debug mode toggle
            function toggleDebug() {
                const url = new URL(window.location);
                if (url.searchParams.has('debug')) {
                    url.searchParams.delete('debug');
                } else {
                    url.searchParams.set('debug', '1');
                }
                window.location.href = url.toString();
            }

            // Add debug toggle button if not in debug mode
            if (!window.location.search.includes('debug')) {
                const debugButton = document.createElement('button');
                debugButton.innerHTML = '🔍 Debug Mode';
                debugButton.className = 'btn btn-outline-secondary btn-sm';
                debugButton.style.position = 'fixed';
                debugButton.style.bottom = '20px';
                debugButton.style.right = '20px';
                debugButton.style.zIndex = '1000';
                debugButton.onclick = toggleDebug;
                document.body.appendChild(debugButton);
            }
        });
    </script>
</body>
</html>
