# ========================================
# حماية مجلد App/includes - إعادة توجيه إلى index.php
# ========================================

# منع Directory Browsing
Options -Indexes

# منع الوصول لجميع الملفات
Order Deny,Allow
Deny from all

# رسائل خطأ مخصصة - إعادة توجيه إلى الصفحة الرئيسية
ErrorDocument 403 /index.php
ErrorDocument 404 /index.php

# منع الوصول للملفات الحساسة
<Files ~ "\.(php|ini|conf|env|json|xml|yml|yaml)$">
    Order allow,deny
    Deny from all
</Files>

# Headers أمان إضافية
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>
