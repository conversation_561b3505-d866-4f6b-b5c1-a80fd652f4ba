<?php
// EmailInformation.php

// Load environment variables
require_once __DIR__ . '/env_loader.php';

// Try to load PHPMailer if available
$phpmailerConstants = [];
if (class_exists('<PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer')) {
    $phpmailerConstants['SMTPS'] = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_SMTPS;
    $phpmailerConstants['STARTTLS'] = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_STARTTLS;
} else {
    // Fallback constants if PHPMailer is not available
    $phpmailerConstants['SMTPS'] = 'smtps';
    $phpmailerConstants['STARTTLS'] = 'tls';
}

// Check if required environment variables are set
$requiredVars = ['MAIL_HOST', 'MAIL_USERNAME', 'MAIL_PASSWORD', 'MAIL_PORT', 'MAIL_RECIPIENTS'];
foreach ($requiredVars as $var) {
    if (!isset($_ENV[$var]) || empty($_ENV[$var])) {
        throw new Exception("Required environment variable '$var' is not set in .env file");
    }
}

return [
    'host' => $_ENV['MAIL_HOST'],
    'username' => $_ENV['MAIL_USERNAME'],
    'password' => $_ENV['MAIL_PASSWORD'],
    'encryption' => ($_ENV['MAIL_ENCRYPTION'] === 'smtps') ? $phpmailerConstants['SMTPS'] : $phpmailerConstants['STARTTLS'],
    'port' => (int)$_ENV['MAIL_PORT'],
    'charset' => $_ENV['MAIL_CHARSET'] ?? 'UTF-8',
    'from_email' => $_ENV['MAIL_USERNAME'], // استخدام نفس البريد كمرسل
    'from_name' => $_ENV['MAIL_FROM_NAME'] ?? 'Kalam CX System', // اسم المرسل
    'recipients' => [ // ✅ قائمة المستلمين
        $_ENV['MAIL_RECIPIENTS'],
    ],
];
