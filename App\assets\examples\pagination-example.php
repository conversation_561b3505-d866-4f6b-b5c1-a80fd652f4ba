<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unified Pagination Example</title>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    
    <!-- Bootstrap (Optional) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Unified Tables CSS -->
    <link rel="stylesheet" href="../css/root-variables.css">
    <link rel="stylesheet" href="../css/unified-tables.css">
    
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f8f9fa;
            padding: 20px;
        }
        .example-section {
            margin-bottom: 40px;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .example-title {
            color: #6c5ce7;
            margin-bottom: 20px;
            border-bottom: 2px solid #6c5ce7;
            padding-bottom: 10px;
        }
        .code-example {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #6c5ce7;
            margin: 15px 0;
        }
        .code-example pre {
            margin: 0;
            font-size: 13px;
        }
    </style>
</head>
<body>
    <?php
    // Include the unified pagination system
    include '../../includes/unified-pagination.php';
    
    // Simulate some data for examples
    $currentPage = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $totalRecords = 250;
    $recordsPerPage = 10;
    $totalPages = ceil($totalRecords / $recordsPerPage);
    $searchQuery = isset($_GET['search']) ? $_GET['search'] : '';
    ?>
    
    <div class="container-fluid">
        <h1 class="text-center mb-5">🔢 نظام Pagination الموحد - أمثلة تطبيقية</h1>
        
        <!-- Example 1: Basic Pagination -->
        <div class="example-section">
            <h2 class="example-title">1. Pagination أساسي</h2>
            
            <div class="code-example">
                <strong>PHP Code:</strong>
                <pre><code>&lt;?php
include 'includes/unified-pagination.php';

echo generateUnifiedPagination(
    $currentPage,    // Current page
    $totalPages,     // Total pages
    $totalRecords,   // Total records
    $recordsPerPage, // Records per page
    '',              // Base URL
    []               // Extra parameters
);
?&gt;</code></pre>
            </div>
            
            <div class="result">
                <strong>Result:</strong>
                <?php 
                echo generateUnifiedPagination(
                    $currentPage, 
                    $totalPages, 
                    $totalRecords, 
                    $recordsPerPage, 
                    '', 
                    []
                );
                ?>
            </div>
        </div>

        <!-- Example 2: Pagination with Search -->
        <div class="example-section">
            <h2 class="example-title">2. Pagination مع البحث</h2>
            
            <div class="code-example">
                <strong>PHP Code:</strong>
                <pre><code>&lt;?php
// Search box
echo generateUnifiedSearch($searchQuery, 'Search users...', '', []);

// Pagination with search parameter
$extraParams = [];
if (!empty($searchQuery)) {
    $extraParams['search'] = $searchQuery;
}

echo generateUnifiedPagination(
    $currentPage, 
    $totalPages, 
    $totalRecords, 
    $recordsPerPage, 
    '', 
    $extraParams
);
?&gt;</code></pre>
            </div>
            
            <div class="result">
                <strong>Result:</strong>
                <?php 
                echo generateUnifiedSearch($searchQuery, 'Search users...', '', []);
                
                $extraParams = [];
                if (!empty($searchQuery)) {
                    $extraParams['search'] = $searchQuery;
                }
                
                echo generateUnifiedPagination(
                    $currentPage, 
                    $totalPages, 
                    $totalRecords, 
                    $recordsPerPage, 
                    '', 
                    $extraParams
                );
                ?>
            </div>
        </div>

        <!-- Example 3: Complete Pagination System -->
        <div class="example-section">
            <h2 class="example-title">3. نظام Pagination كامل</h2>
            
            <div class="code-example">
                <strong>PHP Code:</strong>
                <pre><code>&lt;?php
$config = [
    'current_page' => $currentPage,
    'total_pages' => $totalPages,
    'total_records' => $totalRecords,
    'records_per_page' => $recordsPerPage,
    'base_url' => '',
    'extra_params' => [],
    'show_search' => true,
    'show_per_page' => true,
    'search_value' => $searchQuery,
    'search_placeholder' => 'Search records...',
    'per_page_options' => [5, 10, 25, 50, 100]
];

echo generateCompletePagination($config);
?&gt;</code></pre>
            </div>
            
            <div class="result">
                <strong>Result:</strong>
                <?php 
                $config = [
                    'current_page' => $currentPage,
                    'total_pages' => $totalPages,
                    'total_records' => $totalRecords,
                    'records_per_page' => $recordsPerPage,
                    'base_url' => '',
                    'extra_params' => [],
                    'show_search' => true,
                    'show_per_page' => true,
                    'search_value' => $searchQuery,
                    'search_placeholder' => 'Search records...',
                    'per_page_options' => [5, 10, 25, 50, 100]
                ];
                
                echo generateCompletePagination($config);
                ?>
            </div>
        </div>

        <!-- Example 4: Simple Pagination -->
        <div class="example-section">
            <h2 class="example-title">4. Pagination بسيط</h2>
            
            <div class="code-example">
                <strong>PHP Code:</strong>
                <pre><code>&lt;?php
echo simplePagination($currentPage, $totalPages, '', []);
?&gt;</code></pre>
            </div>
            
            <div class="result">
                <strong>Result:</strong>
                <?php 
                echo simplePagination($currentPage, $totalPages, '', []);
                ?>
            </div>
        </div>

        <!-- Example 5: Results Per Page Selector -->
        <div class="example-section">
            <h2 class="example-title">5. محدد عدد النتائج لكل صفحة</h2>
            
            <div class="code-example">
                <strong>PHP Code:</strong>
                <pre><code>&lt;?php
echo generateResultsPerPage(
    $recordsPerPage,           // Current per page
    [5, 10, 25, 50, 100],     // Options
    '',                        // Base URL
    []                         // Extra parameters
);
?&gt;</code></pre>
            </div>
            
            <div class="result">
                <strong>Result:</strong>
                <?php 
                echo generateResultsPerPage(
                    $recordsPerPage,
                    [5, 10, 25, 50, 100],
                    '',
                    []
                );
                ?>
            </div>
        </div>

        <!-- Example 6: Pagination Data Calculator -->
        <div class="example-section">
            <h2 class="example-title">6. حاسبة بيانات Pagination</h2>
            
            <div class="code-example">
                <strong>PHP Code:</strong>
                <pre><code>&lt;?php
$paginationData = calculatePaginationData($totalRecords, $recordsPerPage, $currentPage);
print_r($paginationData);
?&gt;</code></pre>
            </div>
            
            <div class="result">
                <strong>Result:</strong>
                <?php 
                $paginationData = calculatePaginationData($totalRecords, $recordsPerPage, $currentPage);
                echo '<pre>';
                print_r($paginationData);
                echo '</pre>';
                ?>
            </div>
        </div>

        <!-- Example 7: Real Table with Pagination -->
        <div class="example-section">
            <h2 class="example-title">7. جدول حقيقي مع Pagination</h2>
            
            <div class="unified-table-container">
                <!-- Search -->
                <?php echo generateUnifiedSearch($searchQuery, 'Search employees...', '', []); ?>
                
                <!-- Table -->
                <div class="unified-table-responsive">
                    <table class="unified-table">
                        <thead>
                            <tr>
                                <th><i class="fas fa-hashtag"></i>ID</th>
                                <th><i class="fas fa-user"></i>Name</th>
                                <th><i class="fas fa-envelope"></i>Email</th>
                                <th><i class="fas fa-building"></i>Department</th>
                                <th><i class="fas fa-check-circle"></i>Status</th>
                                <th><i class="fas fa-cogs"></i>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php 
                            // Simulate table data
                            $startRecord = (($currentPage - 1) * $recordsPerPage) + 1;
                            $endRecord = min($currentPage * $recordsPerPage, $totalRecords);
                            
                            for ($i = $startRecord; $i <= $endRecord; $i++): 
                            ?>
                            <tr>
                                <td><span class="employee-code"><?php echo str_pad($i, 3, '0', STR_PAD_LEFT); ?></span></td>
                                <td>Employee <?php echo $i; ?></td>
                                <td>employee<?php echo $i; ?>@company.com</td>
                                <td><?php echo ['IT', 'HR', 'Finance', 'Marketing'][($i - 1) % 4]; ?></td>
                                <td>
                                    <span class="status-badge status-<?php echo $i % 2 == 0 ? 'active' : 'inactive'; ?>">
                                        <?php echo $i % 2 == 0 ? 'Active' : 'Inactive'; ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="table-actions">
                                        <a href="#" class="btn-table-action btn-edit" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="#" class="btn-table-action btn-view" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <?php endfor; ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <?php 
                $extraParams = [];
                if (!empty($searchQuery)) {
                    $extraParams['search'] = $searchQuery;
                }
                
                echo generateUnifiedPagination(
                    $currentPage, 
                    $totalPages, 
                    $totalRecords, 
                    $recordsPerPage, 
                    '', 
                    $extraParams
                );
                ?>
            </div>
        </div>

        <!-- Usage Instructions -->
        <div class="example-section">
            <h2 class="example-title">📋 تعليمات الاستخدام</h2>
            
            <h4>1. تضمين الملف:</h4>
            <div class="code-example">
                <pre><code>&lt;?php include 'includes/unified-pagination.php'; ?&gt;</code></pre>
            </div>
            
            <h4>2. تضمين CSS:</h4>
            <div class="code-example">
                <pre><code>&lt;link rel="stylesheet" href="assets/css/unified-tables.css"&gt;</code></pre>
            </div>
            
            <h4>3. الاستخدام الأساسي:</h4>
            <div class="code-example">
                <pre><code>&lt;?php
// حساب بيانات الصفحات
$totalRecords = 100;
$recordsPerPage = 10;
$currentPage = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$totalPages = ceil($totalRecords / $recordsPerPage);

// عرض Pagination
echo generateUnifiedPagination(
    $currentPage, 
    $totalPages, 
    $totalRecords, 
    $recordsPerPage
);
?&gt;</code></pre>
            </div>
            
            <h4>4. مع البحث والفلاتر:</h4>
            <div class="code-example">
                <pre><code>&lt;?php
$searchQuery = $_GET['search'] ?? '';
$extraParams = [];
if (!empty($searchQuery)) {
    $extraParams['search'] = $searchQuery;
}

echo generateUnifiedPagination(
    $currentPage, 
    $totalPages, 
    $totalRecords, 
    $recordsPerPage, 
    '', 
    $extraParams
);
?&gt;</code></pre>
            </div>
        </div>

        <!-- Features -->
        <div class="example-section">
            <h2 class="example-title">✨ المميزات</h2>
            
            <div class="row">
                <div class="col-md-6">
                    <h5>🎨 التصميم:</h5>
                    <ul>
                        <li>تصميم موحد ومتسق</li>
                        <li>متجاوب مع جميع الأجهزة</li>
                        <li>أيقونات Font Awesome</li>
                        <li>تأثيرات بصرية جذابة</li>
                    </ul>
                    
                    <h5>🔧 الوظائف:</h5>
                    <ul>
                        <li>Pagination ذكي مع نقاط</li>
                        <li>دعم البحث والفلاتر</li>
                        <li>محدد عدد النتائج</li>
                        <li>معلومات الصفحات</li>
                    </ul>
                </div>
                
                <div class="col-md-6">
                    <h5>⚡ الأداء:</h5>
                    <ul>
                        <li>كود محسن وسريع</li>
                        <li>استهلاك ذاكرة قليل</li>
                        <li>تحميل سريع</li>
                        <li>SEO friendly URLs</li>
                    </ul>
                    
                    <h5>🛠️ سهولة الاستخدام:</h5>
                    <ul>
                        <li>API بسيط وواضح</li>
                        <li>تخصيص سهل</li>
                        <li>توثيق شامل</li>
                        <li>أمثلة متعددة</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS (Optional) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
