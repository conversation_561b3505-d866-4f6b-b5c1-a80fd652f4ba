/* Bar Chart Styles */
:root {
    /* Bar Colors */
    --shift-color: rgb(2, 0, 144);
    --shift-hover: rgb(3, 0, 180);
    --break1-color: #ff9800;
    --break1-hover: #ffa726;
    --break2-color: #4caf50;
    --break2-hover: #66bb6a;
    --lunch-color: #f44336;
    --lunch-hover: #ef5350;
    --activity-color: #9c27b0;
    --activity-hover: #ab47bc;
    --off-color: #757575;
    --off-hover: #9e9e9e;
    --vacation-color: #607d8b;
    --vacation-hover: #78909c;
}

/* Hours Bar Container */
.hours-container {
    position: relative;
    height: 30px;
    width: 100%;
    display: flex;
    font-size: 12px;
    color: var(--text-medium);
    background: var(--white);
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    margin-bottom: var(--spacing-sm);
}

.hour-block {
    flex: 1;
    text-align: center;
    border-left: 1px solid var(--border-color);
    padding: 5px 0;
    background: var(--white);
    transition: var(--transition);
    position: relative;
}

.hour-block:hover {
    background: var(--primary-light);
    color: var(--accent-color);
    z-index: 1;
}

/* Shift Bar */
.shift-bar {
    position: absolute;
    height: 20px;
    background-color: var(--shift-color);
    opacity: 0.8;
    border-radius: var(--border-radius-sm);
    transition: var(--transition);
    z-index: 2;
}

.shift-bar:hover {
    opacity: 1;
    background-color: var(--shift-hover);
    transform: scale(1.02);
    box-shadow: var(--shadow);
    z-index: 3;
}

/* Break Bars */
.break-bar {
    position: absolute;
    height: 15px;
    border-radius: var(--border-radius-sm);
    transition: var(--transition);
    z-index: 2;
}

.break-bar.b1 {
    background-color: var(--break1-color);
}

.break-bar.b1:hover {
    background-color: var(--break1-hover);
    transform: scale(1.02);
    box-shadow: var(--shadow);
    z-index: 3;
}

.break-bar.b2 {
    background-color: var(--break2-color);
}

.break-bar.b2:hover {
    background-color: var(--break2-hover);
    transform: scale(1.02);
    box-shadow: var(--shadow);
    z-index: 3;
}

.break-bar.lunch {
    background-color: var(--lunch-color);
}

.break-bar.lunch:hover {
    background-color: var(--lunch-hover);
    transform: scale(1.02);
    box-shadow: var(--shadow);
    z-index: 3;
}

/* Activity Bar */
.activity-bar {
    position: absolute;
    height: 15px;
    background-color: var(--activity-color);
    border-radius: var(--border-radius-sm);
    transition: var(--transition);
    z-index: 2;
}

.activity-bar:hover {
    background-color: var(--activity-hover);
    transform: scale(1.02);
    box-shadow: var(--shadow);
    z-index: 3;
}

/* Status Bars */
.status-bar {
    position: absolute;
    height: 20px;
    border-radius: var(--border-radius-sm);
    transition: var(--transition);
    z-index: 2;
}

.status-bar.off {
    background-color: var(--off-color);
}

.status-bar.off:hover {
    background-color: var(--off-hover);
    transform: scale(1.02);
    box-shadow: var(--shadow);
    z-index: 3;
}

.status-bar.vacation {
    background-color: var(--vacation-color);
}

.status-bar.vacation:hover {
    background-color: var(--vacation-hover);
    transform: scale(1.02);
    box-shadow: var(--shadow);
    z-index: 3;
}

/* Bar Tooltips */
[data-tooltip] {
    position: relative;
}

[data-tooltip]:before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    padding: 5px 10px;
    background: var(--dark-color);
    color: var(--white);
    font-size: 12px;
    border-radius: var(--border-radius-sm);
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    z-index: 10;
}

[data-tooltip]:hover:before {
    opacity: 1;
    visibility: visible;
    bottom: calc(100% + 5px);
}

/* Bar Labels */
.bar-label {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--white);
    font-size: 10px;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    pointer-events: none;
    z-index: 4;
}

/* Bar Container */
.bar-container {
    position: relative;
    height: 100px;
    width: 100%;
    margin: var(--spacing-sm) 0;
    padding: var(--spacing-xs);
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
}

/* Bar Grid */
.bar-grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-columns: repeat(24, 1fr);
    pointer-events: none;
}

.grid-line {
    border-left: 1px dashed var(--border-color);
    height: 100%;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .hours-container {
        height: 25px;
        font-size: 10px;
    }

    .bar-container {
        height: 80px;
    }

    .bar-label {
        font-size: 8px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .hours-container,
    .bar-container {
        background: var(--white);
    }

    .hour-block {
        background: var(--white);
        border-color: var(--border-color);
    }

    .hour-block:hover {
        background: var(--primary-dark);
        color: var(--white);
    }

    [data-tooltip]:before {
        background: var(--dark-color);
        color: var(--white);
    }
} 