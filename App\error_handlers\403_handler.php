<?php
/**
 * Custom 403 Forbidden Error Handler
 * معالج مخصص لأخطاء 403 Forbidden
 * 
 * يقوم بإعادة توجيه المستخدمين إلى الصفحة الرئيسية بدلاً من عرض رسالة خطأ Apache
 */

// Start secure session if not already started
if (session_status() === PHP_SESSION_NONE) {
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_secure', 1);
    ini_set('session.use_only_cookies', 1);
    ini_set('session.cookie_samesite', 'Strict');
    session_start();
}

// Include database connection and functions
include_once $_SERVER['DOCUMENT_ROOT'].'/config/Databasehost.php';

/**
 * Log security events for 403 errors
 */
function log403Event($details = []) {
    global $conn;
    try {
        // Check if database connection exists
        if (!isset($conn) || $conn === null) {
            error_log("Database connection not available for 403 logging");
            return;
        }

        // Check if security_logs table exists
        $check_table = $conn->query("SHOW TABLES LIKE 'security_logs'");
        if ($check_table->num_rows == 0) {
            // Create table if it doesn't exist
            $create_table = "CREATE TABLE IF NOT EXISTS security_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                event_type VARCHAR(100) NOT NULL,
                user_email VARCHAR(255),
                ip_address VARCHAR(45),
                event_data TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )";
            $conn->query($create_table);
        }

        $sql = "INSERT INTO security_logs (event_type, user_email, ip_address, event_data, created_at)
                VALUES (?, ?, ?, ?, NOW())";
        $stmt = $conn->prepare($sql);
        if ($stmt) {
            $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
            $user = $_SESSION['username'] ?? 'anonymous';
            $details_json = json_encode($details);
            $event_type = '403_forbidden';
            $stmt->bind_param("ssss", $event_type, $user, $ip, $details_json);
            $stmt->execute();
            $stmt->close();
        }
    } catch (Exception $e) {
        error_log("403 logging failed: " . $e->getMessage());
    }
}

/**
 * Determine the best redirect destination based on user session
 */
function getRedirectDestination() {
    // Check if user is logged in
    if (isset($_SESSION['username']) && isset($_SESSION['access_level'])) {
        // User is logged in, redirect to appropriate dashboard
        $access_level = $_SESSION['access_level'];
        
        switch ($access_level) {
            case 'Super Admin':
            case 'Admin':
            case 'Editor':
            case 'Manager':
            case 'Leader':
            case 'Viewer':
                return '/App/Home';
            case 'Member':
                return '/MemberHome.php';
            default:
                return '/App/Home';
        }
    } else {
        // User is not logged in, redirect to login page
        return '/Loginpage.php?error=access_denied';
    }
}

/**
 * Get user-friendly error message based on context
 */
function getErrorMessage() {
    if (isset($_SESSION['username'])) {
        return "You don't have permission to access this resource. Redirecting to your dashboard...";
    } else {
        return "Access denied. Please log in to continue.";
    }
}

// Log the 403 event with detailed information
$log_details = [
    'requested_url' => $_SERVER['REQUEST_URI'] ?? 'unknown',
    'referer' => $_SERVER['HTTP_REFERER'] ?? 'direct',
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
    'method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown',
    'timestamp' => date('Y-m-d H:i:s'),
    'session_exists' => isset($_SESSION['username']) ? 'yes' : 'no',
    'access_level' => $_SESSION['access_level'] ?? 'none'
];

log403Event($log_details);

// Get redirect destination and error message
$redirect_url = getRedirectDestination();
$error_message = getErrorMessage();

// Set appropriate HTTP status code
http_response_code(403);

// Check if this is an AJAX request
$is_ajax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';

if ($is_ajax) {
    // Handle AJAX requests with JSON response
    header('Content-Type: application/json');
    echo json_encode([
        'error' => true,
        'message' => $error_message,
        'redirect' => $redirect_url,
        'status' => 403
    ]);
    exit();
}

// For regular requests, redirect immediately without showing error page
header("Location: $redirect_url");
exit();
?>
