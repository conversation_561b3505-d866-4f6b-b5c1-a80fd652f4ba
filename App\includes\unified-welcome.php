<?php
// Unified Welcome Section for all Admin Pages
// يجب أن تكون المتغيرات التالية متوفرة قبل تضمين هذا الملف:
// $imageData - مسار صورة المستخدم
// $firstName - الاسم الأول
// $lastName - الاسم الأخير

// التأكد من وجود المتغيرات المطلوبة
if (!isset($firstName) || !isset($lastName)) {
    // استخراج الاسم من البريد الإلكتروني إذا لم يكن متوفراً
    if (isset($_SESSION['username'])) {
        $email = $_SESSION['username'];
        $beforeAt = explode('@', $email)[0];
        if (strpos($beforeAt, '.') !== false) {
            $nameParts = explode('.', $beforeAt);
            $firstName = ucfirst($nameParts[0]);
            $lastName = ucfirst($nameParts[1]);
        } else {
            $firstName = ucfirst($beforeAt);
            $lastName = '';
        }
    } else {
        $firstName = 'User';
        $lastName = '';
    }
}

// التأكد من وجود متغير الصورة
if (!isset($imageData)) {
    $imageData = '';
}
?>

<div class="welcome-section">
    <?php if (!empty($imageData)): ?>
        <img 
            src="<?php echo htmlspecialchars($imageData); ?>" 
            class="profile-pic" 
            alt="Profile Picture"
            onerror="this.style.display='none'; this.insertAdjacentHTML('afterend', '<div class=\'profile-pic default-avatar\'><i class=\'fas fa-user\'></i></div>');">
    <?php else: ?>
        <div class="profile-pic default-avatar">
            <i class="fas fa-user"></i>
        </div>
    <?php endif; ?>
    
    <div class="welcome-message">
        <h1>Welcome, <?php echo htmlspecialchars(trim($firstName . ' ' . $lastName)); ?></h1>
        <p>You have successfully logged in. This is your admin dashboard.</p>
    </div>
</div>
