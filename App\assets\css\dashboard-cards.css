@import url('root-variables.css');

/* ==================== DASHBOARD CARDS STYLES ==================== */
/* Beautiful cards with #c89cc4 color scheme */

/* Stats Container */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
    margin-bottom: 48px;
    padding: 0;
}

/* Individual Stat Card */
.stat-card {
    background: #ffffff;
    border: 1px solid #ede4f1;
    border-radius: 12px;
    padding: 32px;
    text-align: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    box-shadow: 0 1px 2px 0 rgba(200, 156, 196, 0.08);
}

/* Card Hover Effects */
.stat-card:hover {
    background: var(--card-hover-bg);
    border-color: var(--card-border-hover);
    box-shadow: var(--card-hover-shadow);
    transform: var(--card-hover-transform);
}

/* Card Headers */
.stat-card h3 {
    color: var(--card-title);
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin: 0 0 var(--spacing-md) 0;
    opacity: 0.8;
}

/* Card Values */
.stat-card p {
    color: var(--card-value);
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0;
    line-height: 1;
}

/* Specific Card Color Themes */
.stat-card:nth-child(1) {
    border-left: 4px solid var(--stats-schedule-color);
}

.stat-card:nth-child(1) p {
    color: var(--stats-schedule-color);
}

.stat-card:nth-child(2) {
    border-left: 4px solid var(--stats-absent-color);
}

.stat-card:nth-child(2) p {
    color: var(--stats-absent-color);
}

.stat-card:nth-child(3) {
    border-left: 4px solid var(--stats-vacation-color);
}

.stat-card:nth-child(3) p {
    color: var(--stats-vacation-color);
}

.stat-card:nth-child(4) {
    border-left: 4px solid var(--stats-online-color);
}

.stat-card:nth-child(4) p {
    color: var(--stats-online-color);
}

/* Card Icons (if needed) */
.stat-card::before {
    content: '';
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    opacity: 0.1;
    transition: var(--transition-normal);
}

.stat-card:nth-child(1)::before {
    background: var(--stats-schedule-color);
}

.stat-card:nth-child(2)::before {
    background: var(--stats-absent-color);
}

.stat-card:nth-child(3)::before {
    background: var(--stats-vacation-color);
}

.stat-card:nth-child(4)::before {
    background: var(--stats-online-color);
}

.stat-card:hover::before {
    opacity: 0.2;
    transform: scale(1.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .stats-container {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--spacing-md);
        margin-bottom: var(--spacing-xl);
    }
    
    .stat-card {
        padding: var(--spacing-lg);
    }
    
    .stat-card p {
        font-size: 2rem;
    }
    
    .stat-card h3 {
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .stats-container {
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-sm);
    }
    
    .stat-card {
        padding: var(--spacing-md);
    }
    
    .stat-card p {
        font-size: 1.75rem;
    }
    
    .stat-card h3 {
        font-size: 0.75rem;
        margin-bottom: var(--spacing-sm);
    }
}

/* Loading State */
.stat-card.loading {
    opacity: 0.6;
    pointer-events: none;
}

.stat-card.loading p {
    background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: var(--radius-sm);
    color: transparent;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .stat-card {
        background: var(--gray-800);
        border-color: var(--gray-700);
        color: var(--text-white);
    }
    
    .stat-card:hover {
        background: var(--gray-700);
        border-color: var(--gray-600);
    }
    
    .stat-card h3 {
        color: var(--gray-300);
    }
}

/* Animation for card entrance */
.stat-card {
    animation: fadeInUp 0.6s ease-out;
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
