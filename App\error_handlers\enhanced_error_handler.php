<?php
/**
 * Enhanced Error Handler - Security Optimized
 * معالج الأخطاء المحسن - محسن للأمان
 * 
 * @version 2.0
 * <AUTHOR> CX Security Team
 */

// تضمين ملفات النظام الأمني
require_once __DIR__ . '/error_config.php';
require_once __DIR__ . '/secure_error_handler.php';

// Start secure session if not already started
if (session_status() === PHP_SESSION_NONE) {
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_secure', ErrorConfig::isProduction() ? 1 : 0);
    ini_set('session.use_only_cookies', 1);
    ini_set('session.cookie_samesite', ErrorConfig::isProduction() ? 'Strict' : 'Lax');
    ini_set('session.gc_maxlifetime', 3600);
    ini_set('session.use_strict_mode', 1);
    session_start();
}

// Include database connection
include_once $_SERVER['DOCUMENT_ROOT'].'/config/Databasehost.php';

/**
 * Get error code from query parameter or server variable - Enhanced
 */
function getSecureErrorCode() {
    // التحقق من معامل الاستعلام أولاً
    if (isset($_GET['error']) && is_numeric($_GET['error'])) {
        $error_code = (int)$_GET['error'];
        // التحقق من أن رمز الخطأ صحيح
        if ($error_code >= 400 && $error_code <= 599) {
            return $error_code;
        }
    }
    
    // التحقق من متغير الخادم
    if (isset($_SERVER['REDIRECT_STATUS']) && is_numeric($_SERVER['REDIRECT_STATUS'])) {
        $error_code = (int)$_SERVER['REDIRECT_STATUS'];
        if ($error_code >= 400 && $error_code <= 599) {
            return $error_code;
        }
    }
    
    // التحقق من HTTP response code
    $response_code = http_response_code();
    if ($response_code >= 400 && $response_code <= 599) {
        return $response_code;
    }
    
    // افتراضي إلى 403 إذا لم يتم العثور على رمز خطأ محدد
    return 403;
}

/**
 * Get enhanced error details based on error code
 */
function getEnhancedErrorDetails($error_code) {
    $errors = [
        400 => [
            'title' => 'طلب غير صحيح',
            'message' => 'لا يمكن للخادم فهم الطلب المرسل.',
            'icon' => 'fas fa-exclamation-triangle',
            'color' => 'warning',
            'severity' => ErrorConfig::SEVERITY_WARNING,
            'user_action' => 'يرجى التحقق من البيانات المدخلة والمحاولة مرة أخرى.'
        ],
        401 => [
            'title' => 'غير مصرح',
            'message' => 'يجب تسجيل الدخول للوصول إلى هذه الصفحة.',
            'icon' => 'fas fa-lock',
            'color' => 'danger',
            'severity' => ErrorConfig::SEVERITY_ERROR,
            'user_action' => 'يرجى تسجيل الدخول أولاً.'
        ],
        403 => [
            'title' => 'وصول محظور',
            'message' => 'ليس لديك صلاحية للوصول إلى هذه الصفحة.',
            'icon' => 'fas fa-ban',
            'color' => 'danger',
            'severity' => ErrorConfig::SEVERITY_ERROR,
            'user_action' => 'يرجى التواصل مع المسؤول إذا كنت تعتقد أن هذا خطأ.'
        ],
        404 => [
            'title' => 'الصفحة غير موجودة',
            'message' => 'الصفحة التي تبحث عنها غير موجودة.',
            'icon' => 'fas fa-search',
            'color' => 'info',
            'severity' => ErrorConfig::SEVERITY_NOTICE,
            'user_action' => 'يرجى التحقق من الرابط أو العودة للصفحة الرئيسية.'
        ],
        405 => [
            'title' => 'طريقة غير مسموحة',
            'message' => 'طريقة الطلب المستخدمة غير مسموحة لهذه الصفحة.',
            'icon' => 'fas fa-times-circle',
            'color' => 'warning',
            'severity' => ErrorConfig::SEVERITY_WARNING,
            'user_action' => 'يرجى استخدام طريقة طلب صحيحة.'
        ],
        429 => [
            'title' => 'طلبات كثيرة جداً',
            'message' => 'تم إرسال طلبات كثيرة جداً في وقت قصير.',
            'icon' => 'fas fa-clock',
            'color' => 'warning',
            'severity' => ErrorConfig::SEVERITY_WARNING,
            'user_action' => 'يرجى الانتظار قليلاً قبل المحاولة مرة أخرى.'
        ],
        500 => [
            'title' => 'خطأ في الخادم',
            'message' => 'حدث خطأ داخلي في الخادم.',
            'icon' => 'fas fa-server',
            'color' => 'danger',
            'severity' => ErrorConfig::SEVERITY_CRITICAL,
            'user_action' => 'يرجى المحاولة مرة أخرى لاحقاً أو التواصل مع الدعم الفني.'
        ],
        502 => [
            'title' => 'بوابة سيئة',
            'message' => 'الخادم تلقى استجابة غير صحيحة من خادم آخر.',
            'icon' => 'fas fa-network-wired',
            'color' => 'danger',
            'severity' => ErrorConfig::SEVERITY_ERROR,
            'user_action' => 'يرجى المحاولة مرة أخرى لاحقاً.'
        ],
        503 => [
            'title' => 'الخدمة غير متاحة',
            'message' => 'الخادم غير متاح حالياً بسبب الصيانة أو الحمولة الزائدة.',
            'icon' => 'fas fa-tools',
            'color' => 'warning',
            'severity' => ErrorConfig::SEVERITY_WARNING,
            'user_action' => 'يرجى المحاولة مرة أخرى لاحقاً.'
        ],
        504 => [
            'title' => 'انتهت مهلة البوابة',
            'message' => 'انتهت مهلة انتظار الاستجابة من الخادم.',
            'icon' => 'fas fa-hourglass-end',
            'color' => 'warning',
            'severity' => ErrorConfig::SEVERITY_WARNING,
            'user_action' => 'يرجى المحاولة مرة أخرى.'
        ]
    ];
    
    return $errors[$error_code] ?? [
        'title' => 'خطأ غير معروف',
        'message' => 'حدث خطأ غير متوقع.',
        'icon' => 'fas fa-question-circle',
        'color' => 'secondary',
        'severity' => ErrorConfig::SEVERITY_ERROR,
        'user_action' => 'يرجى المحاولة مرة أخرى أو التواصل مع الدعم الفني.'
    ];
}

/**
 * Get secure redirect destination based on error code and user session
 */
function getSecureRedirectDestination($error_code) {
    // التحقق من وجود جلسة مستخدم
    $has_session = isset($_SESSION['username']) && isset($_SESSION['access_level']);
    $access_level = $_SESSION['access_level'] ?? 'none';
    
    switch ($error_code) {
        case 401: // Unauthorized
            return '/Loginpage.php?error=unauthorized';
            
        case 403: // Forbidden
            if ($has_session) {
                // إعادة التوجيه حسب مستوى الوصول
                switch ($access_level) {
                    case 'Member':
                        return '/Memberpages/MemberHome.php';
                    case 'Admin':
                    case 'Super Admin':
                    case 'Manager':
                    case 'Leader':
                    case 'Editor':
                    case 'Viewer':
                        return '/App/Home.php';
                    default:
                        return '/Loginpage.php?error=invalid_access';
                }
            } else {
                return '/Loginpage.php?error=access_denied';
            }
            
        case 404: // Not Found
        case 405: // Method Not Allowed
            if ($has_session) {
                switch ($access_level) {
                    case 'Member':
                        return '/Memberpages/MemberHome.php';
                    default:
                        return '/App/Home.php';
                }
            } else {
                return '/Loginpage.php';
            }
            
        case 429: // Too Many Requests
            return '/Loginpage.php?error=rate_limit';
            
        case 500: // Internal Server Error
        case 502: // Bad Gateway
        case 503: // Service Unavailable
        case 504: // Gateway Timeout
        default:
            if ($has_session) {
                switch ($access_level) {
                    case 'Member':
                        return '/Memberpages/MemberHome.php?error=system_error';
                    default:
                        return '/App/Home.php?error=system_error';
                }
            } else {
                return '/Loginpage.php?error=system_error';
            }
    }
}

/**
 * Enhanced error logging with security context
 */
function logEnhancedErrorEvent($error_code, $details = []) {
    try {
        // استخدام النظام الآمن للتسجيل
        $context = [
            'error_code' => $error_code,
            'requested_url' => $_SERVER['REQUEST_URI'] ?? 'unknown',
            'referer' => $_SERVER['HTTP_REFERER'] ?? 'direct',
            'method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown',
            'timestamp' => date('Y-m-d H:i:s'),
            'session_exists' => isset($_SESSION['username']) ? 'yes' : 'no',
            'access_level' => $_SESSION['access_level'] ?? 'none',
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => substr($_SERVER['HTTP_USER_AGENT'] ?? 'unknown', 0, 200)
        ];
        
        // دمج التفاصيل الإضافية
        $context = array_merge($context, $details);
        
        // تحديد مستوى الخطورة
        $error_details = getEnhancedErrorDetails($error_code);
        $severity = $error_details['severity'];
        
        // تسجيل في الملفات
        secureErrorLog("HTTP Error {$error_code}: {$error_details['title']}", $severity, $context);
        
        // تسجيل في قاعدة البيانات للأخطاء الخطيرة
        if (in_array($severity, [ErrorConfig::SEVERITY_CRITICAL, ErrorConfig::SEVERITY_ERROR])) {
            logErrorToDatabase("HTTP Error {$error_code}", $error_details['message'], __FILE__, __LINE__, $severity);
        }
        
        return true;
        
    } catch (Exception $e) {
        // في حالة فشل التسجيل، استخدم error_log العادي
        error_log("Enhanced error logging failed: " . $e->getMessage());
        error_log("Original error: HTTP {$error_code}");
        return false;
    }
}

/**
 * Check if request is AJAX
 */
function isAjaxRequest() {
    return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';
}

/**
 * Send JSON error response for AJAX requests
 */
function sendJsonErrorResponse($error_code, $error_details, $redirect_url) {
    header('Content-Type: application/json');
    
    $response = [
        'error' => true,
        'code' => $error_code,
        'title' => $error_details['title'],
        'message' => $error_details['message'],
        'severity' => $error_details['severity'],
        'user_action' => $error_details['user_action'],
        'redirect' => $redirect_url,
        'timestamp' => date('c')
    ];
    
    // في الإنتاج، لا نكشف تفاصيل إضافية
    if (!ErrorConfig::isProduction()) {
        $response['debug'] = [
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown',
            'method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown',
            'referer' => $_SERVER['HTTP_REFERER'] ?? 'none'
        ];
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit();
}

// الحصول على معلومات الخطأ
$error_code = getSecureErrorCode();
$error_details = getEnhancedErrorDetails($error_code);
$redirect_url = getSecureRedirectDestination($error_code);

// تسجيل حدث الخطأ
logEnhancedErrorEvent($error_code, [
    'redirect_destination' => $redirect_url,
    'error_title' => $error_details['title']
]);

// تعيين رمز حالة HTTP المناسب
http_response_code($error_code);

// إضافة headers الأمان
header("X-Frame-Options: DENY");
header("X-XSS-Protection: 1; mode=block");
header("X-Content-Type-Options: nosniff");
header("Referrer-Policy: strict-origin-when-cross-origin");

// التحقق من كون الطلب AJAX
if (isAjaxRequest()) {
    sendJsonErrorResponse($error_code, $error_details, $redirect_url);
}

// للأخطاء الأمنية (403)، إعادة توجيه فورية
if ($error_code == 403) {
    header("Location: $redirect_url");
    exit();
}

// عرض صفحة الخطأ المحسنة
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($error_details['title']); ?> - Kalam CX</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .error-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .error-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            text-align: center;
            max-width: 600px;
            width: 100%;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .error-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .error-title {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .error-message {
            font-size: 1.2rem;
            margin-bottom: 20px;
            color: #6c757d;
        }

        .user-action {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }

        .countdown-container {
            margin: 30px 0;
            padding: 20px;
            background: rgba(0, 123, 255, 0.1);
            border-radius: 10px;
            border: 1px solid rgba(0, 123, 255, 0.2);
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .btn-custom {
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: bold;
            text-decoration: none;
            transition: all 0.3s ease;
            margin: 5px;
            display: inline-block;
        }

        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .error-details {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            text-align: right;
        }

        .security-notice {
            background: rgba(220, 53, 69, 0.1);
            border: 1px solid rgba(220, 53, 69, 0.2);
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
        }

        @media (max-width: 768px) {
            .error-card {
                padding: 20px;
                margin: 10px;
            }

            .error-title {
                font-size: 2rem;
            }

            .error-icon {
                font-size: 3rem;
            }
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-card">
            <!-- أيقونة الخطأ -->
            <div class="error-icon text-<?php echo $error_details['color']; ?>">
                <i class="<?php echo $error_details['icon']; ?>"></i>
            </div>

            <!-- عنوان الخطأ -->
            <h1 class="error-title text-<?php echo $error_details['color']; ?>">
                <?php echo htmlspecialchars($error_details['title']); ?>
            </h1>

            <!-- رسالة الخطأ -->
            <p class="error-message">
                <?php echo htmlspecialchars($error_details['message']); ?>
            </p>

            <!-- إرشادات المستخدم -->
            <div class="user-action">
                <i class="fas fa-info-circle text-primary me-2"></i>
                <?php echo htmlspecialchars($error_details['user_action']); ?>
            </div>

            <!-- تحذير أمني للأخطاء الحساسة -->
            <?php if (in_array($error_code, [401, 403, 429])): ?>
            <div class="security-notice">
                <i class="fas fa-shield-alt text-danger me-2"></i>
                <strong>تنبيه أمني:</strong> تم تسجيل هذه المحاولة لأغراض الأمان.
            </div>
            <?php endif; ?>

            <!-- عداد إعادة التوجيه -->
            <div class="countdown-container">
                <div class="spinner"></div>
                <p class="mb-2">
                    <i class="fas fa-clock text-primary me-2"></i>
                    سيتم إعادة توجيهك خلال <span id="countdown" class="fw-bold text-primary">5</span> ثواني
                </p>
                <small class="text-muted">أو انقر على الزر أدناه للانتقال فوراً</small>
            </div>

            <!-- أزرار التنقل -->
            <div class="mt-4">
                <a href="<?php echo htmlspecialchars($redirect_url); ?>" class="btn btn-primary btn-custom">
                    <i class="fas fa-home me-2"></i>الانتقال الآن
                </a>

                <?php if (isset($_SESSION['username'])): ?>
                <a href="/Logpage/logout.php" class="btn btn-outline-secondary btn-custom">
                    <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                </a>
                <?php endif; ?>
            </div>

            <!-- تفاصيل إضافية للمطورين -->
            <?php if (!ErrorConfig::isProduction()): ?>
            <div class="error-details">
                <h6 class="text-muted mb-3">
                    <i class="fas fa-bug me-2"></i>تفاصيل المطور
                </h6>
                <small class="text-muted">
                    <strong>رمز الخطأ:</strong> <?php echo $error_code; ?><br>
                    <strong>المسار:</strong> <?php echo htmlspecialchars($_SERVER['REQUEST_URI'] ?? 'unknown'); ?><br>
                    <strong>الطريقة:</strong> <?php echo htmlspecialchars($_SERVER['REQUEST_METHOD'] ?? 'unknown'); ?><br>
                    <strong>الوقت:</strong> <?php echo date('Y-m-d H:i:s'); ?><br>
                    <strong>المستخدم:</strong> <?php echo htmlspecialchars($_SESSION['username'] ?? 'غير مسجل'); ?>
                </small>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- JavaScript للعداد التنازلي -->
    <script>
        let countdown = 5;
        const countdownElement = document.getElementById('countdown');
        const redirectUrl = <?php echo json_encode($redirect_url); ?>;

        function updateCountdown() {
            countdownElement.textContent = countdown;

            if (countdown <= 0) {
                window.location.href = redirectUrl;
                return;
            }

            countdown--;
            setTimeout(updateCountdown, 1000);
        }

        // بدء العداد التنازلي
        updateCountdown();

        // إضافة مستمع للنقر على أي مكان للانتقال فوراً
        document.addEventListener('click', function(e) {
            if (e.target.tagName !== 'A') {
                window.location.href = redirectUrl;
            }
        });

        // إضافة مستمع للضغط على أي مفتاح
        document.addEventListener('keypress', function() {
            window.location.href = redirectUrl;
        });
    </script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
