<?php
/**
 * Simple Clean Test - Direct test without FieldManager
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

// Check session and access
if (!isset($_SESSION['username']) || !isset($_SESSION['random_code'])) {
    header("Location: ../../../../Loginpage.php");
    exit();
}

try {
    include $_SERVER['DOCUMENT_ROOT'].'/config/Databasehost.php';
    include '../system/database_config.php';
} catch (Exception $e) {
    die("Configuration error: " . $e->getMessage());
}

$access = $_SESSION['access_level'];
$email = $_SESSION['username'];

// Simple functions
function loadFieldConfigurationSimple() {
    global $conn;
    
    $configFile = __DIR__ . '/field_config.json';
    
    // Try file first
    if (file_exists($configFile) && is_readable($configFile)) {
        $content = file_get_contents($configFile);
        $config = json_decode($content, true);
        if (json_last_error() === JSON_ERROR_NONE && !empty($config)) {
            return $config;
        }
    }
    
    // Try database fallback
    try {
        $stmt = $conn->prepare("SELECT config_data FROM field_config_storage WHERE config_key = ?");
        $key = "main_config";
        $stmt->bind_param("s", $key);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($row = $result->fetch_assoc()) {
            $config = json_decode($row['config_data'], true);
            if (!empty($config)) {
                return $config;
            }
        }
    } catch (Exception $e) {
        // Ignore database errors
    }
    
    return [];
}

function saveFieldConfigurationSimple($config) {
    global $conn;
    
    $configJson = json_encode($config, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    
    // Try to save to file first
    $configFile = __DIR__ . '/field_config.json';
    if (is_writable(dirname($configFile))) {
        $result = file_put_contents($configFile, $configJson);
        if ($result !== false) {
            return true;
        }
    }
    
    // Fallback to database storage
    try {
        // Create table if not exists
        $conn->query("CREATE TABLE IF NOT EXISTS field_config_storage (
            id INT PRIMARY KEY AUTO_INCREMENT,
            config_key VARCHAR(255) UNIQUE,
            config_data TEXT,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        $stmt = $conn->prepare("INSERT INTO field_config_storage (config_key, config_data) VALUES (?, ?) 
                               ON DUPLICATE KEY UPDATE config_data = VALUES(config_data)");
        $key = "main_config";
        $stmt->bind_param("ss", $key, $configJson);
        
        return $stmt->execute();
    } catch (Exception $e) {
        return false;
    }
}

function getDatabaseColumnsSimple() {
    global $conn;
    
    try {
        $result = $conn->query("DESCRIBE databasehc");
        $columns = [];
        
        if ($result) {
            while ($row = $result->fetch_assoc()) {
                $columns[$row['Field']] = [
                    'type' => $row['Type'],
                    'null' => $row['Null'],
                    'key' => $row['Key'],
                    'default' => $row['Default'],
                    'extra' => $row['Extra']
                ];
            }
        }
        
        return $columns;
    } catch (Exception $e) {
        return [];
    }
}

function getOrphanedFieldsSimple() {
    $config = loadFieldConfigurationSimple();
    $dbColumns = getDatabaseColumnsSimple();
    
    $orphanedFields = [];
    
    foreach ($config as $sectionKey => $section) {
        if (isset($section['fields'])) {
            foreach ($section['fields'] as $fieldName => $fieldConfig) {
                if (!isset($dbColumns[$fieldName])) {
                    $orphanedFields[] = [
                        'section' => $sectionKey,
                        'field' => $fieldName
                    ];
                }
            }
        }
    }
    
    return $orphanedFields;
}

function cleanOrphanedFieldsSimple() {
    $orphanedFields = getOrphanedFieldsSimple();
    $config = loadFieldConfigurationSimple();
    
    $cleaned = 0;
    $cleanedList = [];
    
    foreach ($orphanedFields as $orphan) {
        $section = $orphan['section'];
        $field = $orphan['field'];
        
        if (isset($config[$section]['fields'][$field])) {
            unset($config[$section]['fields'][$field]);
            $cleaned++;
            $cleanedList[] = "{$section}|{$field}";
            
            // Remove section if empty
            if (empty($config[$section]['fields'])) {
                unset($config[$section]);
            }
        }
    }
    
    if ($cleaned > 0) {
        $saveResult = saveFieldConfigurationSimple($config);
        if (!$saveResult) {
            return ['success' => false, 'message' => 'Failed to save configuration', 'cleaned' => 0];
        }
    }
    
    return ['success' => true, 'message' => "Cleaned $cleaned orphaned fields", 'cleaned' => $cleaned, 'list' => $cleanedList];
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Clean Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body { background: #f8f9fa; font-family: 'Segoe UI', sans-serif; }
        .test-section { background: white; padding: 20px; border-radius: 8px; margin: 15px 0; border: 1px solid #dee2e6; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>🧪 Simple Clean Orphaned Fields Test</h1>

        <?php
        // Handle POST action
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && $_POST['action'] === 'clean_orphaned') {
            echo "<div class='test-section'>";
            echo "<h2>🧹 Cleaning Orphaned Fields</h2>";
            
            $beforeCount = count(getOrphanedFieldsSimple());
            echo "<p><strong>Before:</strong> $beforeCount orphaned fields</p>";
            
            $result = cleanOrphanedFieldsSimple();
            
            if ($result['success']) {
                echo "<div class='alert alert-success'>";
                echo "✅ <strong>Success!</strong> " . $result['message'];
                if (!empty($result['list'])) {
                    echo "<br><strong>Cleaned fields:</strong> " . implode(', ', $result['list']);
                }
                echo "</div>";
            } else {
                echo "<div class='alert alert-danger'>";
                echo "❌ <strong>Failed!</strong> " . $result['message'];
                echo "</div>";
            }
            
            $afterCount = count(getOrphanedFieldsSimple());
            echo "<p><strong>After:</strong> $afterCount orphaned fields</p>";
            
            echo "</div>";
        }
        ?>

        <!-- Current State -->
        <div class="test-section">
            <h2>📊 Current State</h2>
            
            <?php
            $config = loadFieldConfigurationSimple();
            $dbColumns = getDatabaseColumnsSimple();
            $orphanedFields = getOrphanedFieldsSimple();
            ?>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <h3 class="text-primary"><?php echo count($config); ?></h3>
                            <p>Configuration Sections</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <h3 class="text-success"><?php echo count($dbColumns); ?></h3>
                            <p>Database Columns</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <h3 class="text-danger"><?php echo count($orphanedFields); ?></h3>
                            <p>Orphaned Fields</p>
                        </div>
                    </div>
                </div>
            </div>

            <?php if (!empty($orphanedFields)): ?>
                <h4 class="mt-3">Orphaned Fields Details:</h4>
                <div class="alert alert-warning">
                    <?php foreach ($orphanedFields as $orphan): ?>
                        <div>• <?php echo $orphan['section']; ?>|<?php echo $orphan['field']; ?></div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- File Status -->
        <div class="test-section">
            <h2>📁 File & Database Status</h2>
            
            <?php
            $configFile = __DIR__ . '/field_config.json';
            ?>
            
            <div class="row">
                <div class="col-md-6">
                    <h4>File Storage:</h4>
                    <ul>
                        <li><strong>Config file exists:</strong> <?php echo file_exists($configFile) ? '✅ Yes' : '❌ No'; ?></li>
                        <?php if (file_exists($configFile)): ?>
                            <li><strong>File readable:</strong> <?php echo is_readable($configFile) ? '✅ Yes' : '❌ No'; ?></li>
                            <li><strong>File writable:</strong> <?php echo is_writable($configFile) ? '✅ Yes' : '❌ No'; ?></li>
                        <?php endif; ?>
                        <li><strong>Directory writable:</strong> <?php echo is_writable(dirname($configFile)) ? '✅ Yes' : '❌ No'; ?></li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h4>Database Storage:</h4>
                    <?php
                    try {
                        $result = $conn->query("SHOW TABLES LIKE 'field_config_storage'");
                        if ($result && $result->num_rows > 0) {
                            echo "<div class='alert alert-success'>✅ Database table exists</div>";
                            
                            $configCheck = $conn->query("SELECT COUNT(*) as count FROM field_config_storage WHERE config_key = 'main_config'");
                            if ($configCheck) {
                                $row = $configCheck->fetch_assoc();
                                echo "<p>Configuration records: " . $row['count'] . "</p>";
                            }
                        } else {
                            echo "<div class='alert alert-warning'>⚠️ Database table will be created</div>";
                        }
                    } catch (Exception $e) {
                        echo "<div class='alert alert-danger'>❌ Database error: " . $e->getMessage() . "</div>";
                    }
                    ?>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="test-section">
            <h2>🎮 Actions</h2>
            
            <?php if (count($orphanedFields) > 0): ?>
                <form method="POST" class="d-inline">
                    <input type="hidden" name="action" value="clean_orphaned">
                    <button type="submit" class="btn btn-danger btn-lg me-3" onclick="return confirm('Are you sure you want to clean <?php echo count($orphanedFields); ?> orphaned fields?')">
                        <i class="fas fa-broom me-2"></i>Clean <?php echo count($orphanedFields); ?> Orphaned Fields
                    </button>
                </form>
            <?php else: ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>No orphaned fields found to clean.
                </div>
            <?php endif; ?>
            
            <a href="field_diagnostics.php" class="btn btn-info btn-lg me-3">
                <i class="fas fa-stethoscope me-2"></i>Field Diagnostics
            </a>
            
            <a href="field_settings.php" class="btn btn-primary btn-lg">
                <i class="fas fa-cogs me-2"></i>Field Settings
            </a>
        </div>
    </div>

    <script>
        console.log('🧪 Simple Clean Test Page Loaded');
        console.log('📊 Orphaned fields: <?php echo count($orphanedFields); ?>');
        console.log('📋 Configuration sections: <?php echo count($config); ?>');
        console.log('🗄️ Database columns: <?php echo count($dbColumns); ?>');
    </script>
</body>
</html>
